<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>人员详情</title>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">

    <link href="${ctx}/plugins/select2/css/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/plugins/select2/css/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>

    <script src="${ctx}/plugins/select2/js/select2.js" type="text/javascript"></script>
    <script src="${ctx}/plugins/select2/js/select2.full.min.js" type="text/javascript"></script>

    <script type="text/javascript">
        $(function(){
            // 业务标签 select2初始化
            // $("#applyType").select2({
            //     placeholder: "请选择",
            //     width: null
            // });
        })

        var layerTop = top.layer;

        function BAChange(){

            var BAstatus = $("#BAstatus").val();
            var BAtreatDate = $("#BAtreatDate").val();
            var isRegister = $("#isRegister").val();

            // BAdata-date  BAdata-isRegister  BAdata-status

            $("[BAdata-status]").hide();

            var BAstatusSelect = "]"
            if (BAstatus != "") {
                BAstatusSelect = "*='"+BAstatus +"']"
            }

            var BAtreatDateSelect = "]"
            if (BAtreatDate != "") {
                BAtreatDateSelect = "*='"+BAtreatDate +"']"
            }

            var isRegisterSelect = "]"
            if (isRegister != "") {
                isRegisterSelect = "*='"+isRegister +"']"
            }

            $("[BAdata-status"+BAstatusSelect +"[BAdata-date" + BAtreatDateSelect + "[BAdata-isRegister" + isRegisterSelect).show()

            if (BAstatus == "" && BAtreatDate == "" && isRegister == "") {
                $("[BAdata-status]").show();
            }

        }

        function PAChange(){

            var PAstatus = $("#PAstatus").val();
            var PAtreatDate = $("#PAtreatDate").val();
            var applyType = $("#applyType").val();

            // BAdata-date  BAdata-isRegister  BAdata-status

            $("[PAdata-status]").hide();

            var PAstatusSelect = "]"
            if (PAstatus != "") {
                PAstatusSelect = "*='"+PAstatus +"']"
            }

            var PAtreatDateSelect = "]"
            if (PAtreatDate != "") {
                PAtreatDateSelect = "*='"+PAtreatDate +"']"
            }

            var applyTypeSelect = "]"
            if (applyType != "") {
                applyTypeSelect = "*='"+applyType +"']"
            }

            $("[PAdata-status"+PAstatusSelect +"[PAdata-date" + PAtreatDateSelect + "[PAdata-apply-type" + applyTypeSelect).show()

            if (PAstatus == "" && PAtreatDate == "" && applyType == "") {
                $("[PAdata-status]").show();
            }

        }

        //查看详情
        function caseDetail(claimCaseId, status) {
            console.log(claimCaseId);
            if (status.indexOf('aax') != -1) {
                console.log("初审查看详情");
                window.location.href="${ctx}/claimCaseController/caseDetail?caseId="+claimCaseId;
            } else {
                console.log("理赔查看详情");
                window.location.href="${ctx}/insuranceCaseController/caseDetail?caseId="+claimCaseId;
            }
        }

        //案件关闭
        function closeCase(caseId) {
            var content = '<div class="row" style="padding: 0px 30px" ><div class="col-sm-3">&nbsp;</div><textarea rows="5" class="col-sm-6 form-control" name="closeCaseMsg" id="closeCaseMsg" autocomplete="off" placeholder="请输入关闭案件原因"></textarea></div>' ;
            layer.open({
                type: 1,
                content: content,
                title: '关闭案件',
                area:  ['500px', '300px'],
                offset: 't',
                btn: ['确认','取消'],
                yes: function(index,obj){
                    console.log(typeof $("#closeCaseMsg").val());
                    var closeCaseMsg = $("#closeCaseMsg").val();
                    if(typeof closeCaseMsg != 'string' || closeCaseMsg.trim()==''){
                        layer.msg("关闭案件原因不能为空", {icon: 2, time: 3000,offset: 'r'});
                    }else {
                        var formData = new FormData();
                        formData.append("claimCaseId", caseId);
                        formData.append("type", 200);
                        formData.append("description", closeCaseMsg);
                        $.ajax({
                            url: "${ctx}/insuranceCaseController/closeCase",
                            type: 'POST',
                            data: formData,
                            async: false,
                            cache: false,
                            contentType: false,
                            processData: false,
                            success: function (data) {
                                var result = eval("(" + data + ")");
                                if (result.ret == "0") {
                                    setTimeout(function(){
                                        layer.msg('关闭案件成功', {
                                            icon: 1,
                                            time: 3000,
                                            shade: [0.3, '#000000']//1秒关闭（如果不配置，默认是3秒）
                                        }, function () {
                                            window.location.reload();
                                        });
                                    },1200);
                                } else {
                                    layer.msg(result.msg, {
                                        icon: 1,
                                        time: 1500
                                    }, function (index) {
                                        layer.close(index);
                                    });
                                }
                            },
                            error: function (data) {
                                var result = eval("(" + data + ")");
                                alert(result.msg);
                            }
                        });
                    }
                }
            });
        }

        //补发短信
        function reSendMessage(claimCaseId) {
            layer.open({
                title: "补发短信",
                type: 1,
                content: $('#reSendMessageContainer'),
                area: ['700px', '300px'],
                fixed: false,
                offset: 't',
                btn: ['确认', '取消'],
                closeBtn: 0,
                yes: function (index, layero) {
                    let check = $('#reSendMessageContainer').find('span.checked');
                    let children = check.children();
                    let code = children.attr("code");
                    if (typeof code == "undefined" || code == null) {
                        layerTop.msg("请选择短信类型！！！", {
                            icon: 2,
                            time: 2000
                        });
                        return;
                    }
                    let reissueReason = $("#reissueReason").val();
                    if (reissueReason.trim() == "") {
                        layer.msg("请输入补发原因！！！", {
                            icon: 2,
                            time: 2000
                        });
                        return;
                    }
                    var formData = new FormData();
                    formData.append("claimCaseId", claimCaseId);
                    formData.append("messageCode", code);
                    formData.append("reissueReason", reissueReason);
                    $.ajax({
                        url: "${ctx}/personnelManagementController/reSendMessage",
                        type: 'POST',
                        data: formData,
                        async: false,
                        cache: false,
                        contentType: false,
                        processData: false,
                        success: function (data) {
                            var result = eval("(" + data + ")");
                            if (result.ret == "0") {
                                setTimeout(function(){
                                    layer.msg('发送成功', {
                                        icon: 1,
                                        time: 3000,
                                        shade: [0.3, '#000000']//1秒关闭（如果不配置，默认是3秒）
                                    }, function () {
                                        window.location.reload();
                                    });
                                },1200);
                            } else {
                                layer.msg(result.msg, {
                                    icon: 1,
                                    time: 1500
                                }, function (index) {
                                    layer.close(index);
                                });
                            }
                        },
                        error: function (data) {
                            var result = eval("(" + data + ")");
                            alert(result.msg);
                        }
                    });
                },
                btn2: function (index, layero) {
                    layer.closeAll();
                }
            });
        }

        //补充材料
        function supplementaryMaterials(id) {
            layer.open({
                title: "补充材料",
                type: 1,
                content: $('#supplementaryMaterialsContainer'),
                area: ['600px', '300px'],
                fixed: false,
                offset: 't',
                btn: ['确认', '取消'],
                closeBtn: 0,
                yes: function (index, layero) {
                    let check = $('#supplementaryMaterialsContainer').find('span.checked');
                    let sendData = "";
                    $.each(check, function (index, obj) {
                        let code = $(this).children().attr("code");
                        sendData += "," + code;
                    });
                    if (sendData == "" || sendData == null) {
                        layerTop.msg("请选择补充材料类型！！！", {
                            icon: 2,
                            time: 2000
                        });
                        return;
                    }
                    let supplementaryReason = $("#supplementaryReason").val();
                    if (supplementaryReason.trim() == "" || supplementaryReason == null) {
                        layerTop.msg("请输入补材原因！！！", {
                            icon: 2,
                            time: 2000
                        });
                        return;
                    }
                    let claimCaseId = id;
                    var formData = new FormData();
                    formData.append("claimCaseId", claimCaseId);
                    formData.append("hangCode", JSON.stringify(sendData))
                    formData.append("hangReason", supplementaryReason)
                    $.ajax({
                        url: "${ctx}/personnelManagementController/supplementaryMaterials",
                        type: 'POST',
                        data: formData,
                        async: false,
                        cache: false,
                        contentType: false,
                        processData: false,
                        success: function (data) {
                            var result = eval("(" + data + ")");
                            if (result.ret == "0") {
                                setTimeout(function(){
                                    layer.msg('发起补材成功', {
                                        icon: 1,
                                        time: 3000,
                                        shade: [0.3, '#000000']//1秒关闭（如果不配置，默认是3秒）
                                    }, function () {
                                        window.location.reload();
                                    });
                                },1200);
                            } else {
                                layer.msg(result.msg, {
                                    icon: 1,
                                    time: 1500
                                }, function (index) {
                                    layer.close(index);
                                });
                            }
                        },
                        error: function (data) {
                            var result = eval("(" + data + ")");
                            alert(result.msg);
                        }
                    });
                },
                btn2: function (index, layero) {
                    layer.closeAll();
                }
            });

            let logText = $("#logTextArea").val();
            if(logText != undefined && logText.trim()!=""){
                addCaseLog("${claimCase.id}");
            }
        }

    </script>

    <style>
        body {
            background: #fff;
        }
        .clear-padding {
            padding: 0px !important;
        }
        .clear-margin {
            margin: 0px !important;
        }
        .person-content > div > h4 {
            font-weight: bold;
            font-size: 17px;
            background: #F0F0F0;
            border-bottom: 1px solid #e7ecf1;
            line-height: 21px;
            margin: 0px;
            padding: 10px;
        }
        .person-content > .row {
            margin: 25px 0px !important;
            border: 1px solid #e7ecf1;
        }
        .content-div {
            margin: 13px 0px;
        }
        .content-div > div {
            font-size: 16px !important;
        }
        .table-title {
            font-size: 17px;
            font-weight: bold;
            margin: 15px 5px;
        }
        th {
            text-align: center;
        }

        #baTable thead{
            display:table;
            width:100%;
            table-layout:fixed;
        }

        #baTable tbody{
            display:block;
            width:100%;
            height:120px;
            overflow-y:scroll;
            -webkit-overflow-scrolling: touch; // 为了滚动顺畅
        }

        #baBody tr {
            display:table;
            width:100%;
            table-layout:fixed;
        }

        #paTable thead{
            display:table;
            width:100%;
            table-layout:fixed;
        }

        #paTable tbody{
            display:block;
            width:100%;
            height:120px;
            overflow-y:scroll;
            -webkit-overflow-scrolling: touch; // 为了滚动顺畅
        }

        #paBody tr {
            display:table;
            width:100%;
            table-layout:fixed;
        }

    </style>
</head>
<body>

    <#--补发短信-->
    <div id="reSendMessageContainer" style="display: none;width: 100%;height: 100%">
        <div class="row" style="padding-top: 30px;margin: 0px 0px">
            <div class="col-sm-3">
                <span class="pull-right" style="font-size: 10px;color: #7f7f7f">选择短信类型：</span>
            </div>
            <div class="col-sm-9">
                <#if messageType?? && (messageType.keySet()?size>0)>
                    <#list messageType.keySet() as key>
                        <div class="col-sm-3 clear-padding" style="margin-bottom: 10px;">
                            <input name="messageType" style="left: 0px;right: 0px;margin-left: 0px;" code="${key}"
                                   type="radio">${messageType.get(key)}
                        </div>
                    </#list>
                </#if>
                <textarea class="form-control" id="reissueReason" rows="5" placeholder="请输入补发原因"></textarea>
            </div>
        </div>
    </div>

    <#--补充材料-->
    <div id="supplementaryMaterialsContainer" style="display: none;width: 100%;height: 100%">
        <div class="row" style="padding-top: 30px;margin: 0px 0px">
            <div class="col-sm-3">
                <span class="pull-right" style="font-size: 10px;color: #7f7f7f">选择补充材料类型：</span>
            </div>
            <div class="col-sm-9">
                <#if supplementaryMaterials?? && (supplementaryMaterials.keySet()?size>0)>
                    <#list supplementaryMaterials.keySet() as key>
                        <div class="col-sm-3" style="padding-left: 0px;margin-bottom: 10px;display: flex">
                            <input name="labelType" style="left: 0px;right: 0px;margin-left: 0px;" code="${key}"
                                   type="checkbox">${supplementaryMaterials.get(key)}
                        </div>
                    </#list>
                    <textarea class="form-control" id="supplementaryReason" rows="5" placeholder="请输入补材原因"></textarea>
                </#if>
            </div>
        </div>
    </div>

    <div class="row" >
        <div class="col-sm-12 person-content" style="padding: 20px 30px;padding-left: 50px;">
            <div class="row" style="margin-top: 0px !important;">
                <h4 class="col-sm-12">人员信息</h4>
                <div class="col-sm-12" style="padding: 15px 30px;">
                    <div class="row content-div">
                        <div class="col-sm-4">
                            <div class="col-sm-5 text-right clear-padding">姓名：</div>
                            <div class="col-sm-6 clear-padding"> ${personInfo.treatName!'-'}</div>
                        </div>
                        <div class="col-sm-4">
                            <div class="col-sm-5 text-right clear-padding">身份证：</div>
                            <div class="col-sm-6 clear-padding">${personInfo.treatIdNum!'-'}</div>
                        </div>
                        <div class="col-sm-4">
                            <div class="col-sm-5 text-right clear-padding">联系电话：</div>
                            <div class="col-sm-6 clear-padding">${personInfo.treatMobile!'-'}</div>
                        </div>
                    </div>
                    <div class="row content-div">
                        <div class="col-sm-4">
                            <div class="col-sm-5 text-right clear-padding">车辆信息：</div>
                            <div class="col-sm-6 clear-padding">${personInfo.carBrand!'-'}</div>
                        </div>
                        <div class="col-sm-4">
                            <div class="col-sm-5 text-right clear-padding">车辆车牌号：</div>
                            <div class="col-sm-6 clear-padding">${personInfo.plateNumber!'-'}</div>
                        </div>
                        <div class="col-sm-4">
                            <div class="col-sm-5 text-right clear-padding">联系地址：</div>
                            <div class="col-sm-6 clear-padding">${personInfo.address!'-'}</div>
                        </div>
                    </div>
                    <div class="row content-div">
                        <div class="col-sm-4">
                            <div class="col-sm-5 text-right clear-padding">子商ID：</div>
                            <div class="col-sm-6 clear-padding">${pdsEmployeeInfoVo.subsidiaryAgentId!'-'}</div>
                        </div>
                        <div class="col-sm-4">
                            <div class="col-sm-5 text-right clear-padding">骑手ID：</div>
                            <div class="col-sm-6 clear-padding">${pdsEmployeeInfoVo.customerNumber!'-'}</div>
                        </div>
                        <div class="col-sm-4">
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <h4 class="col-sm-12">投保频率</h4>
                <div class="col-sm-12" style="padding: 15px 30px;">
                    <div class="row content-div">
                        <div class="col-sm-4">
                            <div class="col-sm-5 text-right clear-padding">总投保次数：</div>
                            <div class="col-sm-6 clear-padding">${personFrequencyInfo.times!'-'}</div>
                        </div>
                        <div class="col-sm-4">
                            <div class="col-sm-5 text-right clear-padding">总投保频率：</div>
                            <div class="col-sm-6 clear-padding">${personFrequencyInfo.totalFrequency!'-'}</div>
                        </div>
                        <div class="col-sm-4">
                            <div class="col-sm-5 text-right clear-padding">最早投保时间：</div>
                            <div class="col-sm-6 clear-padding">${(personFrequencyInfo.firstPolicyPersonDate?string('yyyy-MM-dd'))!'-'}</div>
                        </div>
                    </div>
                    <div class="row content-div">
                        <div class="col-sm-4">
                            <div class="col-sm-5 text-right clear-padding">最近连续投保天数：</div>
                            <div class="col-sm-6 clear-padding">${personFrequencyInfo.continuousTimes!'-'}</div>
                        </div>
                        <div class="col-sm-4">
                            <div class="col-sm-5 text-right clear-padding">近30天投保天数：</div>
                            <div class="col-sm-6 clear-padding">${personFrequencyInfo.near30PolicyPersonTimes!'-'}天</div>
                        </div>
                        <div class="col-sm-4">
                            <div class="col-sm-5 text-right clear-padding">最近一次投保时间：</div>
                            <div class="col-sm-6 clear-padding">${(personFrequencyInfo.lastPolicyPersonDate?string('yyyy-MM-dd'))!'-'}</div>
                        </div>
                    </div>
                    <div class="row content-div">
                        <div class="col-sm-4">
                            <div class="col-sm-5 text-right clear-padding">最近30天投率：</div>
                            <div class="col-sm-6 clear-padding">${personFrequencyInfo.near30PolicyPersonFrequency!'-'}</div>
                        </div>
                        <div class="col-sm-4">
                            <div class="col-sm-5 text-right clear-padding">最新保单号：</div>
                            <div class="col-sm-6 clear-padding">${personFrequencyInfo.nearPolicyNo!'-'}</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <h4 class="col-sm-12">报案情况</h4>
                <div class="col-sm-12" style="padding: 15px 30px;border-bottom: 1px solid #e7ecf1;">
                    <div class="row content-div">
                        <div class="col-sm-3">
                            <div class="col-sm-6 text-right clear-padding">累计报案数：</div>
                            <div class="col-sm-6 clear-padding">${BAclaimCaseFrequencyInfo.times!'-'}次</div>
                        </div>
                        <div class="col-sm-3">
                            <div class="col-sm-6 text-right clear-padding">进行中报案：</div>
                            <div class="col-sm-6 clear-padding">${BAclaimCaseFrequencyInfo.processTimes!'-'}件</div>
                        </div>
                        <div class="col-sm-3">
                            <div class="col-sm-6 text-right clear-padding">有效报案数：</div>
                            <div class="col-sm-6 clear-padding">${BAclaimCaseFrequencyInfo.effctiveTimes!'-'}件</div>
                        </div>
                        <div class="col-sm-3">
                            <div class="col-sm-6 text-right clear-padding">注销/关闭：</div>
                            <div class="col-sm-6 clear-padding">${BAclaimCaseFrequencyInfo.deleteTimes!'-'}件/${BAclaimCaseFrequencyInfo.closeTimes!'-'}件</div>
                        </div>
                    </div>
                    <div class="row content-div">
                        <div class="col-sm-3">
                            <div class="col-sm-6 text-right clear-padding">累计未决估损金额：</div>
                            <div class="col-sm-6 clear-padding" style="color: red">${BAclaimCaseFrequencyInfo.processTotalAmount!'-'}</div>
                        </div>
                        <div class="col-sm-3">
                            <div class="col-sm-6 text-right clear-padding">最早报案时间：</div>
                            <div class="col-sm-6 clear-padding">${(BAclaimCaseFrequencyInfo.firstCaseDate?string('yyyy-MM-dd'))!'-'}</div>
                        </div>
                        <div class="col-sm-3">
                            <div class="col-sm-6 text-right clear-padding">最近一次报案时间：</div>
                            <div class="col-sm-6 clear-padding">${(BAclaimCaseFrequencyInfo.nearCaseDate?string('yyyy-MM-dd'))!'-'}</div>
                        </div>
                        <div class="col-sm-3">
                            <div class="col-sm-6 text-right clear-padding">平均报案时效：</div>
                            <div class="col-sm-6 clear-padding">${BAclaimCaseFrequencyInfo.averageHour!'-'}小时</div>
                        </div>
                    </div>
                </div>
                <div class="col-sm-12" style="padding: 15px 30px;border-bottom: 1px solid #e7ecf1;">
                    <h4 class="col-sm-12 table-title">报案信息</h4>
                    <form class="col-sm-12" action="" id="" >
                        <div class="row" style="padding-right: 100px">
                            <div class="col-sm-3 form-group clear-padding">
                                <label class="control-label col-sm-5 clear-padding text-right" style="line-height: 34px">状态：</label>
                                <div class="col-sm-7" style="padding-left: 0;">
                                    <select class="form-control" name="BAstatus" id="BAstatus" onchange="BAChange()">
                                        <option value="">-请选择-</option>
                                        <#if statusMap?? && (statusMap.keySet()?size>0) >
                                            <#list statusMap.keySet() as key>
                                                <option value="${key}" >${statusMap.get(key)}</option>
                                            </#list>
                                        </#if>
                                    </select>
                                </div>
                            </div>
                            <div class="col-sm-3 form-group clear-padding">
                                <label class="control-label col-sm-5 clear-padding text-right" style="line-height: 34px">出险时间：</label>
                                <div class="col-sm-7" style="padding-left: 0;">
                                    <input type="text" class="form-control form-control-inline date-picker" autocomplete="off" data-date-format="yyyy-mm-dd" onchange="BAChange()" name="BAtreatDate" id="BAtreatDate">
                                </div>
                            </div>
                            <div class="col-sm-3 form-group clear-padding">
                                <label class="control-label col-sm-5 clear-padding text-right" style="line-height: 34px">是否立案：</label>
                                <div class="col-sm-7" style="padding-left: 0;">
                                    <select class="form-control" name="isRegister" id="isRegister" onchange="BAChange()">
                                        <option value="">请选择</option>
                                        <option value="1">是</option>
                                        <option value="0">否</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </form>
                    <div class="col-sm-12 clear-padding" style="border: 1px solid #e7ecf1;">
                        <table id = "baTable" class="table table-striped table-hover table-striped table-header-fixed text-center clear-margin" style="border-bottom: 1px solid #e7ecf1;" >
                            <thead>
                            <tr>
                                <th width="">报案号</th>
                                <th width="">出险人</th>
                                <th width="">报案人</th>
                                <th width="">是否立案</th>
                                <th width="">估损金额</th>
<#--                                <th width="">代理商/人资商</th>-->
                                <th width="">产品名称</th>
                                <th width="">状态</th>
                                <th width="">报案时间</th>
                                <th width="">出险时间</th>
                                <th width="">功能</th>
                            </tr>
                            </thead>

                            <tbody id = "baBody">

                            <#list BAclaimCaseList as vo>
                                <tr BAdata-date = "${(vo.treatDate?string('yyyy-MM-dd'))!'-'}" BAdata-isRegister = "${vo.isRegister!'-'}" BAdata-status = "${vo.status!'-'}">
                                    <td title="">${vo.claimCaseNo!'-'}</td>
                                    <td title="">${vo.treatName!'-'}</td>
                                    <td title="">${vo.applyName!'-'}</td>
                                    <td title=""><#if vo.isRegister == 0>否 <#else> 是 </#if></td>
                                    <td title="">${vo.appraisalAmount!'-'}</td>
                                    <td title="">${vo.productName!'-'}</td>
                                    <td title="">${vo.statusStr!'-'}</td>
                                    <td title="">${(vo.startDate?string('yyyy-MM-dd'))!'-'}</td>
                                    <td title="">${(vo.treatDate?string('yyyy-MM-dd'))!'-'}</td>
                                    <#--功能-->
                                    <td>
                                        <#if !vo.status?contains("-1") && !vo.status?contains("aex")>
                                            <#--<a onclick="reSendMessage('${vo.id}')" >补发短信</a>-->
                                            <@shiro.hasPermission name="PERSON_LIST_CLOSE_CASE">
                                                <a onclick="closeCase('${vo.id}')">关闭案件</a>
                                            </@shiro.hasPermission>
                                            <br>
                                        </#if>
                                        <#if vo.status == "abx10">
                                            <a onclick="supplementaryMaterials('${vo.id}')">补充材料</a>
                                        </#if>
                                        <a onclick="caseDetail('${vo.id}','${vo.status}')">查看详情</a>
                                    </td>
                                </tr>
                            </#list>

                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="row">
                <h4 class="col-sm-12">赔案情况</h4>
                <div class="col-sm-12" style="padding: 15px 30px;border-bottom: 1px solid #e7ecf1;">
                    <div class="row content-div">
                        <div class="col-sm-3">
                            <div class="col-sm-6 text-right clear-padding">累计赔案数：</div>
                            <div class="col-sm-6 clear-padding">${PAclaimCaseFrequencyInfo.times!'-'}件</div>
                        </div>
                        <div class="col-sm-3">
                            <div class="col-sm-6 text-right clear-padding">处理中案件数：</div>
                            <div class="col-sm-6 clear-padding">${PAclaimCaseFrequencyInfo.processTimes!'-'}件</div>
                        </div>
                        <div class="col-sm-3">
                            <div class="col-sm-6 text-right clear-padding">未决案件数量：</div>
                            <div class="col-sm-6 clear-padding">${PAclaimCaseFrequencyInfo.hangTimes!'-'}件</div>
                        </div>
                        <div class="col-sm-3">
                            <div class="col-sm-6 text-right clear-padding">累计估损金额：</div>
                            <div class="col-sm-6 clear-padding">${PAclaimCaseFrequencyInfo.totalAmount!'-'}元</div>
                        </div>
                    </div>
                    <div class="row content-div">
                        <div class="col-sm-3">
                            <div class="col-sm-6 text-right clear-padding">已决赔案数：</div>
                            <div class="col-sm-6 clear-padding" style="color: red">${PAclaimCaseFrequencyInfo.finishTimes!'-'}件</div>
                        </div>
                        <div class="col-sm-3">
                            <div class="col-sm-6 text-right clear-padding">发起调查件数：</div>
                            <div class="col-sm-6 clear-padding">${PAclaimCaseFrequencyInfo.prospectTimes!'-'}件</div>
                        </div>
                        <div class="col-sm-3">
                            <div class="col-sm-6 text-right clear-padding">三者人伤件数：</div>
                            <div class="col-sm-6 clear-padding">${PAclaimCaseFrequencyInfo.thirdPersonTimes!'-'}件</div>
                        </div>
                        <div class="col-sm-3">
                            <div class="col-sm-6 text-right clear-padding">物损件数：</div>
                            <div class="col-sm-6 clear-padding">${PAclaimCaseFrequencyInfo.materialTimes!'-'}件</div>
                        </div>
                    </div>
                    <div class="row content-div">
                        <div class="col-sm-3">
                            <div class="col-sm-6 text-right clear-padding">已决赔款金额：</div>
                            <div class="col-sm-6 clear-padding">${PAclaimCaseFrequencyInfo.finishPaymentAmount!'-'}元</div>
                        </div>
                        <div class="col-sm-3">
                            <div class="col-sm-6 text-right clear-padding">平均结案时效：</div>
                            <div class="col-sm-6 clear-padding">${PAclaimCaseFrequencyInfo.averageHour!'-'}小时</div>
                        </div>
                    </div>
                </div>
                <div class="col-sm-12" style="padding: 15px 30px;border-bottom: 1px solid #e7ecf1;">
                    <h4 class="col-sm-12 table-title">赔案信息</h4>
                    <form class="col-sm-12" action="" id="" >
                        <div class="row" style="padding-right: 100px">
                            <div class="col-sm-3 form-group clear-padding">
                                <label class="control-label col-sm-5 clear-padding text-right" style="line-height: 34px">状态：</label>
                                <div class="col-sm-7" style="padding-left: 0;">
                                    <select class="form-control" name="PAstatus" id="PAstatus" onchange="PAChange()">
                                        <option value="">-请选择-</option>
                                        <#if statusMap?? && (statusMap.keySet()?size>0) >
                                            <#list statusMap.keySet() as key>
                                                <option value="${key}" >${statusMap.get(key)}</option>
                                            </#list>
                                        </#if>
                                    </select>
                                </div>
                            </div>
                            <div class="col-sm-3 form-group clear-padding">
                                <label class="control-label col-sm-5 clear-padding text-right" style="line-height: 34px">出险类型：</label>
                                <div class="col-sm-7" style="padding-left: 0;">
                                    <select class="form-control" name="applyType" id="applyType" onchange="PAChange()">
                                        <option value="">请选择</option>
                                        <option value="AA" >人伤</option>
                                        <option value="AB" >三者人伤</option>
                                        <option value="AC" >物损</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-sm-3 form-group clear-padding">
                                <label class="control-label col-sm-5 clear-padding text-right" style="line-height: 34px">出险时间：</label>
                                <div class="col-sm-7" style="padding-left: 0;">
                                    <input type="text" class="form-control form-control-inline date-picker" autocomplete="off" data-date-format="yyyy-mm-dd" name="PAtreatDate" id="PAtreatDate" onchange="PAChange()">
                                </div>
                            </div>
                        </div>
                    </form>
                    <div class="col-sm-12 clear-padding" style="border: 1px solid #e7ecf1;">
                        <table id = "paTable" class="table table-striped table-hover table-striped table-header-fixed text-center clear-margin" style="border-bottom: 1px solid #e7ecf1;" >
                            <thead>
                            <tr>
                                <th width="">报案号</th>
                                <th width="">出险人</th>
                                <th width="">出险类型</th>
                                <th width="">估损金额</th>
                                <th width="">产品名称</th>
                                <th width="">是否赔付</th>
                                <th width="">赔付金额</th>
                                <th width="">出险时间</th>
                                <th width="">结案时间</th>
                                <th width="">状态</th>
                                <th width="">功能</th>
                            </tr>
                            </thead>
                            <tbody id = "paBody">
                            <#list PAclaimCaseList as vo>
                                <tr PAdata-date = "${(vo.treatDate?string('yyyy-MM-dd'))!'-'}" PAdata-apply-type = "${vo.applyType!'-'}" PAdata-status = "${vo.status!'-'}">
                                    <td title="">${vo.claimCaseNo!'-'}</td>
                                    <td title="">${vo.treatName!'-'}</td>
                                    <td title="">${vo.applyTypeStr!'-'}</td>
                                    <td title="">${vo.appraisalAmount!'-'}</td>
                                    <td title="">${vo.productName!'-'}</td>
                                    <td title=""><#if vo.is_refuse == 0>是 <#else> 否 </#if></td>
                                    <td title="">${vo.payAmount!'-'}</td>
                                    <td title="">${(vo.treatDate?string('yyyy-MM-dd'))!'-'}</td>
                                    <td title="">${(vo.endDate?string('yyyy-MM-dd'))!'-'}</td>
                                    <td title="">${vo.statusStr!'-'}</td>
                                    <#--功能-->
                                    <td>
                                        <#if !vo.status?contains("-1") && !vo.status?contains("aex")>
                                           <#-- <a  onclick="reSendMessage('${vo.id}')" >补发短信</a>-->
                                            <@shiro.hasPermission name="PERSON_LIST_CLOSE_CASE">
                                                <a onclick="closeCase('${vo.id}')">关闭案件</a>
                                            </@shiro.hasPermission>
                                            <br>
                                        </#if>
                                         <#if vo.status == "abx10">
                                             <a onclick="supplementaryMaterials('${vo.id}')">补充材料</a>
                                         </#if>
                                        <a onclick="caseDetail('${vo.id}','${vo.status}')">查看详情</a>
                                    </td>
                                </tr>
                            </#list>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>