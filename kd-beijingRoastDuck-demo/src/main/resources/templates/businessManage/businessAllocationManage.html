<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!-- 上述3个meta标签*必须*放在最前面，任何其他内容都*必须*跟随其后！ -->
    <meta name="description" content="">
    <meta name="author" content="">
    <title>亚太操作页面</title>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">

    <link href="${ctx}/metronic/global/plugins/select2/css/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/select2/css/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/static/css/labelColor.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/plugins/loading/jquery.mloading.css" rel="stylesheet" type="text/css"/>

    <script src="${ctx}/plugins/loading/jquery.mloading.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/plugins/select2/js/select2.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/plugins/select2/js/select2.full.min.js" type="text/javascript"></script>
    <style>
        table {
            table-layout: fixed;
        }

        table td {
            word-break: break-all;
            white-space: normal;
            text-align: center;
        }

        table th {
            word-break: break-all;
            white-space: normal;
            text-align: center;
        }



        .headLine {
            display: -webkit-flex; /* Safari */
            display: flex;
            flex-direction: row;
        }

        .table-scroll {
            overflow: auto;
            max-height: 370px;
        }

        .table-scroll::-webkit-scrollbar {
            width: 1px;
        }

        .select-box {
            margin: 3px 15px;
            width: 56%;
        }

        .time-box {
            width: 56%;
            margin: 3px 10px;
        }

        .btn-box {
            margin: 3px 10px;
            background: #1676ff;
        }

        label {
            word-break: keep-all;
            margin: 9px 6px;
        }

        .content-box {
            display: -webkit-flex; /* Safari */
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
            justify-content: space-between;
        }

        .element-box {
            width: 384px;
            height: auto;
            margin: 30px 10px;
            background: rgba(22, 118, 255, 0.05);
        }

        .element-head {
            padding-top: 9px;
        }

        .word-mt-len {
            color: #1676FF;
            margin-top: 4px;
            font-size: 30px;
            font-weight: bold;
        }

        .word-sigle {
            margin-top: 7px;
            font-size: large;
        }

    </style>
    <script type="text/javascript">
        function businessDataExport() {
            alert("下发成功！");
            $.post("${ctx}/businessAllocationManageController/businessDataExport", {}, function (data) {
                let res = JSON.parse(data);
                if (res.ret == 0) {
                    layer.msg('等待接收邮件', {icon: 6, time: 10000});
                } else {
                    alert(res.msg);
                }
            });
        }

        function businessDataExportNew() {
            alert("下发成功！");
            $.post("${ctx}/businessAllocationManageController/businessDataExportV2", {}, function (data) {
                let res = JSON.parse(data);
                if (res.ret == 0) {
                    layer.msg('等待接收邮件', {icon: 6, time: 10000});
                } else {
                    alert(res.msg);
                }
            });
        }

        function businessDataExportForStartDate() {
            alert("下发成功！");
            $.post("${ctx}/businessAllocationManageController/businessDataExportForStartDate", {}, function (data) {
                let res = JSON.parse(data);
                if (res.ret == 0) {
                    layer.msg('等待接收邮件', {icon: 6, time: 10000});
                } else {
                    alert(res.msg);
                }
            });
        }

    </script>
</head>
<body id="qc-Body">
<div class="row">
    <div class="col-sm-12">
        <div class="portlet light portlet-fit bordered">
            <div class="portlet-title">
                <ul class="page-breadcrumb breadcrumb">
                    <li><a href="#">管理功能</a> <i class="fa fa-circle"></i></li>
                    <li><span class="active">业务分配管理</span></li>
                </ul>
            </div>
            <div class="portlet-body">
                <!-- BEGIN FORM-->
                <form id="searchForm" class="form-horizontal" onsubmit="return false">
                    <div class="content-box">
                        <#--<div class="element-box">
                            <div class="element-head">
                                <div class="text-center word-mt-len">
                                    <input type="button" class="btn blue" style="margin-left: 20px;border-radius: 5px!important;" value="承保数据清单导出" onclick="businessDataExport()"/>
                                </div>
                            </div>
                        </div>-->
                        <div class="element-box">
                            <div class="element-head">
                                <div class="text-center word-mt-len">
                                    <input type="button" class="btn blue" style="margin-left: 20px;border-radius: 5px!important;" value="承保数据导出清单-收到时间" onclick="businessDataExportNew()"/>
                                </div>
                            </div>
                        </div>
                        <div class="element-box">
                            <div class="element-head">
                                <div class="text-center word-mt-len">
                                    <input type="button" class="btn blue" style="margin-left: 20px;border-radius: 5px!important;" value="承保数据导出清单-承保时间" onclick="businessDataExportForStartDate()"/>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
                <div class="modal fade" id="saveBtnModal" tabindex="-1" role="basic" aria-hidden="true">
                    <div class="modal-dialog">
                        <img src="${ctx}/images/load.gif">
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>
