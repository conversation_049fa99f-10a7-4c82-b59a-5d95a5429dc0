<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!-- 上述3个meta标签*必须*放在最前面，任何其他内容都*必须*跟随其后！ -->
    <meta name="description" content="">
    <meta name="author" content="">
    <title>报案审核</title>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <script src="${ctx}/js/genProCity.js" type="text/javascript"></script>
    <script src="${ctx}/js/genVBrand.js" type="text/javascript"></script>

    <link href="${ctx}/plugins/select2/css/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/plugins/select2/css/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>

    <script src="${ctx}/plugins/select2/js/select2.js" type="text/javascript"></script>
    <script src="${ctx}/plugins/select2/js/select2.full.min.js" type="text/javascript"></script>

    <script type="text/javascript">

        const loader =  new Loaders({style:"rectangle"});

        function iframeH() {
            var ifm = parent.document.getElementById("contentFrame");
            if (ifm) {
                var ifmH = $(ifm).height()
                var h = $(document.body).height() + 120;
                if (ifmH < h) {
                    parent.document.getElementById("contentFrame").height = h;
                }
            }
        }

        const proCityPickerData = new GenProCity().getValue();
        const proBrandPickerData = new GenVBrand().getValue();
        var applyTypeList = [];
        var subjectList = [];
       /* var subjectList = JSON.parse('${subjectList}');*/
        var applyTypeExist = "${claimCase.applyType}";

        var scrollTop;  // 定义滚动高度

        var pageUuid;

        $(function () {
            $("#policyPersonId").select2({
                placeholder: "请选择",
                width: null
            });
            $("#carBrand").select2({
                placeholder: "请选择",
                width: null
            });

            $(".content-title").on("click", "input[type='checkbox']", function (e) {
                var val = $(this).is(":checked");
                if (val) {
                    $(".rowInfo").each(function(){
                        $(this).show();
                    });
                } else {
                    $(".detailsInfo").hide();
                    $(".rowInfo").each(function(){
                        if ($(this).find("td:eq(3)").text().trim() == "系统") {
                            $(this).hide();
                        }
                    });
                }
                iframeH();
            });

             //初始化出险类型
            $(".blue").parent().prev().children("img").attr("src","${ctx}/static/img/checkBox_1.png");
            var applyType = "${claimCase.applyType}";
            applyType.split(",").forEach(function (item, index) {
                if (item) {
                    applyTypeList.push(item);
                }
            });
            <#assign index =0>
            <#list subjectList as subject >
                var subjectList_${index}  = {};
                <#list subject?keys as key>
                    <#if key.indexOf("set")== 0  >
                    <#assign  key_1 = key?substring(3)?uncap_first>
                    subjectList_${index}['${key_1}'] = '${subject[key_1]}'
                    </#if>
                </#list>
                subjectList.push(subjectList_${index});
                <#assign index = index +1 >
            </#list>

            var error = '${errorMsg}';
            if (typeof error != 'undefined' && error.trim() != "") {
                scrollTop = calculationScrollTop();
                layer.msg(error, {icon: 16, time: 2000, offset: scrollTop, shade: [0.5, '#000'], skin: 'error-class'}, function (index) {
                    layer.close(index);
                    window.location.href = "${ctx}/claimCaseController/claimCaseList";
                });
            } else {
                //初始化赔付信息
                freshDutyInfo();
            }

            //出险类型切换
            $(".apply-type").on("click", "button", function(e){
                var bgCheck = $(this).attr("data-check");
                if (bgCheck == "0") {
                    $(this).attr("data-check","1");
                    applyTypeList.push($(this).attr("value"));
                    $(this).addClass("blue");
                    $(this).closest("dl").find("img").attr("src","${ctx}/static/img/checkBox_1.png");
                    $.ajax({
                        url: "${ctx}/claimCaseController/applyTypeModifyShowAppraisalAmount?applyType=" + $(this).attr("value"),
                        type: 'POST',
                        async: false,
                        cache: false,
                        contentType: false,
                        processData: false,
                        success: function (data) {
                            var result = eval("(" + data + ")");
                            if (result.ret == "0") {
                                result.data.forEach(function (item, index) {
                                    let existBool = true;
                                    for (const i in subjectList) {
                                        if (subjectList[i].parentCode == item.parentCode) {
                                            existBool = false;
                                        }
                                    }
                                    if (existBool) {
                                        subjectList.push(item);
                                    }
                                });
                            } else {
                                scrollTop = calculationScrollTop();
                                layer.msg(result.msg, {
                                    icon: 1,
                                    time: 1500, //1秒关闭（如果不配置，默认是3秒）
                                    offset: scrollTop
                                }, function (index) {
                                    layer.close(index);
                                });
                            }
                        },
                        error: function (data) {
                            var result = eval("(" + data + ")");
                            alert(result.msg);
                        }
                    });
                }else {
                    let thisVal = $(this).attr("value");
                    $(this).attr("data-check","0");
                    $(this).removeClass("blue");
                    for (var index in applyTypeList) {
                        var type = applyTypeList[index];
                        if (type == $(this).attr("value")) {
                            applyTypeList.splice(index, 1);
                        }
                    }
                    if ($(this).parent().find("button[data-check=1]").length == 0) {
                        $(this).closest("dl").find("img").attr("src","${ctx}/static/img/checkBox_0.png");
                        for(var i = 0; i < subjectList.length; i++) {
                            var code = thisVal.substring(0,2);
                            if (subjectList[i].parentCode.startsWith(code)) {
                                subjectList.splice(i,1);
                                i--;
                            }
                        }
                    } else {
                        var addJson = [];
                        for (var i = 0; i < subjectList.length; i++) {
                            if (subjectList[i].parentCode.includes(thisVal)) {
                                var dataJson = subjectList[i];
                                var delCode = dataJson.parentCode;
                                subjectList.splice(i,1);
                                if (delCode.includes(",")) {
                                    for (let j in subjectList) {
                                        let addCode = subjectList[j].parentCode;
                                        if (delCode != addCode && (delCode.split(",")[0] == addCode || delCode.split(",")[1] == addCode) && addCode != thisVal) {
                                            addJson.push(dataJson);
                                        }
                                    }
                                }
                                i--;
                            }
                        }
                        for (const i in addJson) {
                            subjectList.push(addJson[i]);
                        }
                    }
                }
            });

            //初始化 省
            proCityPickerData.forEach(function (provinceItem, provinceIndex) {
                var province = "${claimCase.province}";
                var provinceOption = document.createElement("option");
                provinceOption.innerText = provinceItem['value'];
                if (province == provinceItem['value']) {
                    provinceOption.selected = true;

                    //初始化 市
                    var cityList = provinceItem.childs;
                    var city = "${claimCase.city}";
                    cityList.forEach(function (cityItem, cityIndex) {
                        var cityOption = document.createElement("option");
                        cityOption.innerText = cityItem['value'];
                        if (city == cityItem['value']) {
                            cityOption.selected = true;

                            //初始化 区
                            var districtList = cityItem.childs;
                            var district = "${claimCase.district}";
                            districtList.forEach(function (districtItem, districtIndex) {
                                var districtOption = document.createElement("option");
                                districtOption.innerText = districtItem['value'];
                                if (district == districtItem['value']) {
                                    districtOption.selected = true;
                                }
                                districtOption.value = districtItem['id'];
                                districtOption.setAttribute("data-idx", districtIndex);
                                $("#district").append(districtOption);
                            });
                        }
                        cityOption.value = cityItem['id'];
                        cityOption.setAttribute("data-idx", cityIndex);
                        $("#city").append(cityOption);
                    });

                }
                provinceOption.value = provinceItem['id'];
                provinceOption.setAttribute("data-idx", provinceIndex);
                $("#province").append(provinceOption);
            });

            var brand = "${claimCase.carBrand}";
            //初始化 品牌
            proBrandPickerData.forEach(function (item, index) {
                var childs = item['childs'];
                for (var i in childs) {
                    var value = childs[i];
                    var brandOption = document.createElement("option");
                    brandOption.innerText = value['value'];
                    brandOption.value = value['id'];
                    if (brand == value['id']) {
                        brandOption.selected = true;
                        $("#select2-carBrand-container").text(brand);
                    }
                    $("#carBrand").append(brandOption);
                }
            });

            // 初始化 事故责任
            $("#accidentLiability").select2({
                placeholder: "请选择",
                width: null
            });

            // 初始化 事故类型
            $("#accidentType").select2({
                placeholder: "请选择",
                width: null
            });

            $("#appraisalContent").on("change", "input", function (e) {
                sumMoney = new BigDecimal("0");
                var reg= /^(([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2}))|0)$/;
                $.each($("input[name=subjectMoney]"), function (index, obj) {
                    if ($(this).val() != "--" && reg.test($(this).val())) {
                        var addMoney = new BigDecimal(""+$(this).val());
                        sumMoney = sumMoney.add(addMoney).setScale(2, MathContext.ROUND_HALF_UP);
                    }
                });
                $("input[disabled]").val(sumMoney);
            });

            $(".logListInfo").on("click", ".rowInfo", function (e) {
                e.stopPropagation();
                let icon = $(this).children(1).children(1);
                var iconClass = icon.attr("class");
                if (iconClass == 'icon-plus') {
                    icon.attr("class", "minus");
                    $(this).next().show();
                }
                if (iconClass == 'minus') {
                    icon.attr("class", "icon-plus");
                    $(this).next().hide();
                }
                iframeH();
            });


            $("span[name='treateLabel']").on("click",function () {
                var classStr=$(this).attr("class");
                if(classStr=="span-type-unClick"){
                    $(this).attr("class","span-type-click");
                }else {
                    $(this).attr("class","span-type-unClick");
                }
            });


            $(".attachInfo").on("click", "img", function (e) {
                bigImg($(this).attr("src"));
            });

            $("#knightlllegalItems-content").on("click", "button", function() {
                $(this).toggleClass("blue");
            })

            $("img").each(function (idx, obj) {
                if (!$(obj).attr("data-url")) {
                    return true;
                }
                let img = new Image();
                img.src = $(obj).attr("data-url");
                /*img.onload方法里如果不是自调用函数，则取不到对应的obj*/
                img.onload = (function (e) {
                    $(obj).attr("src", $(obj).attr("data-url"));
                })();
                img.onerror = (function (e) {
                    $(obj).attr("src", '${ctx}/a/job_done.png');
                });
            });


            pageUuid = guid();

            iframeH();

            // 保持登录不超时
            keepAlive();



        });

        function bigImg(src) {
            imgShow("#outerdiv", "#innerdiv", "#bigimg", src);
        }

        function imgShow(outerdiv, innerdiv, bigimg, src) {
            $(bigimg).attr("src", src);//设置#bigimg元素的src属性

            /*获取当前点击图片的真实大小，并显示弹出层及大图*/
            $("<img/>").attr("src", src).load(function () {
                var windowW = $(window).width();//获取当前窗口宽度
                var windowH = $(window).height();//获取当前窗口高度
                var realWidth = this.width;//获取图片真实宽度
                var realHeight = this.height;//获取图片真实高度
                var imgWidth, imgHeight;
                var scale = 0.8;//缩放尺寸，当图片真实宽度和高度大于窗口宽度和高度时进行缩放

                if (realHeight > windowH * scale) {//判断图片高度
                    imgHeight = windowH * scale;//如大于窗口高度，图片高度进行缩放
                    imgWidth = imgHeight / realHeight * realWidth;//等比例缩放宽度
                    if (imgWidth > windowW * scale) {//如宽度扔大于窗口宽度
                        imgWidth = windowW * scale;//再对宽度进行缩放
                    }
                } else if (realWidth > windowW * scale) {//如图片高度合适，判断图片宽度
                    imgWidth = windowW * scale;//如大于窗口宽度，图片宽度进行缩放
                    imgHeight = imgWidth / realWidth * realHeight;//等比例缩放高度
                } else {//如果图片真实高度和宽度都符合要求，高宽不变
                    imgWidth = realWidth;
                    imgHeight = realHeight;
                }
                $(bigimg).css("width", imgWidth);//以最终的宽度对图片缩放

                var w = (windowW - imgWidth) / 2;//计算图片与窗口左边距
                var h = $(window.parent).scrollTop();//计算图片与窗口上边距
                var ifm = parent.document.getElementById("contentFrame");
                if (!ifm) {
                    h = 100;
                }
                var maxHeight = windowH - h - 100;
                if(maxHeight < 0 ){
                    maxHeight = 100;
                }

                $(innerdiv).css({ "top": h, "left": w ,"overflow-x":"auto","max-height": maxHeight});//设置#innerdiv的top和left属性
                $(outerdiv).fadeIn("fast");//淡入显示#outerdiv及.pimg
            });

            var imgTransferDeg = 0;
            $(window).on("keydown",function (event) {
                console.log(event.keyCode);
                switch (event.keyCode) {
                    // 放大
                    case 87:
                        imgToSize(100);
                        break;
                    // 缩小
                    case 83:
                        imgToSize(-100);
                        break;
                    // 左旋
                    case 65:
                        imgReverse(imgTransferDeg=imgTransferDeg-90);
                        break;
                    // 右旋
                    case 68:
                        imgReverse(imgTransferDeg=imgTransferDeg+90);
                        break;
                }
            });

            $(outerdiv).click(function () {//再次点击淡出消失弹出层
                $(this).fadeOut("fast");
                $(window).off("keydown");
                $("#bigimg").attr("sytle","border: 5px solid #fff;");
            });
        }

        //放大缩小图片
        function imgToSize(size)
        {
            var img = $("#bigimg");
            var oWidth = img.width(); //取得图片的实际宽度
            var oHeight = img.height(); //取得图片的实际高度
            img.width(oWidth + size);
            img.height(oHeight + size / oWidth * oHeight);

            var windowW = $(window).width();//获取当前窗口宽度
            var w = (windowW - oWidth) / 2;//计算图片与窗口左边距
            var h = $(window.parent).scrollTop();//计算图片与窗口上边距
            var ifm = parent.document.getElementById("contentFrame");
            if (!ifm) {
                h = 100;
            }
            $("#innerdiv").css({ "top": h, "left": w });//设置#innerdiv的top和left属性
        }

        // 翻转图片
        function imgReverse(arg)
        {

            var img = $("#bigimg");
            console.log(img.css("-webkit-transform"))
            img.css({"-webkit-transform":"rotate("+arg+"deg)"});
        }




        function provinceChange() {
            var provinceIdx = $("#province option:selected").attr("data-idx");
            $("#city").html("<option value=''>-市-</option>");
            $("#district").html("<option value=''>-区-</option>");
            if (provinceIdx) {
                var cityList = proCityPickerData[provinceIdx].childs;
                cityList.forEach(function (item, index) {
                    var option = document.createElement("option");
                    option.innerText = item['value'];
                    option.value = item['id'];
                    option.setAttribute("data-idx", index);
                    $("#city").append(option);
                });
            }

        }

        function cityChange() {
            var provinceIdx = $("#province option:selected").attr("data-idx");
            var cityIdx = $("#city option:selected").attr("data-idx");
            $("#district").html("<option value=''>-区-</option>");
            if (cityIdx) {
                var districtList = proCityPickerData[provinceIdx].childs[cityIdx].childs;
                districtList.forEach(function (item, index) {
                    var option = document.createElement("option");
                    option.innerText = item['value'];
                    option.value = item['id'];
                    $("#district").append(option);
                });
            }
        }

        //案件关闭
        function closeCase(caseId) {
            scrollTop = calculationScrollTop();
            var content = '<div class="row" style="padding: 0px 30px" ><div class="col-sm-3">&nbsp;</div><textarea rows="5" class="col-sm-6 form-control" name="closeCaseMsg" id="closeCaseMsg" autocomplete="off" placeholder="请输入关闭案件原因"></textarea></div>' ;
            layer.open({
                type: 1,
                content: content,
                title: '关闭案件',
                area:  ['500px', '300px'],
                btn: ['确认','取消'],
                offset: scrollTop,
                yes: function(index,obj){
                    console.log(typeof $("#closeCaseMsg").val());
                    var closeCaseMsg = $("#closeCaseMsg").val();
                    if(typeof closeCaseMsg != 'string' || closeCaseMsg.trim()==''){
                        layer.msg("关闭案件原因不能为空", {icon: 2, time: 3000,offset: scrollTop});
                    }else {
                        var formData = new FormData();
                        formData.append("claimCaseId", caseId);
                        formData.append("type", 3);
                        formData.append("description", closeCaseMsg);
                        $.ajax({
                            url: "${ctx}/insuranceCaseController/closeCase",
                            type: 'POST',
                            data: formData,
                            async: false,
                            cache: false,
                            contentType: false,
                            processData: false,
                            success: function (data) {
                                var result = eval("(" + data + ")");
                                if (result.ret == "0") {
                                    loader.show();
                                    $("#screenLoading").css("top", scrollTop);
                                    setTimeout(function(){
                                        layer.msg('关闭案件成功', {
                                            icon: 1,
                                            time: 1000,
                                            offset: scrollTop
                                        }, function () {
                                            window.location.href = "${ctx}/claimCaseController/claimCaseList";
                                        });
                                    },1200);
                                } else {
                                    layer.msg(result.msg, {
                                        icon: 1,
                                        time: 1500,
                                        offset: scrollTop
                                    }, function (index) {
                                        layer.close(index);
                                    });
                                }
                            },
                            error: function (data) {
                                var result = eval("(" + data + ")");
                                alert(result.msg);
                            }
                        });
                    }
                }
            });
        }

        // 校验
        function checkVerifyInput() {
            const mobileCheck = /^1[3456789][0-9]\d{8}$/;

            var treatMobile = $("#treatMobile").val();
            if (treatMobile != undefined) {
                treatMobile = treatMobile.trim();
                if (treatMobile == "") {
                    $("#treatMobile").focus();
                    return "出险人手机号不能为空";
                }
                if (!mobileCheck.test(treatMobile)) {
                    $("#treatMobile").focus();
                    return "出险人手机号不合法";
                }
            }
            var policyPersonId = $("#policyPersonId").val().trim();
            if (policyPersonId == "") {
                $("#policyPersonId").focus();
                return "保单不能为空";
            }
            /*if (applyTypeList.length == 0) {
                return "出险类型不能为空";
            }*/
            var carBrand = $("#carBrand").val().trim();
            if (carBrand == "") {
                $("#carBrand").focus();
                return "肇事车辆信息不能为空";
            }
            var plateNumber = $("#plateNumber").val().trim();
            if (plateNumber == "") {
                $("#plateNumber").focus();
                return "肇事车牌不能为空";
            }
            var province = $("#province").val().trim();
            if (province == "") {
                $("#province").focus();
                return "事故发生省不能为空";
            }
            var city = $("#city").val().trim();
            if (city == "") {
                $("#city").focus();
                return "事故发生市不能为空";
            }
            var district = $("#district").val().trim();
            if (district == "") {
                $("#district").focus();
                return "事故发生区不能为空";
            }
            var address = $("#address").val().trim();
            if (address == "") {
                $("#address").focus();
                return "事故发生详细地址不能为空";
            }
            var description = $("#description").val().trim();
            if (description == "") {
                $("#description").focus();
                return "事故经过描述不能为空";
            }

            var accidentType = $("#accidentType").val().trim();
            if (accidentType == "") {
                $("#accidentType").focus();
                return "事故类型不能为空";
            }

            var accidentLiability = $("#accidentLiability").val().trim();
            if (accidentLiability == "") {
                $("#accidentLiability").focus();
                return "事故责任不能为空";
            }

            var accidentProportion = $("#accidentProportion").val().trim();
            if (accidentProportion == "") {
                $("#accidentProportion").focus();
                return "事故比例不能为空";
            }else {
                var reg = /^\d+$/;
                let result = false;
                if(reg.test(accidentProportion) && (accidentProportion>=0 && accidentProportion <=100)){
                    result = true;
                }
                if(!result){
                    $("#accidentProportion").focus();
                    return "事故比列格式错误，只能为0-100的整数！！";
                }
            }
            var logTextArea = $("#logTextArea").val().trim();
            if (logTextArea == "") {
                $("#logTextArea").focus();
                return "沟通备注不能为空";
            }
            var objectLength = $(".tr-object").length;
            if (objectLength == 0) {
                return "必须存在一个损失项目";
            }
        }
        
        //审核通过
        function auditPass() {
            freshDutyInfo();
            var errorVerify = checkVerifyInput();
            scrollTop = calculationScrollTop();
            if (errorVerify) {
                layer.msg(errorVerify, {
                    offset: scrollTop,
                    icon: 2
                });
                return false;
            }

            var claimCase = {
                id: "${claimCase.id}",
                policyPersonId: $("#policyPersonId").val().trim(),
                /*applyType: applyTypeList.join(","),*/
                carBrand: $("#carBrand").val().trim(),
                plateNumber: $("#plateNumber").val().trim(),
                province: $("#province").val().trim(),
                city: $("#city").val().trim(),
                district: $("#district").val().trim(),
                address: $("#address").val().trim().replaceAll("\n", ""),
                description: $("#description").val().trim().replaceAll("\n", ""),
                accidentType: $("#accidentType").val().trim(),
                accidentLiability: $("#accidentLiability").val().trim(),
                accidentProportion: $("#accidentProportion").val().trim(),
                remark: $("#logTextArea").val().trim().replaceAll("\n", ""),
                // subjectList: subjectList
            };
            var treatMobile = $("#treatMobile").val();
            if (treatMobile != undefined) {
                claimCase.treatMobile = treatMobile.trim();
            }
            let label = "";
            $.each($("span[class='span-type-click']"),function () {
                let code = $(this).attr("code");
                label +=","+code;
            });
            if(label!=""){
                label = label.substring(1);
                claimCase["label"] = label;
            }
            let dataObject = localStorage.getItem(pageUuid);
            console.log(dataObject,"赔付项数据");
            if(dataObject){
                claimCase["claimCaseObjectVoList"] = JSON.parse(dataObject);
            }

            // 组装exInfo
            let knightlllegalItems = "";
            $("#knightlllegalItems-content").find("button[class='blue']").each(function() {
                knightlllegalItems += "," + $(this).attr("data-value");
            });
            if (knightlllegalItems) {
                knightlllegalItems = knightlllegalItems.substr(1);
                let exInfo = {"knightlllegalItems" : knightlllegalItems};
                claimCase["exInfo"] = JSON.stringify(exInfo);
            }

            $.ajax({
                url: "${ctx}/claimCaseController/auditPass",
                type: 'POST',
                data: JSON.stringify(claimCase),
                async: false,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    var result = eval("(" + data + ")");
                    loader.show();
                    $("#screenLoading").css("top", scrollTop);
                    if (result.ret == "0") {
                        setTimeout(function(){
                            layer.msg(result.msg, {icon: 1, time: 1500, offset: scrollTop}, function (index) {
                                layer.close(index);
                                window.location.href = "${ctx}/claimCaseController/claimCaseList";
                            });
                        }, 1200);
                    } else {
                        setTimeout(function(){
                            layer.msg(result.msg, {icon: 2, time: 1000, offset: scrollTop}, function (index) {
                                layer.close(index);
                                window.location.href = "${ctx}/claimCaseController/claimCaseList";
                            });
                        }, 1200);
                    }
                },
                error: function (data) {
                    var result = eval("(" + data + ")");
                    layer.msg(result.msg, {icon: 1, time: 1000, offset: scrollTop}, function () {
                        layer.close(index);
                    });
                }
            });
        }
        
        //再次去电
        function againPhone(e) {
            scrollTop = calculationScrollTop();
            var formData = new FormData();
            formData.append("claimCaseId", "${claimCase.id}");
            if ($(e).text().trim() == "再次去电") {
                formData.append("phoneType", 0);
            }
            if ($(e).text().trim() == "去电完成") {
                formData.append("phoneType", 1);
            }
            formData.append("type", 2);
            $.ajax({
                url: "${ctx}/insuranceCaseController/againPhone",
                type: 'POST',
                data: formData,
                async: false,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    var result = eval("(" + data + ")");
                    loader.show();
                    $("#screenLoading").css("top", scrollTop);
                    setTimeout(function(){
                        layer.msg(result.msg, {icon: 1, time: 2000, offset: scrollTop}, function (index) {
                            if(result.ret=='0'){
                                window.location.reload();
                            }else {
                                layer.close(index);
                            }
                        });
                    }, 1000);
                },
                error: function (data) {
                    var result = eval("(" + data + ")");
                    layer.msg(result.msg, {icon: 1, time: 1000, offset: scrollTop}, function () {
                        layer.close(index);
                    });
                }
            });
        }

        //标记是否疑难
        function markDiffcultCase(e) {
            scrollTop = calculationScrollTop();
            let difficultCase = "${claimCase.isDifficultCase}";
            if (difficultCase + "" != 1) {
                var content = '<div class="row" style="padding: 0px 30px" ><div class="col-sm-3">&nbsp;</div><textarea rows="5" class="col-sm-6 form-control"  id="markDiffcultCaseMsg" autocomplete="off" placeholder="请输入挂起原因"></textarea></div>' ;
                layer.open({
                    type: 1,
                    content: content,
                    title: '案件挂起',
                    area:  ['500px', '300px'],
                    btn: ['确认','取消'],
                    offset: scrollTop,
                    yes: function(index,obj){
                        console.log(typeof $("#markDiffcultCaseMsg").val());
                        var hangCaseMsg = $("#markDiffcultCaseMsg").val();
                        if(typeof hangCaseMsg != 'string' || hangCaseMsg.trim()==''){
                            layer.msg("挂起原因不能为空", {icon: 2, time: 3000,offset: scrollTop});
                        }else {
                            var formData = new FormData();
                            formData.append("claimCaseId", "${claimCase.id}");
                            formData.append("type", 3);
                            formData.append("isSkip", 1);
                            formData.append("description", hangCaseMsg);
                            $.ajax({
                                url: "${ctx}/insuranceCaseController/markDiffcultCase",
                                type: 'POST',
                                data: formData,
                                async: false,
                                cache: false,
                                contentType: false,
                                processData: false,
                                success: function (data) {
                                    var result = eval("(" + data + ")");
                                    loader.show();
                                    $("#screenLoading").css("top", scrollTop);
                                    if (result.ret == "0") {
                                        layer.msg('案件挂起成功', {
                                            icon: 1,
                                            time: 1500,
                                            offset: scrollTop
                                        }, function () {
                                            window.location.reload();
                                        });
                                    } else {
                                        layer.msg(result.msg, {
                                            icon: 2,
                                            time: 3000 ,
                                            offset: scrollTop
                                        }, function (index) {
                                            layer.close(index);
                                        });
                                    }
                                },
                                error: function (data) {
                                    var result = eval("(" + data + ")");
                                    alert(result.msg);
                                }
                            });
                        }
                    }
                });
            }else {
                layer.confirm(
                    '是否取消疑难?', {icon: 2, title: '执行',offset: scrollTop}, function (index) {
                        var formData = new FormData();
                        formData.append("claimCaseId", "${claimCase.id}");
                        formData.append("type", 4);
                        $.ajax({
                            url: "${ctx}/insuranceCaseController/markDiffcultCase",
                            type: 'POST',
                            data: formData,
                            async: false,
                            cache: false,
                            contentType: false,
                            processData: false,
                            success: function (data) {
                                var result = eval("(" + data + ")");
                                if (result.ret == "0") {
                                    layer.msg('取消成功', {
                                        icon: 1,
                                        time: 1500,
                                        offset: scrollTop
                                    }, function () {
                                        window.location.reload();
                                    });
                                } else {
                                    layer.msg(result.msg, {
                                        icon: 2,
                                        time: 3000,
                                        offset: scrollTop
                                    }, function (index) {
                                        layer.close(index);
                                    });
                                }
                            },
                            error: function (data) {
                                var result = eval("(" + data + ")");
                                alert(result.msg);
                            }
                        });

                    }
                )
            }

        }
        
        function viewClaimApplyBook(claimCaseId) {
            window.open("${ctx}/claimCaseController/viewClaimApplyBook?claimCaseId="+claimCaseId);
        }
        
        function refreshBook() {
            scrollTop = calculationScrollTop();
            $.ajax({
                url: "${ctx}/claimCaseController/refreshBook?claimCaseId=${claimCase.id}",
                type: 'POST',
                async: false,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    var result = eval("(" + data + ")");
                    if (result.ret == "0") {
                         $(".caseBook").html("<iframe width='280px' height='300px' src=" + result.data.fileObjectId + "></iframe>");
                    } else {
                        layer.msg(result.msg, {icon: 1, time: 1000, offset: scrollTop}, function (index) {
                            layer.close(index);
                        });
                    }
                },
                error: function (data) {
                    var result = eval("(" + data + ")");
                    layer.msg(result.msg, {icon: 1, time: 1000, offset: scrollTop}, function () {
                        layer.close(index);
                    });
                }
            });
        }


        //修改估损金额
        function modifyAppraisalAmount(claimCaseId) {
            $("#appraisalContent").html("");
            for (let index in subjectList) {
                var dataJson = subjectList[index];
                let appraisalAmount = dataJson.appraisalAmount;
                if (appraisalAmount == "-1") {
                    appraisalAmount = "--";
                }
                var div = `<div class="col-sm-12 " style="display: flex;margin-bottom: 10px">
                                    <label class="col-sm-4" style="padding-right: 0;line-height: 34px">` + dataJson.subjectName + `：</label>
                                    <div class="col-sm-4" style="display: flex">
                                        <input class="form-control" data-index="` + index + `" type="text" name="subjectMoney" value="` + appraisalAmount + `"><span style="line-height: 34px">（元）</span>
                                    </div>
                               </div>`;
                $("#appraisalContent").append(div);
            }
            var sumDiv = `<div class="col-sm-12 " style="display: flex;margin-bottom: 10px">
                    <label class="col-sm-3"
                           style="padding-right: 0;line-height: 34px">估损金额：</label>
                    <div class="col-sm-9" style="display: flex">
                        <input type="text" value="`+sumMoney+`" disabled class="form-control"><span style="line-height: 34px">（元）</span>
                    </div>
                </div>`;
            $("#appraisalContent").append(sumDiv);
            var openWindowWidth = $(document).width() * 0.5 + "px";
            scrollTop = calculationScrollTop();
            layer.open({
                type: 1,
                title: '修改估损金额',
                area: openWindowWidth,
                offset: scrollTop,
                fix: false, //不固定
                maxmin: true,
                content: $("#appraisalAmountDiv"),
                btn: ['确认','取消'],
                yes:function (index,obj) {
                    var closeBool = true;
                    var reg= /^(([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2}))|0)$/;
                    $.each($("input[name=subjectMoney]"), function (index, obj) {
                        let dataIndex = $(this).attr("data-index");
                        var thisVal = $(this).val();
                        if (thisVal != "--" && !reg.test($(this).val())) {
                            closeBool = false;
                            layer.msg("金额填写有误！", {icon: 2, time: 1500, offset: scrollTop}, function (index) {
                                layer.close(index);
                            });
                            return;
                        }
                        if (thisVal == "--") {
                            thisVal = "-1";
                        }
                        subjectList[dataIndex].appraisalAmount = thisVal;
                    });
                    if (closeBool) {
                        $("#appraisalAmount").text(sumMoney);
                        layer.close(index);
                    }
                }
            });
        }

        //查看详情
        function caseDetail(claimCaseId, status) {
            console.log(claimCaseId);
            window.location.href="${ctx}/claimCaseController/caseDetail?caseId="+claimCaseId;
        }

        //跳过审核
        function skipClaimVerify(caseId) {
            scrollTop = calculationScrollTop();
            var content = '<div class="row" style="padding: 0px 30px" ><div class="col-sm-3">&nbsp;</div><textarea rows="5" class="col-sm-6 form-control" name="skipCaseReason" id="skipCaseReason" autocomplete="off" placeholder="请输入跳过原因"></textarea></div>';
            layer.open({
                type: 1,
                content: content,
                title: '案件审核跳过',
                area: ['500px', '300px'],
                btn: ['确认', '取消'],
                offset: scrollTop,
                yes: function (index, obj) {
                    console.log(typeof $("#skipCaseReason").val());
                    var skipReason = $("#skipCaseReason").val();
                    if (typeof skipReason != 'string' || skipReason.trim() == '') {
                        layer.msg("跳过原因不能为空", {icon: 2, time: 3000, offset: scrollTop});
                    } else {
                        var formData = new FormData();
                        formData.append("claimCaseId", caseId);
                        formData.append("skipReason", skipReason);
                        $.ajax({
                            url: "${ctx}/claimCaseController/skipClaimVerify",
                            type: 'POST',
                            data: formData,
                            async: false,
                            cache: false,
                            contentType: false,
                            processData: false,
                            success: function (data) {
                                var result = eval("(" + data + ")");
                                if (result.ret == "0") {
                                    loader.show();
                                    $("#screenLoading").css("top", scrollTop);
                                    setTimeout(function () {
                                        layer.msg('跳过案件成功', {
                                            icon: 1,
                                            time: 2000,
                                            offset: scrollTop
                                        }, function () {
                                            window.location.href = "${ctx}/claimCaseController/claimCaseList";
                                        });
                                    }, 1200);
                                } else {
                                    layer.msg(result.msg, {
                                        icon: 1,
                                        time: 1500 //1秒关闭（如果不配置，默认是3秒）
                                    }, function (index) {
                                        layer.close(index);
                                    });
                                }
                            },
                            error: function (data) {
                                var result = eval("(" + data + ")");
                                alert(result.msg);
                            }
                        });
                    }
                }
            });
        }

        
        // 计算滚动高度
        function calculationScrollTop() {
            var ifm = parent.document.getElementById("contentFrame");
            scrollTop = $(window.parent).scrollTop() + 150;
            if (!ifm) {
                scrollTop = 300;
            }
            return scrollTop + "px";
        }

        let objectData ;

        //刷新请求后台，责任数据
        function freshDutyInfo() {
            $("#dutyInfoShow").html("");
            let dataObject = localStorage.getItem(pageUuid);
            dataObject = JSON.parse(dataObject);
            console.log(dataObject);

            if(dataObject){
                objectData = new Map();
                for(let index in dataObject){
                    let obj = dataObject[index];
                    console.log(obj);


                    let typeName="-";
                    if(obj.typeName){
                        typeName = obj.typeName;
                    }

                    let name="-";
                    if(obj.name){
                        name = obj.name;
                    }
                    let mobile="";
                    if(obj.mobile){
                        mobile = obj.mobile;
                    }
                    let estimatedOverallLoss="-";
                    if(obj.estimatedOverallLoss){
                        estimatedOverallLoss = obj.estimatedOverallLoss;
                    }
                    let hospitalName = "-";
                    if (obj.hospitalName != undefined) {
                        hospitalName = obj.hospitalName;
                    }
                    let init = $('<tr class="tr-object">\n' +
                        '                                <td width="15%">'+typeName+'</td>\n' +
                        '                                <td width="15%">'+name+'</td>\n' +
                        '                                <td width="15%">'+mobile+'</td>\n' +
                        '                                <td width="15%">'+estimatedOverallLoss+'</td>\n' +
                        '                                <td width="15%" style="border-right: 1px solid #e7ecf1">'+hospitalName+'</td>\n' +
                        '                            </tr>');
                    let trInit = $('<td width="15%" >' +
                        '</td>');
                    objectData.set(obj.objectId,JSON.stringify(obj));
                    let editInit = $('<input value="编辑" type="button" name="editInit" class="audit-button btn btn-primary" onclick="editDutyInfo(\''+obj.objectId+'\')" />');

                    let delInit = $('<input value="删除" type="button"  name="delInit" class="audit-button btn btn-danger" onclick="delDutyInfo(this)"  dutyInfoId="'+obj.objectId+'") />');

                    trInit.append(editInit);
                    trInit.append(delInit);
                    init.append(trInit);
                    $("#dutyInfoShow").prepend(init);
                }
            }
            $("#dutyInfoShow").append('<tr>\n' +
                '                                      <td colspan="5" style="border-right: 1px solid #e7ecf1"></td>\n' +
                '                                      <td >\n' +
                '                                          <button type="button" class="audit-button btn btn-primary"\n' +
                '                                                  onclick="addDutyInfo()">新建</button>\n' +
                '                                      </td>\n' +
                '                                  </tr>');

            iframeH();

        }

        //增加赔付信息
        function addDutyInfo() {
            scrollTop = calculationScrollTop();
            layer.open({
                type: 2,
                title: '损失项目',
                area:  ['800px'],
                offset: scrollTop,
                fix: false, //不固定
                maxmin: true,
                content: "${ctx}/claimCaseObjectController/claimCaseObject?pageId=" + pageUuid,
                success: function (layero, index) {
                    layer.iframeAuto(index);
                }
            });
        }

        //编辑赔付信息
        function editDutyInfo(objectId) {

            scrollTop = calculationScrollTop();
            //编辑的时候将数据放到本地中，open的页面获取数据后移除
            localStorage.setItem(objectId, objectData.get(objectId));

            layer.open({
                type: 2,
                title: '损失项目',
                area:  ['800px'],
                offset: scrollTop,
                fix: false, //不固定
                maxmin: true,
                content: "${ctx}/claimCaseObjectController/claimCaseObject?pageId=" + pageUuid+"&objectId="+objectId,
                success: function (layero, index) {
                    layer.iframeAuto(index);
                }
            });
        }

        //删除赔付信息
        function delDutyInfo(obj) {
            let objectId = $(obj).attr("dutyInfoId");
            let dataObject = localStorage.getItem(pageUuid);
            dataObject = JSON.parse(dataObject);
            for(let index in dataObject){
                if(dataObject[index].objectId==objectId){
                    console.log("删除成功");
                    dataObject.splice(index,1);
                    break;
                }
            }
            localStorage.setItem(pageUuid,JSON.stringify(dataObject));
            freshDutyInfo();
        }

        //生成uuid
        function guid() {
            return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
                var r = Math.random() * 16 | 0,
                    v = c == 'x' ? r : (r & 0x3 | 0x8);
                return v.toString(16);
            });
        }

        function keepAlive() {
            setInterval(function () {
                    $.ajax({
                        url: "${ctx}/claimCaseController/keepAlive",
                        type: 'POST',
                        async: true,
                        cache: false,
                        contentType: false,
                        processData: false,
                        success: function (data) {
                            console.log(data);
                        },
                        error: function (data) {
                            console.log(data);
                        }
                    });}
                ,60000*5)
        }
    </script>
    <style>

        .error-class .layui-layer-content {
            font-size: 20px;
            font-weight: bold;
            color: red;
        }

        .layui-layer-msg {
            z-index: 1989101411 !important;
        }

        /*loader需要的遮罩层css start */
        #screen {
            height: 100%;
            width: 100%;
            z-index: 198910141;
            position: fixed;
            background: rgba(0,0,0,0.3);
            top:0;
        }

        #screen #screenLoading{
            margin: 0 auto;
            top: 50%;
            transform: translateY(-50%);
            background: greenyellow;
        }
        /*loader需要的遮罩层css end*/

        #appraisalAmountDiv {
            display: none;
        }
        th {
            text-align: center;
        }
        .case-title {
            color: #3662EC;
            font-size: 15px;
            font-weight: bold;
            margin-bottom: 15px;
        }
        .caseBook {
            display: flex;
            align-items: baseline;
        }
        .content-title {
            font-size: 17px;
            font-weight: bold;
            color: #3D3D3D;
            margin-bottom: 20px;
            padding-left: 0;
            margin-left: 0;
        }
        .case-content {
            margin: 0px 20px;
            border: 1px solid #ebebeb;
        }
        textarea {
            background: #EFEFEF;
        }
        .logTextArea{
            width: 100%;
            height: 135px;
        }
        .audit-button{
            margin: 10px 15px;
        }
        .clear-padding {
            padding: 0px;
        }
        .clear-margin {
            margin: 0px;
        }
        .blue {
            background: #0597FF;
            border-color: #ecebeb;
        }
        .logListInfo {
            margin-bottom: 2%;
        }
        .logListInfo .detailsInfo {
            display: none;
            padding-left: 8%;
        }
        .span-type {
            display: inline-block;
            background: #0b94ea;
            color: #fff;
            font-size: 10px;
            border-radius: 5px;
            margin:3px;
            padding: 2px;
        }
        .td-description-overflow {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 190px;
            border-right: 1px solid #ebebeb;
            text-align: left;
        }
        .minus {
            border: 1px solid black;
            border-radius: 50%;
            width: 13px;
            height: 13px;
            position: relative;
            margin-top: 3%;
        }

        .minus::before {
            content: '';
            position: absolute;
            left: 2px;
            top: 5px;
            width: 7px;
            border-top: 1px solid;
        }

        .logListInfo .detailsInfo > td {
            word-wrap:break-word;
            word-break:break-all;
            text-align: left;
        }
        .logListInfo .rowInfo:hover{
            cursor: pointer;
        }
        .span-type-unClick {
            background: #F4F4F4;
            color: black;
            font-size: 10px;
            border-radius: 5px;
            margin:3px;
            padding: 2px;
            cursor: pointer;
        }
        .span-type-click {
            background: #0597FF;
            color: #fff;
            font-size: 10px;
            border-radius: 5px;
            margin:3px;
            padding: 2px;
            cursor: pointer;
        }
        .block-show {
            display: flex;
        }

        .block-head-label {
            margin-bottom: 1%;
            font-size: 20px;
        }

        .attachInfo {
            border: 1px solid #D8D8D8;
            margin-bottom: 2%;
        }

        .attachInfo label {
            margin-top: 20px;
        }

        .attachInfo img {
            width: 100%;
            height: 100%;
            border: 1px solid;
        }

        .attachInfo .col-sm-2 {
            margin-top: 20px !important;
        }

        .attachInfo .icon-attach {
            display: block;
            position: absolute;
            background-color: #D8D8D8;
            color: #709BF3;
            width: 20px;
            height: 25px;
            right: 1px;
            top: 1px;
            text-align: center;
            line-height: 25px;
            font-size: 10px;
        }
    </style>
</head>


<div class="container-fluid" id="appraisalAmountDiv">
    <div class="row" style="padding-top: 30px;padding-bottom: 30px;margin: 0px 0px">
        <div class="col-sm-9" id="appraisalContent">
        </div>
    </div>
</div>

<body>

<div id="outerdiv" style="position: fixed; top: 0; left: 0; background: rgba(0,0,0,0.7); z-index: 2; width: 100%; height: 100%; display: none;">
    <div id="innerdiv" style="position: absolute;">
        <img id="bigimg" style="border: 5px solid #fff;" src="" />
    </div>
</div>

<div class="row">
    <div class="col-sm-12">
        <div class="portlet light portlet-fit bordered">
            <div class="portlet-title">
                <ul class="page-breadcrumb breadcrumb">
                    <li><span>风神</span> <i class="fa fa-circle"></i></li>
                    <li><span class="active">报案管理</span> <i class="fa fa-circle"></i></li>
                    <li><span class="active">报案审核</span></li>
                </ul>
            </div>
            <div class="portlet-body">
              <div class="row" style="padding: 0px 20px;">
                  <div class="col-sm-6 margin-bottom-20" style="font-size: 20px;">
                      案件号：${claimCase.claimCaseNo} <br>
                      承保公司：
                      <#if !errorMsg??>
                          <#if claimCase.province == '辽宁省' && claimCase.treatDate?datetime gte "2023-09-29 00:00:00"?datetime>
                              <span style="display:inline-block;background-color: #067ee5;color: #fff;padding: 2px 6px;border-radius: 2px;">国元</span>
                          <#else >
                              <span style="display:inline-block;background-color: #26c281;color: #fff;padding: 2px 6px;border-radius: 2px;">大地</span>
                          </#if>
                      </#if>
                  </div>
                  <div class="col-sm-6 text-right margin-bottom-20">
                      <button class="btn green margin-right-10" onclick="skipClaimVerify('${claimCase.id}')">跳过任务</button>
                      <@shiro.hasPermission name="REPORT_CASE_LIST_CLOSE_CASE">
                        <button class="btn btn-danger margin-right-10" onclick="closeCase('${claimCase.id}')">关闭报案</button>
                      </@shiro.hasPermission>
                  </div>
              </div>
                <div class="row margin-bottom-20" style="margin: 0px 20px">
                    <div class="text-center col-sm-12" style="padding: 18px 0px;font-weight: bold;border: 1px solid #ebebeb;">
                        <span class="font-red" style="margin-right: 20px">此出险人有相关类似案件，请核实</span>
                        <span>无相关类似案件，请新增报案信息</span>
                    </div>
                    <div style="border: 1px solid #e7ecf1;">
                        <table class="table table-striped table-hover table-striped table-header-fixed text-center" style="margin-bottom: 0px;">
                            <thead>
                            <tr>
                                <th width="9%">报案人姓名</th>
                                <th width="9%">报案人手机号</th>
                                <th width="9%">出险人姓名</th>
                                <th width="8%">出险人身份证</th>
                                <th width="9%">出险类型</th>
                                <th width="9%">报案时间</th>
                                <th width="9%">出险时间</th>
                                <th width="15%" style="border-right: 1px solid #ebebeb;text-align: left;">事故信息描述</th>
                                <th width="25%">功能</th>
                            </tr>
                            </thead>
                            <tbody>
                            <#if claimCaseList?exists && (claimCaseList?size gt 0)>
                                <#list claimCaseList as case>
                                    <tr>
                                        <td title="${case.applyName}">${case.applyName!"--"}</td>
                                        <td title="${case.applyMobile}">${case.applyMobile!"--"}</td>
                                        <td title="${case.treatName}">${case.treatName!"--"}</td>
                                        <td title="${case.treatIdNum}">${case.treatIdNum!"--"}</td>
                                        <td title="${case.applyType}">
                                            <#list applyTypeList as type>
                                                <#if case.applyType?contains("${type.code}")><span class="span-type">${type.parentTypeName+"-"+type.childTypeName}</span></#if>
                                            </#list>
                                        </td>
                                        <td>${case.startDate?string["yyyyMMdd"]}</td>
                                        <td>${case.treatDate?string["yyyyMMdd"]}</td>
                                        <td class="td-description-overflow" title="${case.description}">${case.description!"--"}</td>
                                        <#--功能-->
                                        <td>
                                            <@shiro.hasPermission name="REPORT_CASE_LIST_CLOSE_CASE">
                                                <#if !case.status?contains("-1") && case.status!="aex-20">
                                                    <button class="btn blue" onclick="closeCase('${case.id}')">关闭报案</button>
                                                </#if>
                                            </@shiro.hasPermission>
                                            <button class="btn blue" onclick="caseDetail('${case.id}')">查看详情</button>
                                            <button class="btn blue" onclick="viewClaimApplyBook('${case.id}')">理赔申请书</button>
                                        </td>
                                    </tr>
                                </#list>
                            <#else>
                                <tr>
                                    <td colspan="12" class="text-danger text-center"> 暂无数据</td>
                                </tr>
                            </#if>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="row case-content margin-bottom-20" style="">
                    <div class="content-title">基本信息</div>
                        <form class="row padding-tb-10" id="caseForm" method="post">
                            <div class="col-sm-6">
                                <div class="row" style="margin-bottom: 30px">
                                    <div class="case-title col-sm-12 col-sm-offset-2">报案人信息</div>
                                    <div class="col-sm-12">
                                        <div class="case-cent col-sm-offset-2">
                                            <div class="row margin-bottom-15">
                                                <div class="col-sm-4 text-right">
                                                    
                                                    报案人姓名：
                                                </div>
                                                <div class="col-sm-6">
                                                    ${claimCase.applyName}
                                                </div>
                                            </div>
                                            <div class="row margin-bottom-15">
                                                <div class="col-sm-4 text-right">
                                                    
                                                    报案人手机号：
                                                </div>
                                                <div class="col-sm-6">
                                                    ${claimCase.applyMobile}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row" style="margin-bottom: 30px">
                                    <div class="case-title col-sm-12 col-sm-offset-2">出险人信息</div>
                                    <div class="col-sm-12">
                                        <div class="case-cent col-sm-offset-2">
                                            <div class="row margin-bottom-15">
                                                <div class="col-sm-4 text-right">
                                                    出险人姓名：
                                                </div>
                                                <div class="col-sm-6" id="treatName-text">
                                                    ${claimCase.treatName}
                                                </div>
                                            </div>
                                            <div class="row margin-bottom-15">
                                                <div class="col-sm-4 text-right">
                                                    出险人身份证：
                                                </div>
                                                <div class="col-sm-6" id="treatIdNum-text">
                                                    ${claimCase.treatIdNum}
                                                </div>
                                            </div>
                                            <div class="row margin-bottom-15">
                                                <div class="col-sm-4 text-right">
                                                    手机号：
                                                </div>
                                                <div class="col-sm-6" id="mobile-text">
                                                    <#if claimCase.treatMobile != "" >
                                                        ${claimCase.treatMobile}
                                                    <#else>
                                                        <input type="text" name="treatMobile" id="treatMobile" class="form-control">
                                                    </#if>
                                                </div>
                                            </div>
                                            <div class="row margin-bottom-15">
                                                <div class="col-sm-4 text-right">
                                                    出险日期：
                                                </div>
                                                <div class="col-sm-6">
                                                    ${(claimCase.treatDate?string["yyyy-MM-dd"])!''}
                                                </div>
                                            </div>
                                            <div class="row margin-bottom-15">
                                                <div class="col-sm-4 text-right">
                                                    出险时间：
                                                </div>
                                                <div class="col-sm-6">
                                                    ${(claimCase.treatDate?string["HH:mm:ss"])!''}
                                                </div>
                                            </div>
                                            <div class="row margin-bottom-15">
                                                <div class="col-sm-4 text-right">
                                                    <span style="color: red">*</span>
                                                    可用保单选择：
                                                </div>
                                                <div class="col-sm-6">
                                                    <select class="form-control js-data-example-ajax kp-select2" name="policyPersonId" id="policyPersonId" <#if claimCase.comeFrom == 1>disabled</#if>>
                                                        <option value="">请选择</option>
                                                        <#list policyPersonList as p>
                                                            <option value="${p.id}" <#if claimCase.policyPersonId == p.id>selected</#if>>${p.name}-${p.idNumber}-${p.startDate?string["yyyy年MM月dd日HH时mm时ss秒"]}至${p.endDate?string["yyyy年MM月dd日HH时mm时ss秒"]}</option>
                                                        </#list>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <#--<div class="row">
                                    <input type="hidden" name="applyType" id="applyType">
                                    <div class="case-title col-sm-12 col-sm-offset-2" >出险类型</div>
                                    <div class="col-sm-12">
                                        <div class="case-cent col-sm-offset-4">
                                            <#if applyTypeMap?exists && (applyTypeMap?size gt 0)>
                                                <#list applyTypeMap.keySet() as key>
                                                    <div class="margin-bottom-15">
                                                        <dl>
                                                            <dt class="margin-bottom-10">
                                                                <img src="${ctx}/static/img/checkBox_0.png" class="img-apply"> ${key}
                                                            </dt>
&lt;#&ndash;                                                                <img src="<#if applyTypeMap.get(key)?contains(claimCase.applyType?split)>${ctx}/static/img/checkBox_1.png<#else>${ctx}/static/img/checkBox_0.png</#if>" class="img-apply"> ${key}</dt>&ndash;&gt;
                                                            <div style="padding-left: 30px" class="apply-type">
                                                                <#list applyTypeMap.get(key) as value>
                                                                    <button type="button" data-check="<#if claimCase.applyType?contains(value.code)>1<#else>0</#if>"
                                                                            style="margin-right: 10px" value="${value.code}"
                                                                            class="<#if claimCase.applyType?contains(value.code)>blue</#if>">${value.childTypeName}</button>
                                                                </#list>
                                                            </div>
                                                        </dl>
                                                    </div>
                                                </#list>
                                            </#if >
                                        </div>
                                    </div>
                                </div>-->
                            </div>
                            <div class="col-sm-6">
                                <div class="row" style="margin-bottom: 30px">
                                    <div class="case-title col-sm-12 col-sm-offset-1" >事故信息</div>
                                    <div class="col-sm-12">
                                        <div class="case-cent col-sm-offset-1">
                                            <div class="row margin-bottom-15">
                                                <div class="col-sm-4 text-right">
                                                    <span style="color: red">*</span>
                                                    出险人肇事车辆信息：
                                                </div>
                                                <div class="col-sm-6">
                                                    <select class="form-control js-data-example-ajax kp-select2" name="carBrand" id="carBrand">
                                                        <option value="">-请选择-</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="row margin-bottom-15">
                                                <div class="col-sm-4 text-right">
                                                    <span style="color: red">*</span>
                                                    出险人肇事车牌：
                                                </div>
                                                <div class="col-sm-6">
                                                    <input type="text" class="form-control" name="plateNumber" id="plateNumber" value="${claimCase.plateNumber}">
                                                </div>
                                            </div>
                                            <div class="row margin-bottom-15">
                                                <div class="col-sm-4 text-right">
                                                    <span style="color: red">*</span>
                                                    事故发生地点：
                                                </div>
                                                <div class="col-sm-6 margin-bottom-10">
                                                    <div class="row">
                                                        <div class="col-sm-4">
                                                            <select class="form-control" name="province" id="province" onchange="provinceChange()">
                                                                <option value="">-省-</option>
                                                            </select>
                                                        </div>
                                                        <div class="col-sm-4">
                                                            <select class="form-control" name="city" id="city" onchange="cityChange()">
                                                                <option value="">-市-</option>
                                                            </select>
                                                        </div>
                                                        <div class="col-sm-4">
                                                            <select class="form-control" name="district" id="district">
                                                                <option value="">-区-</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-sm-8 col-sm-offset-2">
                                                    <textarea class="form-control" placeholder="请输入详细地址" rows="4" style="width: 100%" name="address" id="address">${claimCase.address}</textarea>
                                                </div>
                                            </div>
                                            <div class="row margin-bottom-10">
                                                <div class="col-sm-4 text-right">
                                                    <span style="color: red">*</span>
                                                    事故经过描述：
                                                </div>
                                            </div>
                                            <div class="row margin-bottom-15">
                                                <div class="col-sm-8 col-sm-offset-2">
                                                    <textarea class="form-control" placeholder="请输入事件描述" rows="7" style="width: 100%"  name="description" id="description">${claimCase.description}</textarea>
                                                </div>
                                            </div>
                                            <div class="row margin-bottom-15">
                                                <div class="col-sm-4 text-right">
                                                    <span style="color: red">*</span>
                                                    事故类型：
                                                </div>
                                                <div class="col-sm-6">
                                                    <#--<input type="text" class="form-control" name="accidentType" id="accidentType" list="accidentTypeList" value="${claimCase.accidentType}">
                                                    <datalist id="accidentTypeList">
                                                        <option>单方</option>
                                                        <option>多方</option>
                                                    </datalist>-->
                                                    <select class="form-control select2-multiple" name="accidentType"
                                                            id="accidentType">
                                                        <option value="">请选择</option>
                                                        <option value="单方">单方</option>
                                                        <option value="多方">多方</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="row margin-bottom-15">
                                                <div class="col-sm-4 text-right">
                                                    <span style="color: red">*</span>事故责任：
                                                </div>
                                                <div class="col-sm-6">
                                                    <#--<input type="text" class="form-control" name="accidentLiability" id="accidentLiability" list="accidentLiabilityList" value="${claimCase.accidentLiability}">
                                                    <datalist id="accidentLiabilityList">
                                                        <option>全责</option>
                                                        <option>主责</option>
                                                        <option>同责</option>
                                                        <option>次责</option>
                                                        <option>无责</option>
                                                    </datalist>-->
                                                    <select class="form-control select2-multiple" name="accidentLiability"
                                                            id="accidentLiability">
                                                        <option value="">请选择</option>
                                                        <#--<option value="全责">全责</option>
                                                        <option value="主责">主责</option>
                                                        <option value="同责">同责</option>
                                                        <option value="次责">次责</option>
                                                        <option value="无责">无责</option>-->
                                                        <#list accidentDutyList as duty>
                                                            <option value="${duty}">${duty}</option>
                                                        </#list>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="row margin-bottom-15">
                                                <div class="col-sm-4 text-right">
                                                    <span style="color: red">*</span>
                                                    事故比例：
                                                </div>
                                                <div class="col-sm-6">
                                                    <input type="text" class="form-control" name="accidentProportion" id="accidentProportion" list="accidentProportionList" value="${claimCase.accidentProportion}">
                                                    <datalist id="accidentProportionList">
                                                        <option>0</option>
                                                        <option>10</option>
                                                        <option>20</option>
                                                        <option>30</option>
                                                        <option>40</option>
                                                        <option>50</option>
                                                        <option>60</option>
                                                        <option>70</option>
                                                        <option>80</option>
                                                        <option>90</option>
                                                        <option>100</option>
                                                    </datalist>
                                                </div>
                                            </div>
                                            <div class="row margin-bottom-15" id="knightlllegalItems-content">
                                                <div class="col-sm-4 text-right">事故违规类型：</div>
                                                <div class="col-sm-6">
                                                        <#if accidentViolationTypeMap?? && (accidentViolationTypeMap.keySet()?size > 0)>
                                                            <ul class="list-inline">
                                                                <#list accidentViolationTypeMap.keySet() as key>
                                                                    <li style="margin: 5px 0px;">
                                                                        <button type="button" class="<#if knightlllegalItemsList?contains(key)>blue</#if>" data-value="${key}">${accidentViolationTypeMap.get(key)}</button>
                                                                    </li>
                                                                </#list>
                                                            </ul>
                                                        </#if >
                                                </div>
                                            </div>
                                            <div class="row margin-bottom-15">
                                                <div class="col-sm-4 text-right">
                                                    出险标签：
                                                </div>
                                                <div class="col-sm-6">
                                                    <div class="block-show">
                                                        <span name="treateLabel" class="span-type-unClick" code="Aax008">门诊</span>
                                                        <span name="treateLabel" class="span-type-unClick" code="Aax009">住院</span>
                                                        <span name="treateLabel" class="span-type-unClick" code="Aax010">死亡</span>
                                                        <span name="treateLabel" class="span-type-unClick" code="Aax011">ICU</span>
                                                        <span name="treateLabel" class="span-type-unClick" code="Aax021">残疾</span>
                                                        <span name="treateLabel" class="span-type-unClick" code="Aax022">骨折</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <#--<div class="row margin-bottom-15">
                                                <div class="col-sm-10 col-sm-offset-2">
                                                    估损金额：<span id="appraisalAmount">${claimCase.appraisalAmount}</span> 元 <a style="margin-left: 20px" onclick="modifyAppraisalAmount('${claimCase.id}')">修改</a>
                                                </div>
                                            </div>-->
                                        </div>
                                    </div>
                                </div>
                                <#--<div class="row" >
                                    <div class="case-title col-sm-12 col-sm-offset-1">理赔申请书</div>
                                    <div class="col-sm-12">
                                        <div class="case-cent col-sm-offset-1">
                                            <div class="row">
                                                <div class="col-sm-10 col-sm-offset-2">
                                                    <div class="caseBook">
                                                        <#if claimCaseAttach?exists>
                                                            <iframe width="280px" height="300px" src="${claimCaseAttach.fileObjectId}" >
                                                            </iframe>
                                                        <#else>
                                                            <a onclick="refreshBook()">点击刷新</a>
                                                        </#if>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>-->
                            </div>
                        </form>
                    </div>

                <div class="row" style="margin: 0px 20px">
                    <div class="block-head-label">
                        影像信息
                    </div>
                    <div class="col-sm-12 attachInfo">
                        <#if attachMap?? && (attachMap.keySet()?size>0)>
                            <#list attachMap.keySet() as key>
                                <div attach-type="${key}">
                                    <label><#if imgInfoMap.get(key)??>${imgInfoMap.get(key)}<#else >${key!'其它'}</#if></label>
                                    <div class="col-sm-12">
                                        <#list attachMap.get(key) as attach>
                                            <div class="col-sm-2" style="display: flex;flex-wrap: wrap;align-items: flex-start">
                                                <div style="position: relative;width:230px;height:250px">
                                                    <img  src="${ctx}/a/ajax-loader.gif" data-url="${attach.fileObjectId}" >
                                                    <#if attach.replenishNub!>
                                                        <div class="icon-attach">${attach.replenishNub!''}</div></#if>
                                                </div>
                                            </div>
                                        </#list>
                                    </div>
                                </div>
                            </#list>
                        </#if>
                    </div>
                </div>

                <div class="row dutyInfoArea" style="margin: 10px 20px 30px">
                    <div class="content-title col-sm-12"><span style="color: red">*</span>损失项目</div>
                    <div class="col-sm-12 clear-padding" style="border: 1px solid #e7ecf1;">
                        <table class="table table-striped table-hover table-striped table-header-fixed text-center clear-margin" >
                            <thead>
                            <tr>
                                <th width="15%">类型</th>
                                <th width="15%">赔付对象名称</th>
                                <th width="15%">手机号</th>
                                <th width="15%" >预估金额</th>
                                <th width="15%" style="border-right: 1px solid #e7ecf1">医院名称</th>
                                <th width="15%" >功能</th>
                            </tr>
                            </thead>
                            <tbody id="dutyInfoShow">

                            <tr>
                                <td colspan="5" style="border-right: 1px solid #e7ecf1"></td>
                                <td>
                                    <button type="button" class="audit-button btn btn-primary"
                                            onclick="addDutyInfo()">新建</button>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="row"style="margin: 10px 20px 30px">
                    <div class="content-title col-sm-12"><span style="color: red">*</span>沟通记录/新增备注</div>
                    <div class="col-sm-12 clear-padding margin-bottom-10">
                        <textarea placeholder="请输入" class="logTextArea" id="logTextArea"></textarea>
                    </div>
                    <div class="col-sm-12 text-center ">
                        <button class="audit-button btn btn-warning" onclick="auditPass()">审核通过</button>
                       <#-- <button class="audit-button btn btn-primary" onclick="againPhone(this)">
                            <#if claimCase.label!=null && claimCase.label?contains("Aax002")>去电完成<#else >再次去电</#if>
                        </button>
                        <button class="audit-button btn btn-primary" onclick="markDiffcultCase(this)">
                            <#if claimCase.isDifficultCase!=1>标记疑难<#else >取消疑难</#if>
                        </button>-->
                    </div>
                </div>
                <div class="row logListInfo" style="margin: 0px 20px">
                    <div class="content-title col-sm-12">日志信息 <input type="checkbox" id="checkSystem"> <label for="checkSystem" style="font-size: 8px;font-weight: bold;">是否展示系统日志</label></div>
                    <div class="col-sm-12 clear-padding" style="border: 1px solid #e7ecf1;">
                        <table class="table table-striped table-hover table-striped table-header-fixed text-center clear-margin" >
                            <thead>
                            <tr>
                                <th width="10%"></th>
                                <th width="15%">岗位</th>
                                <th width="15%">类型</th>
                                <th width="15%">人员</th>
                                <th width="45%">时间</th>
                            </tr>
                            </thead>
                            <tbody>
                            <#list claimCaseLogList as log>
                                <tr class="rowInfo" <#if log.creator == "系统"> style="display: none;" </#if>>
                                    <td width="10%" align="center">
                                        <div class="icon-plus"></div>
                                    </td>
                                    <td>${log.position}</td>
                                    <td>
                                       ${claimCaseLogTypeEnumMap.get(log.type).msg}
                                    </td>
                                    <td>
                                        <#if log.creator?contains("-") && log.creator!="-1" >
                                            ${log.creator?substring(0,log.creator?index_of("-"))}
                                        <#else>
                                            ${log.creator}
                                        </#if>
                                    </td>
                                    <td>${log.createTime?string["yyyy-MM-dd HH:mm:ss"]}</td>
                                </tr>
                                <tr class="detailsInfo">
                                    <td></td>
                                    <td colspan="4" title="${log.description?html}" width="90%" >${log.description}</td>
                                </tr>
                            </#list>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>