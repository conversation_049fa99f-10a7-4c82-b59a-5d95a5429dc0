<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>推送风神</title>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <link href="${ctx}/metronic/global/plugins/select2/css/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/select2/css/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>

    <script src="${ctx}/metronic/global/plugins/select2/js/select2.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/plugins/select2/js/select2.full.min.js" type="text/javascript"></script>
    <style>
        .row {
            margin-top: 20px;
            margin-left: 20px;
            margin-right: 20px;
        }

        .btn-div {
            text-align: right;
            margin: 10px;
        }

        .btn-div > button {
            margin-right: 20px;
        }



        .logListInfo {
            margin-bottom: 2%;
        }

        .logListInfo table {
            border: 1px solid #C2C2C2;
        }

        .logListInfo .detailsInfo {
            display: none;
            padding-left: 8%;
        }

        .minus {
            border: 1px solid black;
            border-radius: 50%;
            width: 13px;
            height: 13px;
            position: relative;
            margin-top: 3%;
        }

        .minus::before {
            content: '';
            position: absolute;
            left: 2px;
            top: 5px;
            width: 7px;
            border-top: 1px solid;
        }

        .logListInfo .detailsInfo > td {
            word-wrap:break-word;
            word-break:break-all;
            text-align: left;
        }
        .logListInfo .rowInfo:hover{
            cursor: pointer;
        }

        .label-title {
            font-size: 18px !important;
        }

        .blue {
            background: #0597FF;
            border-color: #ecebeb;
        }
    </style>
    <script>

        $(function() {
            $(".logListInfo").on("click", ".rowInfo", function (e) {
                e.stopPropagation();
                let icon = $(this).children(1).children(1);
                var iconClass = icon.attr("class");
                if (iconClass == 'icon-plus') {
                    icon.attr("class", "minus");
                    $(this).next().show();
                }
                if (iconClass == 'minus') {
                    icon.attr("class", "icon-plus");
                    $(this).next().hide();
                }
            });

            $("#status-content").on("click", "button", function() {
                var className = $(this).attr("class");
                if (className) {
                    $(this).removeClass("blue");
                } else {
                    $(this).attr("class", "blue");
                    $(this).siblings().removeClass("blue");
                }
            })
        })

        function push() {
            var status = $(".blue").attr("value");
            console.log(status);
            if (!status) {
                layer.msg("请选择回传状态", {icon: 2, time: 1500});
                return;
            }
            layer.confirm("请再次确认是否推送风神？", {icon: 3}, function (index) {
                    var formData = new FormData();
                    formData.append("claimCaseId", "${claimCase.id}");
                    formData.append("auditStatus", status);
                    $.ajax({
                        url: "${ctx}/claimCaseController/pushElm",
                        type: 'POST',
                        data: formData,
                        async: true,
                        cache: false,
                        contentType: false,
                        processData: false,
                        success: function (data) {
                            var result = eval("(" + data + ")");
                            if (result.ret == "0") {
                                layer.msg(result.msg, {icon: 1, time: 2000}, function(index) {
                                    parent.window.location.reload();
                                    layer.close(index);
                                });
                            } else {
                                layer.msg(result.msg, {
                                    icon: 2,
                                    time: 1500
                                });
                            }
                        },
                        error: function (data) {
                            var result = eval("(" + data + ")");
                            alert(result.msg);
                        }
                    });
                }
            );
        }
    </script>
</head>
<body>
    <div>

        <div class="row">
            <div class="col-sm-12" style="margin-bottom: 20px;padding-left: 0px;padding-right: 0px;">
                <div class="col-sm-4">
                    <div class="col-sm-12">
                        案件号：${claimCase.claimCaseNo}
                    </div>
                </div>
                <div class="col-sm-2" style="padding: 0px;">
                    <div class="col-sm-12" style="padding: 0px;">
                        来源：
                        <#if claimCase.comeFrom == 1>
                        APP
                        <#else>
                        400
                        </#if>
                    </div>
                </div>
                <div class="col-sm-4" style="padding: 0px;">
                    <div class="col-sm-12" style="padding: 0px;">
                        当前状态：${claimCaseStatusEumMap.get(claimCase.status).msg}
                    </div>
                </div>
            </div>
            <div class="col-sm-2">
                <span class="pull-right" style="font-size: 14px;color: #7f7f7f">选择回传状态：</span>
            </div>
            <div class="col-sm-9" id="status-content">
                <#list pushElmStatusEnumMap.keySet() as key>
                       <#-- <input name="elmStatus" style="left: 0px;right: 0px;margin-left: 0px;" code="${pushElmStatusEnumMap.get(key).status}"
                               type="radio">${key}-->
                        <button type="button" style="margin-bottom: 10px;margin-right: 10px;" value="${pushElmStatusEnumMap.get(key).status}">${key}</button>
                </#list>
            </div>
            <div class="row">
                <div class="col-sm-12 btn-div" >
                    <button class="btn btn-primary" onclick="push()">推送</button>
                    <button class="btn btn-warning" onclick="parent.layer.closeAll();">取消</button>
                </div>
            </div>
        </div>

        <div class="row logListInfo">
            <div class="block-head-label col-sm-12" style="margin-bottom: 20px;">
                <span class="label-title">日志信息</span>
            </div>
            <div class="col-sm-12">
                <table class="table">
                    <thead>
                    <tr>
                        <td width="10%"></td>
                        <td width="20%">岗位</td>
                        <td width="20%">类型</td>
                        <td width="20%">人员</td>
                        <td width="30%">时间</td>
                    </tr>
                    </thead>
                    <tbody>
                    <#if claimCaseLogList??>
                        <#list claimCaseLogList as log>
                            <tr class="rowInfo">
                                <td width="10%" align="center">
                                    <div class="icon-plus"></div>
                                </td>
                                <td width="20%">${log.position}</td>
                                <td width="20%">
                                    ${claimCaseLogTypeEnumMap.get(log.type).msg}
                                </td>
                                <td width="20%">
                                    <#if log.creator?contains("-") && log.creator!="-1" >
                                        ${log.creator?substring(0,log.creator?index_of("-"))}
                                    <#else>
                                        ${log.creator}
                                    </#if>
                                </td>
                                <td width="30%">${log.createTime?string["yyyy-MM-dd HH:mm:ss"]}</td>
                            </tr>
                            <tr class="detailsInfo">
                                <td width="10%" align="center"></td>
                                <td width="90%" colspan="4" style="overflow-x: visible;">
                                    请求参数：${log.reqData}<br>
                                    响应结果：${log.resData}<br>
                                </td>
                            </tr>
                        </#list>
                    </#if>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</body>
</html>