<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <link href="${ctx}/metronic/global/plugins/select2/css/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/select2/css/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>

    <script src="${ctx}/metronic/global/plugins/select2/js/select2.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/plugins/select2/js/select2.full.min.js" type="text/javascript"></script>
    <script type="text/javascript">
        const loader =  new Loaders({style:"rectangle"});
        function page(n, s) {
            $("#pageNum").val(n);
            $("#pageSize").val(s);
            $("#searchForm").submit();
            return false;
        }

        var scrollTop;

        $(document).ready(function () {

            // 业务标签 select2初始化
            $("#applyType").select2({
                placeholder: "请选择",
                width: null
            });

            $("#log-claimCaseNo").select2({
                placeholder: "请选择",
                width: null
            });

            // 业务标签 select2初始化
            var labelList = [];
            <#if labelShowMap?? && (labelShowMap.keySet()?size>0)>
            <#list labelShowMap.keySet() as key>
            labelList.push({id:'${key}',text:'${labelShowMap.get(key).msg}'})
            </#list>
            </#if>
            console.log(labelList);
            $("#label").select2({
                placeholder: "请选择",
                width: null,
                data: labelList
            });
            var nowLabel =[];
            <#if claimCaseVo.label?exists>
            <#list claimCaseVo.label?split(",") as code>
            nowLabel.push("${code}");
            </#list>
            console.log(nowLabel);
            $("#label").val(nowLabel).trigger('change');
            </#if>
        });

        //查看详情
        function caseDetail(claimCaseId) {
            window.location.href="${ctx}/claimCaseController/caseDetail4BS?caseId="+claimCaseId;
        }

        function statusSwitch(status) {
            $("#status").val(status);
            $("#searchForm").submit();
        }

        function anewCreateClaimApplyBook(id) {
            $.ajax({
                url: "${ctx}/claimCaseController/anewCreateClaimApplyBook?id=" + id,
                type: 'POST',
                async: true,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    var result = eval("(" + data + ")");
                    if (result.ret == "0") {
                        layer.msg(result.msg, {
                            icon: 1,
                            time: 2000
                        },function () {
                            window.location.reload();
                        });
                    } else {
                        layer.msg(result.msg, {
                            icon: 2,
                            time: 2000
                        });
                    }
                },
                error: function (data) {
                    var result = eval("(" + data + ")");
                    alert(result.msg);
                }
            });
        }

        //推送风神
        function layerOpenPushElm(id){
            layer.open({
                title: "推送风神",
                type: 2,
                area: ['900px','500px'],
                offset: 'auto',
                fix: false, //不固定
                maxmin: true,
                scrollbar: false,
                content: '${ctx}/claimCaseController/getPushElm?id='+id,
                success: function (layero, index) {
                    // layer.iframeAuto(index);
                }
            });
        }

        //编辑图片按钮
        function caseListEidtAttach() {
            var openWindowWidth = $(document).width() * 0.8 + "px";
            scrollTop = calculationScrollTop();
            let url = "${ctx}/claimCaseController/caseListEditAttach";
            layer.open({
                type: 2,
                title: '影像编辑',
                area: [openWindowWidth, '800px'],
                offset: scrollTop,
                fix: false, //不固定
                maxmin: true,
                content: url,
                shadeClose: true,
                success: function (layero, index) {
                    layer.iframeAuto(index);
                }
            });
        }


        // 计算滚动高度
        function calculationScrollTop() {
            var ifm = parent.document.getElementById("contentFrame");
            scrollTop = $(window.parent).scrollTop() + 150;
            if (!ifm) {
                scrollTop = 300;
            }
            return scrollTop + "px";
        }

        // 新增备注
        function layerOpenAddLog() {
            $("#log-claimCaseNo").select2("val", " ");
            $("#log-description").val("");
            layer.open({
                title: "新增备注",
                type: 1,
                area: ['900px','450px'],
                offset: 'auto',
                fix: false, //不固定
                maxmin: true,
                scrollbar: false,
                content: $('#addClaimCaseLog'),
                btn: ['新增', '取消'],
                yes: function (index, obj) {
                    var id = $("#log-claimCaseNo").val().trim();
                    var description = $("#log-description").val().trim();
                    if(!id) {
                        layer.msg('请选择案件号！！！', {icon: 2, time: 2000});
                        return;
                    }
                    if(!description) {
                        layer.msg('请输入日志描述！！！', {icon: 2, time: 2000});
                        return;
                    }
                    var formData = new FormData();
                    formData.append("claimCaseId", id);
                    formData.append("description", description);
                    $.ajax({
                        url: "${ctx}/claimCaseController/addCaseLog",
                        type: 'POST',
                        data: formData,
                        async: false,
                        cache: false,
                        contentType: false,
                        processData: false,
                        success: function (data) {
                            var result = eval("(" + data + ")");
                            if (result.ret == "0") {
                                layer.msg("成功", {
                                    icon: 1,
                                    time: 1500
                                }, function (index) {
                                    window.location.reload();
                                });
                            } else {
                                layer.msg(result.msg, {
                                    icon: 2,
                                    time: 2000
                                });
                            }
                        },
                        error: function (data) {
                            alert(data);
                            console.log(data);
                        }
                    });
                },
                btn2: function(index, obj) {
                    layer.close(index);
                }
            });
        }

        //分配人员
        function bindPerson(id){
            layer.open({
                title: "人员任务分配",
                type: 2,
                area: ['900px','500px'],
                offset: 'auto',
                fix: false, //不固定
                maxmin: true,
                scrollbar: false,
                content: '${ctx}/claimCaseDistributionController/getBindPersonLayer4BS?id='+id,
                success: function (layero, index) {
                    // layer.iframeAuto(index);
                }
            });
        }
        //清空内部审核或者保司审核人员
        function clearCheckPerson(id){
            layer.open({
                title: "清空初审复核人员",
                type: 2,
                area: ['900px','500px'],
                offset: 'auto',
                fix: false, //不固定
                maxmin: true,
                scrollbar: false,
                content: '${ctx}/claimCaseDistributionController/getCheckPersonLayer?id='+id,
                success: function (layero, index) {
                    // layer.iframeAuto(index);
                }
            });
        }

    </script>
    <style>

        .select2-dropdown {
            z-index: 19891099 !important;
        }

        /*loader需要的遮罩层css start */
        #screen {
            height: 100%;
            width: 100%;
            z-index: 198910141;
            position: fixed;
            background: rgba(0,0,0,0.3);
            top:0;
        }

        #screen #screenLoading{
            margin: 0 auto;
            top: 40%;
            transform: translateY(-50%);
            background: greenyellow;
        }
        /*loader需要的遮罩层css end*/

        .clear-padding {
            padding: 0px !important;
        }

        .form-active > div {
            border-top-left-radius: 5px;
            border-top-right-radius: 5px;
            border: 1px solid #e7ecf1;
            border-bottom: 0;
            border-right: 0;
            overflow: hidden;
            vertical-align: bottom;
            padding: 0px;
            display: inline-block;
        }
        .form-active > div > ul {
            margin: 0px;
            padding: 0px;
            list-style: none;
            display: flex;
        }
        .li-default {
            display: table-cell;
            border-bottom: 0;
            padding: 6px 70px 6px 30px;
            font-size: 17px;
            font-weight: bold;
            color: #3D3D3D;
            cursor: pointer;
            border-right: 1px solid #e7ecf1;
        }
        .li-default:hover {
            background: #0b94ea;
            color: #fff;
        }
        .li-blue {
            background: #0b94ea ;
            color: #fff;
        }
        .span-type {
            display: inline-block;
            background: #0b94ea;
            color: #fff;
            font-size: 10px;
            border-radius: 5px;
            margin:3px;
            padding: 2px;
        }
        td > a {
            display: inline-block;
            margin: 3px;
        }

        .td-overflow {
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            max-width: 100px;
        }
        .labelGroup span{
            margin-left: 5px;
            margin-right: 5px;
            background-color: #1676FF;
            color: white;
            border-radius: 2px;
        }

        .btn-bule {
            color: #FFFFFF;
            background-color: #1676FF;
            border-color: #1676FF;
            margin-left: 3%;
        }
    </style>
</head>
<body>

<div id="addClaimCaseLog" style="padding: 4% 4% 0px;display: none;">
    <div class="row" style="margin-bottom: 30px;">
        <div class="col-sm-2" style="height: 34px;line-height: 34px;text-align: right;">案件号：</div>
        <div class="col-sm-8">
            <select class="form-control select2-multiple" name="log-claimCaseNo" id="log-claimCaseNo">
                <option value=" ">请选择</option>
                <#if claimCaseNoList??>
                    <#list claimCaseNoList as case>
                        <option value="${case.id}">${case.claimCaseNo}</option>
                    </#list>
                </#if>
            </select>
        </div>
    </div>
    <div class="row" >
        <div class="col-sm-2" style="height: 34px;line-height: 34px;text-align: right;">备注描述：</div>
        <div class="col-sm-8">
            <textarea class="form-control" rows="10" name="log-description" id="log-description"></textarea>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-sm-12">
        <div class="portlet light portlet-fit bordered">
            <div class="portlet-title">
                <ul class="page-breadcrumb breadcrumb">
                    <li><span>风神</span> <i class="fa fa-circle"></i></li>
                    <li><span class="active">案件管理</span></li>
                </ul>
            </div>
            <div class="portlet-body">

                <!-- BEGIN FORM-->
                <form id="searchForm" class="form-horizontal" action="${ctx}/claimCaseController/claimCaseList4BS"
                      method="post">
                    <input id="pageNum" name="pageNum" type="hidden" value="1"/>
                    <input id="pageSize" name="pageSize" type="hidden" value="${page.pageSize}"/>
                    <div class="form-body">
                        <div class="row">
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">报案号：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <input type="text" class="form-control" name="claimCaseNo" id="claimCaseNo"
                                               value="${claimCaseVo.claimCaseNo}"
                                               placeholder="请输入"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">报案人姓名：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <input type="text" class="form-control" name="applyName" id="applyName"
                                               value="${claimCaseVo.applyName}"
                                               placeholder="请输入"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">报案人手机号：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <input type="text" class="form-control" name="applyMobile" id="applyMobile"
                                               value="${claimCaseVo.applyMobile}"
                                               placeholder="请输入"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                           <#-- <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">出险类型：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                       <select class="form-control select2-multiple" multiple name="applyType" id="applyType">
                                           <option value="">请选择</option>
                                           <#list applyTypeList as type>
                                               <option value="${type.code}" <#if claimCaseVo.applyType?contains("${type.code}")>selected</#if>>${type.parentTypeName+"-"+type.childTypeName}</option>
                                           </#list>
                                       </select>
                                    </div>
                                </div>
                            </div>-->
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">出险人姓名：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <input type="text" class="form-control" name="treatName" id="treatName"
                                               value="${claimCaseVo.treatName}"
                                               placeholder="请输入"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">出险人身份证：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <input type="text" class="form-control" name="treatIdNum" id="treatIdNum"
                                               value="${claimCaseVo.treatIdNum}"
                                               placeholder="请输入"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">报案时间：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <div class="input-group date-picker input-daterange"
                                             data-date-format="yyyy-mm-dd" >
                                            <input type="text" class="form-control" name="startDateStart" id="startDateStart" autocomplete="off"
                                                   value="${claimCaseVo.startDateStart}">
                                            <span class="input-group-addon"><font style="vertical-align: inherit;"><font
                                                            style="vertical-align: inherit;"> 至 </font></font></span>
                                            <input type="text" class="form-control" name="startDateEnd" id="startDateEnd" autocomplete="off"
                                                   value="${claimCaseVo.startDateEnd}">
                                        </div>
                                    </div>
                                </div>
                            </div>
<#--                            <div class="col-sm-4">-->
<#--                                <div class="form-group">-->
<#--                                    <label class="control-label col-sm-3" style="padding-right: 0">出险类型：</label>-->
<#--                                    <div class="col-sm-8" style="padding-left: 0;">-->
<#--                                        <select class="form-control select2-multiple" name="applyType"-->
<#--                                                id="applyType">-->
<#--                                            <option value=" " selected>请选择</option>-->
<#--                                            <#if appyTypeNewMap?exists>-->
<#--                                                <#list appyTypeNewMap.keySet() as key>-->
<#--                                                    <option value="${key}" <#if claimCaseVo.applyType==key>selected</#if>>${appyTypeNewMap.get(key).msg}</option>-->
<#--                                                </#list>-->
<#--                                            </#if>-->
<#--                                        </select>-->
<#--                                    </div>-->
<#--                                </div>-->
<#--                            </div>-->
                        </div>
                        <div class="row">
                            <#--<div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">是否立案：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                       <select class="form-control" name="isRegister" id="isRegister" value="">
                                           <option value="">请选择</option>
                                           <option value="1" <#if claimCaseVo.isRegister==1>selected</#if>>是</option>
                                           <option value="0" <#if claimCaseVo.isRegister==0>selected</#if>>否</option>
                                       </select>
                                    </div>
                                </div>
                            </div>-->
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">出险时间：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <div class="input-group date-picker input-daterange"
                                             data-date-format="yyyy-mm-dd" >
                                            <input type="text" class="form-control" name="treatDateStart" id="treatDateStart" autocomplete="off"
                                                   value="${claimCaseVo.treatDateStart}">
                                            <span class="input-group-addon"><font style="vertical-align: inherit;"><font
                                                            style="vertical-align: inherit;"> 至 </font></font></span>
                                            <input type="text" class="form-control" name="treatDateEnd" id="treatDateEnd" autocomplete="off"
                                                   value="${claimCaseVo.treatDateEnd}">
                                        </div>
                                    </div>
                                </div>
                            </div>
<#--                            <div class="col-sm-4">-->
<#--                                <div class="form-group">-->
<#--                                    <label class="control-label col-sm-3" style="padding-right: 0">标签：</label>-->
<#--                                    <div class="col-sm-8" style="padding-left: 0;">-->
<#--                                        <select class="form-control select2-multiple"  name="label"-->
<#--                                                id="label" multiple>-->
<#--                                            <option value="">请选择</option>-->
<#--                                            &lt;#&ndash;<#if labelShowMap?? && (labelShowMap.keySet()?size>0)>-->
<#--                                                <#list labelShowMap.keySet() as key>-->
<#--                                                    <option <#if (claimCaseVo.label!'-')==key>selected</#if>value="${key}">-->
<#--                                                        ${labelShowMap.get(key).msg}-->
<#--                                                    </option>-->
<#--                                                </#list>-->
<#--                                            </#if>&ndash;&gt;-->
<#--                                        </select>-->
<#--                                    </div>-->
<#--                                </div>-->
<#--                            </div>-->
                        </div>
                    </div>
                    <div class="form-actions">
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="btn-group pull-right" style="margin-bottom: 10px;margin-right: 30px;">
                                    <button onclick="layerOpenAddLog()" type="button" class="btn yellow" style="margin-bottom: 10px;">新增备注</button>
                                </div>
                                <div class="btn-group pull-right" style="margin-bottom: 10px;margin-right: 30px;">
                                    <button id="query" type="submit" class="btn green" style="margin-bottom: 10px;">查询</button>
                                </div>
                                <@shiro.hasPermission name="EDIT_CLAIM_ATTACH">
                                    <div class="btn-group pull-right" style="margin-bottom: 10px;;margin-right:30px">
                                        <button id="query" type="button" class="btn btn-bule" style="margin-bottom: 10px" onclick="caseListEidtAttach()">添加图片</button>
                                    </div>
                                </@shiro.hasPermission>
                            </div>
                        </div>
                    </div>
                    <div class="form-active">
                        <div>
                            <input type="hidden" name="status" id="status" value="${claimCaseVo.status}">
                            <ul>
                                <li class="li-default <#if claimCaseVo.status == 9>li-blue</#if>" onclick="statusSwitch(9)">未决
                                </li>
                                <li class="li-default <#if claimCaseVo.status == 8>li-blue</#if>" onclick="statusSwitch(8)">已决
                                </li>
                                <li class="li-default <#if claimCaseVo.status == 7>li-blue</#if>" onclick="statusSwitch(7)">关闭
                                </li>
                            </ul>
                        </div>
                    </div>
                </form>
                <table class="table table-striped table-bordered table-hover table-header-fixed">
                    <thead>
                    <tr>
                        <th width="10%">报案号</th>
                        <th width="10%">出险人姓名</th>
                        <th width="10%">出险人身份证</th>
                        <th width="12%">出险类型</th>
                        <th width="10%">标签</th>
                        <th width="7%">估损金额</th>
                        <th width="7%">赔款金额</th>
<#--                        <th width="13%" class="td-overflow">案件状态</th>-->
<#--                        <th width="8%">责任人</th>-->
                        <th width="13%">功能</th>
                    </tr>
                    </thead>
                    <tbody>
                    <#list page.list as vo>
                        <tr>
                            <td title="">${vo.claimCaseNo}</td>
                            <td title="">${vo.treatName}</td>
                            <td title="">${vo.treatIdNum}</td>
                            <td>
                                <#if vo.applyType??>
                                    <#list vo.applyType?split(",") as name>
                                        <span class="span-type">${name}</span>
                                    </#list>
                                </#if>
                            </td>
                            <td title="" class="labelGroup">
                                <#if vo.label??>
                                    <#list vo.label.split(",") as key>
                                        <#if (key?trim)!="">
                                            <span class="${key} span-type" style="color: ${labelShowMap.get(key).fontColor};background-color: ${labelShowMap.get(key).color}"><#if labelShowMap.get(key)??>${labelShowMap.get(key).msg}<#else>${key}</#if></span>
                                        </#if>
                                    </#list>
                                </#if>
                            </td>
                            <td>${vo.appraisalAmount!'--'}</td>
                            <td>${vo.payAmount!'--'}</td>
<#--                            <#if closeMap.get(vo.id)?? >-->
<#--                                <td class="td-overflow" title="${closeMap.get(vo.id)}（${claimCaseStatusEumMap.get(vo.status).msg}）">-->
<#--                                    ${closeMap.get(vo.id)}（${claimCaseStatusEumMap.get(vo.status).msg}）-->
<#--                                </td>-->
<#--                            <#else>-->
<#--                                <td class="td-overflow" title="${claimCaseStatusEumMap.get(vo.status).msg}">-->
<#--                                    ${claimCaseStatusEumMap.get(vo.status).msg}-->
<#--                                </td>-->
<#--                            </#if>-->
<#--                            <td>-->
<#--                                <#if vo.modifier??>-->
<#--                                    <#if vo.modifier?contains("-") && vo.modifier!="-1" >-->
<#--                                        ${vo.modifier?substring(0,vo.modifier?index_of("-"))}-->
<#--                                    <#else>-->
<#--                                        ${vo.modifier}-->
<#--                                    </#if>-->
<#--                                </#if>-->
<#--                            </td>-->
                            <#--功能-->
                            <td>
<#--                                <@shiro.hasPermission name="CLAIM_CASE_PUSH_ELM">-->
<#--                                    <a href="#" onclick="layerOpenPushElm('${vo.id}')">推送风神</a>-->
<#--                                </@shiro.hasPermission>-->
                                <a href="#" onclick="caseDetail('${vo.id}')">查看详情</a>
<#--                                 <@shiro.hasPermission name="ANEW_CREATE_CLAIM_APPLY_BOOK">-->
<#--                                    <a onclick="anewCreateClaimApplyBook('${vo.id}')">重新生成理赔申请书</a>-->
<#--                                 </@shiro.hasPermission>-->
                                <#--未决且不是报案待审核才有分配人员-->
                                <#if claimCaseVo.status == 9 && !vo.status?starts_with("aax")>
                                    <@shiro.hasPermission name="CLAIM_CASE_OBECT_DISTRIBUTION_AUDITER">
                                        <a onclick="bindPerson('${vo.id}')">分配人员</a>
                                    </@shiro.hasPermission>
                                </#if>
                                <#if claimCaseVo.status == 9 && !vo.status?starts_with("aax")>
                                    <@shiro.hasPermission name="CLAIM_CASE_OBECT_DISTRIBUTION_AUDITER">
                                        <a onclick="clearCheckPerson('${vo.id}')">清空初审复核人员</a>
                                    </@shiro.hasPermission>
                                </#if>


                            </td>
                        </tr>
                    </#list>
                    </tbody>
                </table>
                <div class="modal fade" id="saveBtnModal" tabindex="-1" role="basic" aria-hidden="true">
                    <div class="modal-dialog">
                        <img src="${ctx}/images/load.gif">
                    </div>
                </div>
            </div>
            <!-- END FORM-->
        </div>
    </div>
</div>
<@sc.pagination page=page />
</body>
</html>