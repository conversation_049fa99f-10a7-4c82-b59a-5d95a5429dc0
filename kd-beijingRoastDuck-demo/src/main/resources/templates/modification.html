<!DOCTYPE html>
<!--
Template Name: Metronic - Responsive Admin Dashboard Template build with Twitter Bootstrap 3.3.5
Version: 4.5.2
Author: KeenThemes
Website: http://www.keenthemes.com/
Contact: <EMAIL>
Follow: www.twitter.com/keenthemes
Like: www.facebook.com/keenthemes
Purchase: http://themeforest.net/item/metronic-responsive-admin-dashboard-template/4021469?ref=keenthemes
License: You must have a valid license purchased only from themeforest(the above link) in order to legally use the theme for your project.
-->
<!--[if IE 8]>
<html lang="en" class="ie8 no-js"> <![endif]-->
<!--[if IE 9]>
<html lang="en" class="ie9 no-js"> <![endif]-->
<!--[if !IE]><!-->
<html lang="en">
<!--<![endif]-->
<!-- BEGIN HEAD -->

<head>
    <meta charset="utf-8"/>
    <title>风神系统</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta content="width=device-width, initial-scale=1" name="viewport"/>
    <meta content="" name="description"/>
    <meta content="" name="author"/>

    <#include "/common/cssResource.html">
    <!-- BEGIN PAGE LEVEL STYLES -->
    <link href="${ctx}/metronic/pages/css/login.min.css" rel="stylesheet" type="text/css"/>
    <!-- END PAGE LEVEL STYLES -->

    <#include "/common/jsResource.html">
    <script src="${ctx}/static/js/jquery.slideunlock.js"></script>

    <style type="text/css">
        .login {
            overflow: hidden;
        }
        .text {
            position:absolute;
            align-self:flex-start;
            padding:0px 0px 0px 0px;
            box-sizing:border-box;
            width:100%;
            margin-left: 160px;
            color: #1676FF;
            cursor: pointer;
            margin-top: 27px;
        }

        .error-color{
            font-weight: bold;
            color: #f50000
        }
    </style>
    <script type="text/javascript">

        //增加金额校验
        jQuery.validator.addMethod("isNewPwdOnce", function (value, element) {
            if (value == "" || value == "undfind") {
                return false;
            }

            if (value==$("#newPwd").val()){
                return true;
            }else {
                return false;
            }

        }, "两次密码不一致");

        //增加金额校验
        jQuery.validator.addMethod("isNewPwd", function (value, element) {
            if (value == "" || value == "undfind") {
                return false
            }

            var p = /[0-9]/;
            var b = p.test(value);//true,说明有数字
            var p1 = /[a-z]/i;
            var b1 = p1.test(value);//true,说明有英文字母

            if (b&&b1 &&value.length>=6 && value.length<=12){
                return true;
            }else {
                return false;
            }

        }, "密码不符合校验");

        $(document).ready(
            function () {
                var form = $('#loginForm');
                form.validate({
                    errorElement: 'span', //default input error message container
                    errorClass: 'error-color', // default input error message class
                    focusInvalid: false, // do not focus the last invalid input
                    rules: {
                        verification: {
                            required: true,
                            remote: "${ctx}/oauthController/checkVerification"
                        },
                        originalPwd: {
                            required: true,
                            remote: "${ctx}/oauthController/checkOriginalPwd"
                        },
                        newPwd: {
                            required: true,
                            isNewPwd: true
                        },
                        newPwdOnce: {
                            required: true,
                            isNewPwdOnce: true
                        }
                    },
                    messages: {
                        verification: {
                            required: "<font color='#CA0000' style='font-weight:bold;'>验证码不能为空</font>",
                            remote:"<font color='#CA0000' style='font-weight:bold;'>验证码错误</font>"
                        },
                        originalPwd: {
                            required: "<font color='#CA0000' style='font-weight:bold;'>原密码不能为空</font>",
                            remote:"<font color='#CA0000' style='font-weight:bold;'>原密码错误</font>"
                        },
                        newPwd: {
                            required: "<font color='#CA0000' style='font-weight:bold;'>新密码不能为空</font>",
                            remote:"<font color='#CA0000' style='font-weight:bold;'>新密码不符合规范</font>"
                        },
                        newPwdOnce: {
                            required: "<font color='#CA0000' style='font-weight:bold;'>新密码不能为空</font>",
                            remote:"<font color='#CA0000' style='font-weight:bold;'>新密码两次输入不相同</font>"
                        }
                    },

                    invalidHandler: function (event, validator) { //display error alert on form submit
                        App.scrollTo(form, 0);
                    },

                    highlight: function (element) { // hightlight error inputs
                        $(element).closest('.form-group').removeClass(
                            "has-success").addClass('has-error'); // set error class to the control group
                    },

                    unhighlight: function (element) { // revert the change done by hightlight

                    },

                    success: function (label, element) {
                        var icon = $(element).parent('.input-icon').children(
                            'i');
                        $(element).closest('.form-group').removeClass(
                            'has-error').addClass('has-success'); // set success class to the control group
                        icon.removeClass("fa-warning").addClass("fa-check");
                    },

                    submitHandler: function (form) {
                        var formData = new FormData($('#loginForm')[0]);
                        $.ajax({
                            url : "${ctx}/oauthController/changePassword",
                            type : 'POST',
                            data : formData,
                            async : true,
                            cache : false,
                            contentType : false,
                            processData : false,
                            dataType: 'json',
                            success : function(data) {
                                if(data.ret == 0){
                                    alert("修改成功");
                                    window.location.href="${ctx}/index";
                                }
                            },
                            error : function(data) {
                                alert("ajax异常");
                            }
                        });

                    }
                });
            }
        );


        /*var wait=60;
        function getTime(){
            if (wait==60){
                $.ajax({
                    url: "${ctx}/oauthController/verification",
                    type: 'POST',
                    async: true,
                    cache: false,
                    dataType: 'json',
                    contentType: false,
                    processData: false,
                    success: (data) => {
                    },
                    error: (data) => {
                        shark.show("系统异常,请联系管理员");
                        loader.hide();
                    }
                });

                layer.msg(
                    "发送成功！",
                    {time: 2000}
                )

                time(this);
            }
        };
        function time(o) {
            if (wait == 0) {
                wait=60;
                var company = document.getElementById('text');
                company.style.marginLeft = '160px';
                $(".btn1").text("| 重新获取");
            }else{
                var company = document.getElementById('text');
                company.style.marginLeft = '130px';
                $(".btn1").text("| "+wait + "秒后重新获取");
                wait--;
                setTimeout(function () {
                    time(o)
                }, 1000)
            }
        }*/
    </script>

</head>
<!-- END HEAD -->

<body class=" login">
<div class="menu-toggler sidebar-toggler"></div>
<!-- END SIDEBAR TOGGLER BUTTON -->
<!-- BEGIN LOGO -->
<!-- END LOGO -->
<!-- BEGIN LOGIN -->
<div style="background-repeat:no-repeat;height: 720px;">
    <div class="content" style="width: 320px !important;margin-top:150px;background: white">
        <!-- BEGIN LOGIN FORM -->
        <form id="loginForm" class="login-form form-horizontal" method="post">
            <h3 class="form-title " style="color:#1676FF">修改密码</h3>
            <div class="form-group" style="margin-right: 0; margin-left: 5px">
                在下方输入你的修改信息。
            </div>
            <div class="form-group" style="margin-right: 0; margin-left: 0">
                <!--ie8, ie9 does not support html5 placeholder, so we just show field title for that-->
                 <input style="background-color:#f8f8f8" class="form-control form-control-solid placeholder-no-fix" type="text" value="${userName}" id="userName" name="userName" readonly/>
                 <#--<div class="text" id="text" onclick="getTime()"><span class="btn1" style="border-width:0px!important;background-color:rgba(0,0,0,0)!important;">| 获取验证码</span></div>-->
            </div>
            <#--<div class="form-group" style="margin-right: 0; margin-left: 0;margin-bottom: 0px!important;">
                <!--ie8, ie9 does not support html5 placeholder, so we just show field title for that&ndash;&gt;
                <input style="background-color:#f8f8f8" class="form-control form-control-solid placeholder-no-fix" type="text" maxlength="6" autocomplete="off" placeholder="请输入验证码" id="verification" name="verification"/>
                <span style="color: #1676FF; font-size: 12px">验证码会发送到您的注册邮箱。</span>
            </div>-->
            <div class="form-group" style="margin-right: 0; margin-left: 0">
                <!--ie8, ie9 does not support html5 placeholder, so we just show field title for that-->
                <input style="background-color:#f8f8f8" class="form-control form-control-solid placeholder-no-fix" type="text" autocomplete="off" maxlength="12" placeholder="请输入原密码" id="originalPwd" name="originalPwd"/>
            </div>
            <div class="form-group" style="margin-right: 0; margin-left: 0;margin-bottom: 0px!important;">
                <!--ie8, ie9 does not support html5 placeholder, so we just show field title for that-->
                <input style="background-color:#f8f8f8" class="form-control form-control-solid placeholder-no-fix" type="text" autocomplete="off" maxlength="12" placeholder="请输入新密码" id="newPwd" name="newPwd"/>
                <span style="color: #1676FF; font-size: 12px">密码需在6-12位且包含字母、数字。</span>
            </div>
            <div class="form-group" style="margin-right: 0; margin-left: 0">
                <!--ie8, ie9 does not support html5 placeholder, so we just show field title for that-->
                <input style="background-color:#f8f8f8" class="form-control form-control-solid placeholder-no-fix" type="text" autocomplete="off" maxlength="12" placeholder="请再次输入新密码" id="newPwdOnce" name="newPwdOnce"/>
            </div>

            <div id="msg"></div>
            <div class="form-actions" style="border-bottom:0px">
                <button type="submit" class="btn uppercase" style="width: 40%!important;background-color:#1676FF;color: #ffffff">提交</button>
                <button type="button" class="btn uppercase" style="margin-left:45px;width: 40%!important;background-color: #7F7F7F;color: #FFFFFF;" onclick="javascript :history.back(-1);">返回</button>
            </div>
        </form>

    </div>
</div>
<div class="copyright">2022 © Shanghai Kedong Network Technology Co., Ltd
</div>

</body>

</html>