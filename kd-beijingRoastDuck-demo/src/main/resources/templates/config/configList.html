<!--[if IE 8]> <html lang="en" class="ie8 no-js"> <![endif]-->
<!--[if IE 9]> <html lang="en" class="ie9 no-js"> <![endif]-->
<!--[if !IE]><!-->
<html lang="en">
    <!--<![endif]-->
<head>
<meta charset="utf-8" />
<title>应用信息</title>
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta content="width=device-width, initial-scale=1" name="viewport" />
<meta content="" name="description" />
<meta content="" name="author" />
<#include "/common/cssResource.html">
<#include "/common/jsResource.html">
<script type="text/javascript">

function page(n,s){
	$("#pageNum").val(n);
	$("#pageSize").val(s);
	$("#searchForm").submit();
	return false;
}

function openNew(){
	var openWindowWidth = $(document).width() * 0.8 + "px";
	var offsetH = ($(window).height()/5 - 20 > 120 ? 120 : $(window).height()/5 - 20) + "px";
	layer.open({
		type: 2,
		area: openWindowWidth,
		offset : offsetH,
		fix: false, //不固定
		maxmin: true,
		content: '${ctx}/configController/form?operationType=new',
		success: function(layero, index){
		    layer.iframeAuto(index);
		}
	});
}

function openEdit(configId){
	var openWindowWidth = $(document).width() * 0.8 + "px";
	var offsetH = ($(window).height()/5 - 20 > 120 ? 120 : $(window).height()/5 - 20) + "px";
	layer.open({
		type: 2,
		area: openWindowWidth,
		offset : offsetH,
		fix: false, //不固定
		maxmin: true,
		content: '${ctx}/configController/form?id='+configId+'&operationType=edit',
		success: function(layero, index){
		    layer.iframeAuto(index);
		}
	});
}

</script>

</head>
<body>
	<!-- BEGIN PAGE BASE CONTENT -->
	<div class="row">
		<div class="col-sm-12">
			<!-- BEGIN EXAMPLE TABLE PORTLET-->
			<div class="portlet light portlet-fit bordered">
				<div class="portlet-title">
					<ul class="page-breadcrumb breadcrumb">
						<li><a>企业服务台</a></li><i class="fa fa-circle"></i></li>
						<li><a>系统管理</a></li><i class="fa fa-circle"></i></li>
						<li><span class="active">配置管理</span></li>
					</ul>
				</div>
				<div class="portlet-body">
					<div class="table-toolbar">
						<div class="row">
							<form id="searchForm" action="${ctx}/configController/configList" method="post">
								<input id="pageNum" name="pageNum" type="hidden" value="${page.pageNum}" />
								<input id="pageSize" name="pageSize" type="hidden" value="${page.pageSize}" />

								<div class="col-sm-11">
									<div class="col-sm-4">
										<div class="form-group">
											<label class="control-label col-sm-2" style="padding-right: 0">键：</label>
											<div class="col-sm-8" style="padding-left: 0;">
												<input type="text" class="form-control" name="ckey"
													   value="${configReq.ckey}" />
											</div>
										</div>
									</div>
									<div class="col-sm-4">
										<div class="form-group">
											<label class="control-label col-sm-4" style="padding-right: 0">值：</label>
											<div class="col-sm-8" style="padding-left: 0;">
												<input type="text" class="form-control" name="cvalue"
													   value="${configReq.cvalue}" />
											</div>
										</div>
									</div>
								</div>
								<div class="col-sm-12">
									<div class="btn-group pull-right">
										<button type="submit" class="btn green" >查询</button><div class="col-sm-1"></div>
										<button type="button" id="sample_editable_1_new" class="btn green" onclick="openNew()">新增</button>
									</div>
								</div>
							</form>
						</div>
					</div>
					<table class="table table-striped table-bordered table-hover table-header-fixed"
						id="configTable">
						<thead>
							<tr>
								<th>键</th>
								<th>值</th>
								<th>状态</th>
								<th>描述</th>
								<th>操作</th>
							</tr>
						</thead>
						<tbody>
							<#list page.list as config>
								<tr>
									<td class="col-sm-3" style="word-wrap:break-word;word-break:break-all;">${config.ckey}</td>
									<td class="col-sm-3" style="word-wrap:break-word;word-break:break-all;">${config.cvalue}</td>
									<td class="col-sm-1"><#if config.status = 1> 有效 <#else> 无效 </#if></td>
									<td class="col-sm-3" style="word-wrap:break-word;word-break:break-all;">${config.remark}</td>
									<td class="col-sm-2">
										<a href="#" onclick="openEdit('${config.id}')">编辑</a>
										<a href="${ctx}/configController/deleteConfig?id=${config.id}" onclick="return confirm('确认要执行 删除 操作吗？', this.href)">删除</a>
									</td>
								</tr>
							</#list>
						</tbody>
					</table>
				</div>
			</div>
			<!-- END EXAMPLE TABLE PORTLET-->
		</div>
	</div>
	<!-- END PAGE BASE CONTENT -->
	<@sc.pagination page=page />

	<form id="searchForm" action="${ctx}/configController/configList" method="post">
		<input id="pageNum" name="pageNum" type="hidden" value="${page.pageNum}" />
		<input id="pageSize" name="pageSize" type="hidden" value="${page.pageSize}" />
	</form>
</body>
</html>