<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!-- 上述3个meta标签*必须*放在最前面，任何其他内容都*必须*跟随其后！ -->
    <meta name="description" content="">
    <meta name="author" content="">
    <!-- Custom styles for this template -->
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <#--widget-->


    <script type="text/javascript">
        function iframeH() {
            var ifm = parent.document.getElementById("contentFrame");
            if (ifm) {
                var ifmH = $(ifm).height()
                var h = $(document.body).height() + 120;
                if (ifmH < h) {
                    parent.document.getElementById("contentFrame").height = h;
                }
            }
        }

        var layerTop = top.layer;

        const loader =  new Loaders({style:"rectangle"});
        $(document).ready(function () {

            $(".logListInfo").on("click", ".rowInfo", function (e) {
                e.stopPropagation();
                console.log('hhhh')
                let icon = $(this).children(1).children(1);
                var iconClass = icon.attr("class");
                if (iconClass == 'icon-plus') {
                    icon.attr("class", "minus");
                    $(this).next().show();
                }
                if (iconClass == 'minus') {
                    icon.attr("class", "icon-plus");
                    $(this).next().hide();
                }
            });

            $(".block-head-label").on("click", "input[type='checkbox']", function (e) {
                var val = $(this).is(":checked");
                if (val) {
                    $(".rowInfo").each(function(){
                        $(this).show();
                    });
                } else {
                    $(".detailsInfo").hide();
                    $(".rowInfo").each(function(){
                        if ($(this).find("td:eq(3)").text().trim() == "系统") {
                            $(this).hide();
                        }
                    });
                }
                iframeH();
            });

            freshStatistics("0");

            parent.checkVerifCaseStatusIsError('${claimCase.id}','abx10');
        });

        function checkVerifCaseStatusIsError(caseId,status){
            if(caseId != undefined && caseId.trim()!=""){
                var isError = false;
                setInterval(function () {
                    if(!isError){
                        var formData = new FormData();
                        formData.append("claimCaseId", caseId);
                        $.ajax({
                            url: "${ctx}/insuranceCaseController/getCaseInfo",
                            type: 'POST',
                            data: formData,
                            async: true,
                            cache: false,
                            contentType: false,
                            processData: false,
                            success: function (data) {
                                var result = eval("(" + data + ")");
                                if (result.ret == "0") {
                                    if(result.msg != status){
                                        isError = true;
                                        layer.msg("案件状态已修改！！！",{icon: 2,time:-1,shade: [0.5, '#000000']})
                                    }
                                } else {
                                    console.log(data);
                                }
                            },
                            error: function (data) {
                                console.log(data);
                            }
                        });}
                },5000)

            }
        }


        //下载保单
        function onloadEPolicy(policyPersonId) {
            if (typeof policyPersonId == 'undefined' || policyPersonId == '') {
                layerTop.msg('暂未数据！！！', {
                    icon: 2,
                    time: 2000
                });
                return;
            }

            var formData = new FormData();
            formData.append("policyPersonId", policyPersonId);
            $.ajax({
                url: "${ctx}/insuranceCaseController/onloadEPolicy",
                type: 'POST',
                data: formData,
                async: true,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    var result = eval("(" + data + ")");
                    if (result.ret == "0") {
                        window.open(result.msg, "_blank");
                    } else {
                        layerTop.msg(result.msg, {
                            icon: 2,
                            time: 2000
                        });
                    }
                },
                error: function (data) {
                    var result = eval("(" + data + ")");
                    alert(result.msg);
                }
            });

        }

        //查看保险方案
        function seePlanName(planId) {
            if (typeof planId == 'undefined' || planId == '') {
                layerTop.msg('暂未数据！！！', {
                    icon: 2,
                    time: 2000
                });
                return;
            }

            var formData = new FormData();
            formData.append("planId", planId);
            $.ajax({
                url: "${ctx}/insuranceCaseController/seePlanNameByPlanId",
                type: 'POST',
                data: formData,
                async: true,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    var result = eval("(" + data + ")");
                    if (result.ret == "0") {
                        window.open(result.msg, "_blank");
                    } else {
                        layerTop.msg(result.msg, {
                            icon: 2,
                            time: 2000
                        });
                    }
                },
                error: function (data) {
                    var result = eval("(" + data + ")");
                    alert(result.msg);
                }
            });

        }

        //案件关闭
        function closeCase(caseId) {
            var content = '<div class="row" style="padding: 0px 30px" ><div class="col-sm-3">&nbsp;</div><textarea rows="5" class="col-sm-6 form-control" name="closeCaseMsg" id="closeCaseMsg" autocomplete="off" placeholder="请输入关闭案件原因"></textarea></div>';
            layer.open({
                type: 1,
                content: content,
                title: '关闭案件',
                area: ['500px', '300px'],
                btn: ['确认', '取消'],
                yes: function (index, obj) {
                    console.log(typeof $("#closeCaseMsg").val());
                    var closeCaseMsg = $("#closeCaseMsg").val();
                    if (typeof closeCaseMsg != 'string' || closeCaseMsg.trim() == '') {
                        layer.msg("关闭案件原因不能为空", {icon: 2, time: 3000, offset: 'r'});
                    } else {
                        var formData = new FormData();
                        formData.append("claimCaseId", caseId);
                        formData.append("type", 102);
                        formData.append("description", closeCaseMsg);
                        $.ajax({
                            url: "${ctx}/insuranceCaseController/closeCase",
                            type: 'POST',
                            data: formData,
                            async: false,
                            cache: false,
                            contentType: false,
                            processData: false,
                            success: function (data) {
                                var result = eval("(" + data + ")");
                                if (result.ret == "0") {
                                    loader.show();
                                    setTimeout(function () {
                                        layer.msg('关闭案件成功', {
                                            icon: 1,
                                            time: 1000 //1秒关闭（如果不配置，默认是3秒）
                                        }, function () {
                                            window.location.href = "${ctx}/insuranceCaseController/claimCaseList";
                                        });
                                    }, 1200);
                                } else {
                                    layer.msg(result.msg, {
                                        icon: 1,
                                        time: 1500 //1秒关闭（如果不配置，默认是3秒）
                                    }, function (index) {
                                        layer.close(index);
                                    });
                                }
                            },
                            error: function (data) {
                                var result = eval("(" + data + ")");
                                alert(result.msg);
                            }
                        });
                    }
                }
            });
        }

        //跳过审核
        function skipClaimVerify(caseId) {
            var content = '<div class="row" style="padding: 0px 30px" ><div class="col-sm-3">&nbsp;</div><textarea rows="5" class="col-sm-6 form-control" name="skipCaseReason" id="skipCaseReason" autocomplete="off" placeholder="请输入跳过原因"></textarea></div>';
            layer.open({
                type: 1,
                content: content,
                title: '案件审核跳过',
                area: ['500px', '300px'],
                btn: ['确认', '取消'],
                yes: function (index, obj) {
                    console.log(typeof $("#skipCaseReason").val());
                    var closeCaseMsg = $("#skipCaseReason").val();
                    if (typeof closeCaseMsg != 'string' || closeCaseMsg.trim() == '') {
                        layer.msg("跳过原因不能为空", {icon: 2, time: 3000, offset: 'r'});
                    } else {
                        var formData = new FormData();
                        formData.append("claimCaseId", caseId);
                        formData.append("skipReason", closeCaseMsg);
                        $.ajax({
                            url: "${ctx}/insuranceCaseController/skipClaimVerify",
                            type: 'POST',
                            data: formData,
                            async: false,
                            cache: false,
                            contentType: false,
                            processData: false,
                            success: function (data) {
                                var result = eval("(" + data + ")");
                                if (result.ret == "0") {
                                    loader.show();
                                    setTimeout(function () {
                                        layer.msg('跳过案件成功', {
                                            icon: 1,
                                            time: 1000 //1秒关闭（如果不配置，默认是3秒）
                                        }, function () {
                                            window.location.href = "${ctx}/insuranceCaseController/claimCaseList";
                                        });
                                    }, 1200);
                                } else {
                                    layer.msg(result.msg, {
                                        icon: 1,
                                        time: 1500 //1秒关闭（如果不配置，默认是3秒）
                                    }, function (index) {
                                        layer.close(index);
                                    });
                                }
                            },
                            error: function (data) {
                                var result = eval("(" + data + ")");
                                alert(result.msg);
                            }
                        });
                    }
                }
            });
        }

        //出险人身份证影像
        function seeTreatIdTypeAttach(claimCaseId) {
            var openWindowWidth = $(document).width() * 0.8 + "px";
            var offsetH = ($(window).height() / 5 - 20 > 120 ? 120 : $(window).height() / 5 - 20) + "px";
            layer.open({
                type: 2,
                title: '出险人身份证影像信息',
                area: openWindowWidth,
                offset: offsetH,
                fix: false, //不固定
                maxmin: true,
                content: "${ctx}/insuranceCaseController/claimCaseAttachFile?claimCaseId=" + claimCaseId,
                success: function (layero, index) {
                    layer.iframeAuto(index);
                }
            });
        }

        //补发短信
        function reSendMessage(claimCaseId) {
            layer.open({
                title: "补发短信",
                type: 1,
                content: $('#reSendMessageContainer'),
                area: ['600px', '300px'],
                fixed: false,
                offset: 't',
                btn: ['确认', '取消'],
                closeBtn: 0,
                yes: function (index, layero) {
                    let check = $('#reSendMessageContainer').find('span.checked');
                    let children = check.children();
                    let code = children.attr("code");
                    if (typeof code == "undefined" || code == null) {
                        layerTop.msg("请选择短信类型！！！", {
                            icon: 2,
                            time: 2000
                        });
                        return;
                    }
                    let reissueReason = $("#reissueReason").val();
                    if (reissueReason.trim() == "") {
                        layer.msg("请输入补发原因！！！", {
                            icon: 2,
                            time: 2000
                        });
                        return;
                    }
                    var formData = new FormData();
                    formData.append("claimCaseId", claimCaseId);
                    formData.append("messageCode", code);
                    formData.append("reissueReason", reissueReason);
                    $.ajax({
                        url: "${ctx}/insuranceCaseController/reSendMessage",
                        type: 'POST',
                        data: formData,
                        async: true,
                        cache: false,
                        contentType: false,
                        processData: false,
                        success: function (data) {
                            var result = eval("(" + data + ")");
                            if (result.ret == "0") {
                                layerTop.msg("发送成功", {
                                    icon: 1,
                                    time: 2000
                                }, function () {
                                    layer.closeAll();
                                });
                            } else {
                                layerTop.msg(result.msg, {
                                    icon: 2,
                                    time: 2000
                                });
                            }
                        },
                        error: function (data) {
                            var result = eval("(" + data + ")");
                            alert(result.msg);
                        }
                    });
                },
                btn2: function (index, layero) {
                    layer.closeAll();
                }
            });
        }

        //催办记录
        function pressDoLog() {
            layerTop.msg("待建设！！！", {
                icon: 3,
                time: 2000
            });
        }

        //修改估损金额
        function modifyAppraisalAmount(claimCaseId) {
            var openWindowWidth = $(document).width() * 0.5 + "px";
            var offsetH = ($(window).height() / 5 - 20 > 120 ? 120 : $(window).height() / 5 - 20) + "px";
            layer.open({
                type: 2,
                title: '修改估损金额',
                area: openWindowWidth,
                offset: offsetH,
                fix: false, //不固定
                maxmin: true,
                content: "${ctx}/insuranceCaseController/modifyAppraisalAmount?claimCaseId=" + claimCaseId,
                success: function (layero, index) {
                    layer.iframeAuto(index);
                }
            });
        }

        function changeAppraisalAmount(money) {
            $("#appraisalAmount").html('估损金额（旧版）：' + money + '元');
        }

        //添加日志
        function addCaseLog(claimCaseId) {
            let areaMsg = $("#logTextArea").val().trim();
            if (areaMsg == '') {
                layer.tips("请添加日志描述！！", '#logTextArea', {tips: 1});
                return;
            }

            var formData = new FormData();
            formData.append("claimCaseId", claimCaseId);
            formData.append("description", areaMsg);
            $.ajax({
                url: "${ctx}/insuranceCaseController/addCaseLog",
                type: 'POST',
                data: formData,
                async: true,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    var result = eval("(" + data + ")");
                    if (result.ret == "0") {
                        let logMsg = result.data;

                        console.log(logMsg);
                        console.log();
                        $(".logListInfo tbody").prepend(`
                        <tr class="rowInfo">
                                <td width="10%" align="center">
                                    <div class="icon-plus"></div>
                                </td>
                                <td>` + logMsg.position + `</td>
                                <td>` + logMsg.status + `</td>
                                <td>` + logMsg.creator.substring(0,logMsg.creator.indexOf("-")) + `</td>
                                <td>` + timeStamp2String(logMsg.createTime) + `</td>
                            </tr>
                            <tr class="detailsInfo">
                                <td></td>
                                <td colspan="4">` + logMsg.description + `</td>
                            </tr>
                        `);
                        iframeH();
                    } else {
                        console.log(data);
                    }
                },
                error: function (data) {
                    var result = eval("(" + data + ")");
                    console.log(data);
                }
            });

            console.log(areaMsg);
        }

        //标记是否疑难
        function markDiffcultCase(e) {
            scrollTop = calculationScrollTop();
            let difficultCase = "${claimCase.isDifficultCase}";
            /*var X = $(obj).offset().top;  //获取当前元素x坐标
            var Y = $(obj).offset().left; //获取当前元素y坐标*/
            if (difficultCase + "" != 1) {
                var content = '<div class="row" style="padding: 0px 30px" ><div class="col-sm-3">&nbsp;</div><textarea rows="5" class="col-sm-6 form-control"  id="markDiffcultCaseMsg" autocomplete="off" placeholder="请输入挂起原因"></textarea></div>';
                layer.open({
                    type: 1,
                    content: content,
                    title: '案件挂起',
                    area: ['500px', '300px'],
                    btn: ['确认', '取消'],
                    offset: scrollTop,
                    yes: function (index, obj) {
                        console.log(typeof $("#markDiffcultCaseMsg").val());
                        var hangCaseMsg = $("#markDiffcultCaseMsg").val();
                        if (typeof hangCaseMsg != 'string' || hangCaseMsg.trim() == '') {
                            layer.msg("挂起原因不能为空", {icon: 2, time: 3000, offset: scrollTop});
                        } else {
                            var formData = new FormData();
                            formData.append("claimCaseId", "${claimCase.id}");
                            formData.append("type", 7);
                            formData.append("isSkip", 1);
                            formData.append("description", hangCaseMsg);
                            $.ajax({
                                url: "${ctx}/insuranceCaseController/markDiffcultCase",
                                type: 'POST',
                                data: formData,
                                async: false,
                                cache: false,
                                contentType: false,
                                processData: false,
                                success: function (data) {
                                    var result = eval("(" + data + ")");
                                    if (result.ret == "0") {
                                        layer.msg('案件挂起成功', {
                                            icon: 1,
                                            time: 1500,
                                            offset: scrollTop
                                        }, function () {
                                            window.location.reload();
                                        });
                                    } else {
                                        layer.msg(result.msg, {
                                            icon: 2,
                                            time: 3000 ,
                                            offset: scrollTop
                                        }, function (index) {
                                            layer.close(index);
                                        });
                                    }
                                },
                                error: function (data) {
                                    var result = eval("(" + data + ")");
                                    alert(result.msg);
                                }
                            });
                        }
                    }
                });
            } else {
                layer.confirm(
                    '是否取消疑难?', {icon: 2, title: '执行',offset: scrollTop}, function (index) {
                        var formData = new FormData();
                        formData.append("claimCaseId", "${claimCase.id}");
                        formData.append("type", 8);
                        $.ajax({
                            url: "${ctx}/insuranceCaseController/markDiffcultCase",
                            type: 'POST',
                            data: formData,
                            async: false,
                            cache: false,
                            contentType: false,
                            processData: false,
                            success: function (data) {
                                var result = eval("(" + data + ")");
                                if (result.ret == "0") {
                                    layer.msg('取消成功', {
                                        icon: 1,
                                        time: 1500,
                                        offset: scrollTop
                                    }, function () {
                                        window.location.reload();
                                    });
                                } else {
                                    layer.msg(result.msg, {
                                        icon: 1,
                                        time: 3000,
                                        offset: scrollTop
                                    }, function (index) {
                                        layer.close(index);
                                    });
                                }
                            },
                            error: function (data) {
                                var result = eval("(" + data + ")");
                                alert(result.msg);
                            }
                        });

                    }
                )
            }

            let logText = $("#logTextArea").val();
            if(logText != undefined && logText.trim()!=""){
                addCaseLog("${claimCase.id}");
            }

        }
        // 计算滚动高度
        function calculationScrollTop() {
            var ifm = parent.document.getElementById("contentFrame");
            scrollTop = $(window.parent).scrollTop() + 150;
            if (!ifm) {
                scrollTop = 300;
            }
            return scrollTop + "px";
        }

        //再次来电切换
        function changeCustomerTask(e) {
            scrollTop = calculationScrollTop();
            var formData = new FormData();
            formData.append("claimCaseId", "${claimCase.id}");
            if ($(e).text().trim() == "再次去电") {
                formData.append("phoneType", 0);
            }
            if ($(e).text().trim() == "去电完成") {
                formData.append("phoneType", 1);
            }
            formData.append("type", 102);
            layer.closeAll();
            layer.msg("提交中..",{
                icon:16,
                time:-1,
                shade: [0.5, '#ffffff'],
                offset: scrollTop
            })
            console.log(formData.get("phoneType"));
            $.ajax({
                url: "${ctx}/insuranceCaseController/againPhone",
                type: 'POST',
                data: formData,
                async: true,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    var result = eval("(" + data + ")");

                    setTimeout(function(){
                        layer.msg(result.msg, {icon: 1, time: 2000, offset: scrollTop}, function (index) {
                            if(result.ret=='0'){
                                window.location.reload();
                            }else {
                                layer.close(index);
                            }
                        });
                    }, 1000);
                },
                error: function (data) {
                    var result = eval("(" + data + ")");
                    alert(result.msg);
                }
            });
            let logText = $("#logTextArea").val();
            if(logText != undefined && logText.trim()!=""){
                addCaseLog("${claimCase.id}");
            }
        }



        function modifySubjectRemark(paramMap) {
            let subjectId = paramMap.get("subjectId");
            let type = paramMap.get("type");
            let data = paramMap.get("data");
            let children = $("div[sujectArea='" + subjectId + "']").children();
            children.html(data);
            switch (type) {
                case "verify":
                    children.attr("class", "notice notice-deal");
                    break;
                case "hangUp":
                    children.attr("class", "notice notice-hangup");
                    break;
            }
        }

        function getDutyAeraInfo(claimCaseSubjectId) {
            var formData = new FormData();
            formData.append("claimCaseSubjectId", claimCaseSubjectId);
            $.ajax({
                url: "${ctx}/insuranceCaseController/getDutyAeraInfo",
                type: 'POST',
                data: formData,
                async: false,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    var result = eval("(" + data + ")");
                    let paramMap = new Map();
                    paramMap.set("subjectId",claimCaseSubjectId);
                    paramMap.set("data",result.msg);
                    if(result.ret=="0"){
                        paramMap.set("type","verify");
                    }
                    modifySubjectRemark(paramMap);
                },
                error: function (data) {
                    console.log(data);
                }
            });
        }

        function subjectVerify(subjectId) {
            var openWindowWidth = $(document).width() + "px";
            var openWindowHeight = $(document).height() + "px";
            layer.open({
                type: 2,
                title: '出险人身份证影像信息',
                area: [openWindowWidth, openWindowHeight],
                offset: 't',
                fix: false, //不固定
                maxmin: true,
                shade: ["0.3","#000000"],
                shadeClose: true,
                content: "${ctx}/insuranceCaseController/claimSubjectVerify?claimCaseSubjectId=" + subjectId,
                success: function (layero, index) {
                    layer.iframeAuto(index);
                }
            });
        }


        //时间格式化
        function timeStamp2String(time) {
            var datetime = new Date();
            datetime.setTime(time);
            var year = datetime.getFullYear();
            var month = datetime.getMonth() + 1 < 10 ? "0" + (datetime.getMonth() + 1) : datetime.getMonth() + 1;
            var date = datetime.getDate() < 10 ? "0" + datetime.getDate() : datetime.getDate();
            var hour = datetime.getHours() < 10 ? "0" + datetime.getHours() : datetime.getHours();
            var minute = datetime.getMinutes() < 10 ? "0" + datetime.getMinutes() : datetime.getMinutes();
            var second = datetime.getSeconds() < 10 ? "0" + datetime.getSeconds() : datetime.getSeconds();
            return year + "-" + month + "-" + date + " " + hour + ":" + minute + ":" + second;
        }


        function freshStatistics(type) {
            let baseUserId = "${claimCase.baseUserId}";
            let formData = new FormData();
            formData.append("baseUserId", baseUserId)
            $.ajax({
                url: "${ctx}/insuranceCaseController/getBaseUserStatistics",
                type: 'POST',
                data: formData,
                async: false,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    var result = eval("(" + data + ")");
                    if (result.ret == "0") {
                        if (type == "1") {
                            layer.msg("刷新成功！！", {
                                icon: 1,
                                time: 1000
                            });
                        }
                        $("#lastStatisticsData").html(result.lastStatisticsData);
                        $("#times").html(result.times + "次");
                        $("#sumTimesRate").html(result.sumTimesRate + "%");
                        $("#continuousTimes").html(result.continuousTimes + "次");
                        $("#sumNear30TimesRate").html(result.sumNear30TimesRate + "%");
                        $("#near30DayTimes").html(result.near30DayTimes + "次");
                        $("#lastTimePolicyPersonDate").html(result.lastTimePolicyPersonDate);
                        $("#lastPolicyPersonDate").html(result.lastPolicyPersonDate);
                    } else {
                        if (type == "1") {

                        }
                    }
                },
                error: function (data) {
                    if (type == "1") {
                        var result = eval("(" + data + ")");
                        alert(result.msg);
                    }
                }
            });
        }

        function getHistoryRecord(baseUserId) {
            if (baseUserId == '') {
                layerTop.msg("暂无信息！！！", {icon: 2, time: 3000});
                return;
            }
            var openWindowWidth = $(document).width() * 0.8 + "px";
            var offsetH = ($(window).height() / 5 - 20 > 120 ? 120 : $(window).height() / 5 - 20) + "px";
            layer.open({
                type: 2,
                title: '历史保单',
                area: openWindowWidth,
                offset: offsetH,
                fix: false, //不固定
                maxmin: true,
                content: "${ctx}/insuranceCaseController/getHistoryRecord?baseUserId=" + baseUserId,
                success: function (layero, index) {
                    layer.iframeAuto(index);
                }
            });
        }

        //案件提交
        function subimitAll(caseId) {
            layer.closeAll()
            let indexLoad = layer.msg("提交中..",{
                icon:16,
                time:-1,
                shade: [0.5, '#ffffff']
            })
            let caseExInfo = {};
            if(localStorage.getItem("firstEventType")!=null && localStorage.getItem("firstEventType")!=undefined){
                let item = localStorage.getItem("firstEventType");
                caseExInfo["firstEventType"]=item;
            }
            if(localStorage.getItem("eventRespConfirm")!=null && localStorage.getItem("eventRespConfirm")!=undefined){
                let item = localStorage.getItem("eventRespConfirm");
                caseExInfo["eventRespConfirm"]=item;
            }
            if(localStorage.getItem("accidentType")!=null && localStorage.getItem("accidentType")!=undefined){
                let item = localStorage.getItem("accidentType");
                caseExInfo["accidentType"]=item;
            }
            if(localStorage.getItem("knightlllegalItems")!=null && localStorage.getItem("knightlllegalItems")!=undefined){
                let item = localStorage.getItem("knightlllegalItems");
                caseExInfo["knightlllegalItems"]=item;
            }
            if(localStorage.getItem("exceptionLabel")!=null && localStorage.getItem("exceptionLabel")!=undefined){
                let item = localStorage.getItem("exceptionLabel");
                caseExInfo["exceptionLabel"]=item;
            }
            if(localStorage.getItem("callPolice")!=null && localStorage.getItem("callPolice")!=undefined){
                let item = localStorage.getItem("callPolice");
                caseExInfo["callPolice"]=item;
            }
            localStorage.removeItem("firstEventType");
            localStorage.removeItem("eventRespConfirm");
            localStorage.removeItem("accidentType");
            localStorage.removeItem("knightlllegalItems");
            localStorage.removeItem("exceptionLabel");
            localStorage.removeItem("callPolice");
            let formData = new FormData();
            formData.append("claimCaseExInfo",JSON.stringify(caseExInfo));
            formData.append("claimCaseId",caseId);
            $.ajax({
                url: "${ctx}/insuranceCaseController/caseAllSubmit",
                type: 'POST',
                data: formData,
                async: true,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    var result = eval("(" + data + ")");
                    if (result.ret == "0") {
                        let i =setInterval(function () {
                            let formData1 = new FormData();
                            formData1.append("claimCaseId",caseId);
                            $.ajax({
                               url: "${ctx}/insuranceCaseController/getCaseIsVerify",
                                type: 'POST',
                                data: formData1,
                                async: false,
                                cache: false,
                                contentType: false,
                                processData: false,
                                success: function (data1) {
                                    var result = eval("(" + data1 + ")");
                                    console.log(data1);
                                    if (result.ret == "0" || result.ret == "verifyErrorReason") {
                                        var openWindowWidth = $(document).width() * 0.8 + "px";
                                        var offsetH = ($(window).height() / 5 - 20 > 120 ? 120 : $(window).height() / 5 - 20) + "px";
                                        layer.open({
                                            type: 2,
                                            title: '查看案件理算结果',
                                            area: openWindowWidth,
                                            offset: offsetH,
                                            fix: false, //不固定
                                            maxmin: true,
                                            content: "${ctx}/insuranceCaseController/getCaseVerifyResult?claimCaseId="+caseId,
                                            success: function (layero, index) {
                                                layer.iframeAuto(index);
                                                layer.close(indexLoad);
                                            }
                                        });
                                        clearInterval(i);
                                    }
                                    if(result.ret == "errorVerify"){
                                        layer.msg(result.msg,{icon: 2,time:-1});
                                        clearInterval(i);
                                    }

                                },
                                error: function (data) {
                                    console.log(data)
                                    clearInterval(i);
                                }
                            });
                        },2000);
                    } else {
                        layerTop.msg(result.msg, {
                            icon: 2,
                            time: 2000
                        });
                    }
                },
                error: function (data) {
                    var result = eval("(" + data + ")");
                    alert(result.msg);
                }
            });

            let logText = $("#logTextArea").val();
            if(logText != undefined && logText.trim()!=""){
                addCaseLog(caseId);
            }
        }

        //查看人员详情
        function viewPersonDetail(policyPersonId) {
            console.log(policyPersonId);
            if (policyPersonId.trim() == "") {
                layer.msg("暂无人员信息", {icon: 2, time: 2000, offset:  "100px"}, function (index) {
                    layer.close(index);
                });
                return;
            }
            window.open("${ctx}/personnelManagementController/personnelManagementDetail?policyPersonId="+policyPersonId);
        }

        //补充材料页面
        function supplementaryMaterials() {
            let applyMobile=`${claimCase.applyMobile}`;
            $("#supplementaryMobile").val(applyMobile);
            $("#selfBookMobile").val(applyMobile);
            const mobileCheck = /^1[3456789][0-9]\d{8}$/;
            $("#supplementaryMobile").on("blur",function () {
                if (!$(this).val() || !(mobileCheck.test($(this).val()))) {
                    layer.tips("请填写合法的手机号码",$(this),{icon: 2,time: 2000,tips:1});
                    $(this).val("");
                }
            });
            $("#selfBookMobile").on("blur",function () {
                if (!$(this).val() || !(mobileCheck.test($(this).val()))) {
                    layer.tips("请填写合法的手机号码",$(this),{icon: 2,time: 2000,tips:1});
                    $(this).val("");
                }
            });
            layer.open({
                title: "补充材料",
                type: 1,
                content: $('#supplementaryMaterialsContainer'),
                area: ['600px', '300px'],
                fixed: false,
                offset: 't',
                closeBtn: 0,
                shadeClose: true,
                yes: function (index, layero) {

                },
                btn2: function (index, layero) {
                    layer.closeAll();
                }
            });

            let logText = $("#logTextArea").val();
            if(logText != undefined && logText.trim()!=""){
                addCaseLog("${claimCase.id}");
            }
        }

        //影像补材提交
        function supplementaryMaterialsAction() {
            let check = $('#supplementaryMaterialsContainer').find('span.checked');
            let sendData = "";
            $.each(check, function (index, obj) {
                let code = $(this).children().attr("code");
                sendData += "," + code;
            });
            if (sendData == "") {
                layerTop.msg("请选择补充材料类型！！！", {
                    icon: 2,
                    time: 2000
                });
                return;
            }
            let supplementaryReason = $("#supplementaryReason").val();
            if (supplementaryReason.trim() == "") {
                layerTop.msg("请输入补材原因！！！", {
                    icon: 2,
                    time: 2000
                });
                return;
            }
            let claimCaseId = `${claimCase.id}`;
            var formData = new FormData();
            formData.append("claimCaseId", claimCaseId);
            formData.append("hangCode", JSON.stringify(sendData))
            formData.append("hangReason", supplementaryReason)
            formData.append("mobile", $.trim($("#supplementaryMobile").val()))
            $.ajax({
                url: "${ctx}/insuranceCaseController/supplementaryMaterials",
                type: 'POST',
                data: formData,
                async: true,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    var result = eval("(" + data + ")");
                    if (result.ret == "0") {
                        layerTop.msg(result.msg, {
                            icon: 1,
                            time: 3000
                        }, function () {
                            window.location.href="${ctx}/insuranceCaseController/caseDetail?caseId="+claimCaseId;
                        });
                    } else {
                        layerTop.msg(result.msg, {
                            icon: 2,
                            time: 2000
                        });
                    }
                },
                error: function (data) {
                    var result = eval("(" + data + ")");
                    alert(result.msg);
                }
            });
        }

        function selfBookAction() {
            let selfBookReason = $("#selfBookReason").val();
            if (selfBookReason.trim() == "") {
                layerTop.msg("请输入补充申请书原因！！！", {
                    icon: 2,
                    time: 2000
                });
                return;
            }
            let claimCaseId = `${claimCase.id}`;
            var formData = new FormData();
            formData.append("claimCaseId", claimCaseId);
            formData.append("hangReason", selfBookReason)
            formData.append("mobile", $.trim($("#selfBookMobile").val()))
            $.ajax({
                url: "${ctx}/insuranceCaseController/supplementarySelfBook",
                type: 'POST',
                data: formData,
                async: true,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    var result = eval("(" + data + ")");
                    if (result.ret == "0") {
                        layerTop.msg(result.msg, {
                            icon: 1,
                            time: 3000
                        }, function () {
                            window.location.href="${ctx}/insuranceCaseController/caseDetail?caseId="+claimCaseId;
                        });
                    } else {
                        layerTop.msg(result.msg, {
                            icon: 2,
                            time: 2000
                        });
                    }
                },
                error: function (data) {
                    var result = eval("(" + data + ")");
                    alert(result.msg);
                }
            });
        }

        function closeSupplementaryMaterialsContainer() {
            layer.closeAll();
        }

        function editDuty(claimCaseId) {
            var openWindowWidth = $(document).width() * 0.8 + "px";
            var offsetH = ($(window).height() / 5 - 20 > 120 ? 120 : $(window).height() / 5 - 20) + "px";
            layer.open({
                type: 2,
                title: '影像责任',
                area: [openWindowWidth,'800px'],
                offset: offsetH,
                fix: false, //不固定
                maxmin: true,
                shadeClose: true,
                content: "${ctx}/insuranceCaseController/editDuty?claimCaseId=" + claimCaseId,
                success: function (layero, index) {
                    layer.iframeAuto(index);
                }
            });
        }
    </script>
    <style>
        .layui-layer-msg {
            z-index: 1989101411 !important;
        }

        /*loader需要的遮罩层css start */
        #screen {
            height: 100%;
            width: 100%;
            z-index: 198910141;
            position: fixed;
            background: rgba(0,0,0,0.3);
            top:0;
        }

        #screen #screenLoading{
            margin: 0 auto;
            top: 70%;
            transform: translateY(-50%);
            background: greenyellow;
        }

        .form-group {
            margin-bottom: 10px !important;
        }

        .widget-thumb .widget-thumb-heading {
            font-size: 16px !important;
        }

        .caption-subject {
            font-weight: 500 !important;
        }

        .btn-bule {
            color: #FFFFFF;
            background-color: #1676FF;
            border-color: #1676FF;
            margin-left: 3%;
        }

        .btn-grey {
            color: #FFFFFF;
            background-color: #7f7f7f;
            border-color: #7f7f7f;
            margin-left: 3%;
        }

        .btn-orange {
            color: #FFFFFF;
            background-color: orange;
            border-color: orange;
            margin-left: 3%;
        }


        .btn-bule:hover {
            color: #FFFFFF;
        }

        .btn-grey:hover {
            color: #FFFFFF;
        }

        .btn-orange:hover {
            color: #FFFFFF;
        }

        .btn-light-bule {
            color: #3662EC;
            background-color: #DEEBFF;
            border-color: #DEEBFF;
            margin-left: 3%;
        }

        .btn-light-bule:hover {
            color: #3662EC;
        }

        .container-head {
            border-bottom-width: 3px !important;
            padding-left: 10% !important;
            padding-right: 10% !important;
        }

        .container-boby {
            padding-left: 5% !important;
            padding-right: 5% !important;
        }

        .block-show {
            display: flex;
        }

        .block-head-label {
            margin-bottom: 1%;
            font-size: 20px;
        }

        .block-head-label a {
            font-size: 15px;
            margin-left: 10px;
        }

        .block-head-label span {
            font-size: 15px;
        }

        .block-border {
            border-top: 3px solid #EFEFEF;
            border-bottom: 3px solid #EFEFEF;
            border-left: 3px solid #FFFFFF;
            border-right: 3px solid #EFEFEF;
            margin-text-outline: 1.5%;
            margin-bottom: 2%;
        }

        .block-border .col-sm-4 {
            padding: 0px 1px 0px 0px !important;
        }

        .block-border .col-sm-8 {
            padding: 0px 1px 0px 0px !important;
        }

        .block-border .col-sm-12 {
            padding: 0px 1px 0px 0px !important;
        }

        .left-min-5 {
            width: 41.66666%;
            padding-bottom: 2.5%;
            background-color: #EFEFEF;
            padding-top: 2.5%;
            text-align: center;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .line-left-min-5 {
            width: 41.66666%;
            border-top: 3px solid #EFEFEF;
            border-bottom: 3px solid #FFFFFF;
        }

        .right-min-7 {
            width: 58.33334%;
            padding-bottom: 2.5%;
            background-color: white;
            padding-top: 2.5%;
            padding-left: 10px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .line-right-min-7 {
            width: 58.33334%;
            border-top: 3px solid #FFFFFF;
            border-bottom: 3px solid #EFEFEF;
        }

        .left-mid-5 {
            width: 20.83333%;
            padding-bottom: 1.25%;
            background-color: #EFEFEF;
            padding-top: 1.25%;
            text-align: center;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .line-left-mid-5 {
            width: 20.83333%;
            border-top: 3px solid #EFEFEF;
            border-bottom: 3px solid #FFFFFF;
        }

        .right-mid-7 {
            width: 79.16667%;
            padding-bottom: 1.25%;
            background-color: #FFFFFF;
            padding-top: 1.25%;
            padding-left: 10px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .line-right-mid-7 {
            width: 79.16667%;
            border-top: 3px solid #FFFFFF;
            border-bottom: 3px solid #EFEFEF;
        }


        .left-max-5 {
            width: 13.88888%;
            padding-bottom: 0.833%;
            background-color: #EFEFEF;
            padding-top: 0.833%;
            text-align: center;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .line-left-max-5 {
            width: 13.88888%;
            border-top: 3px solid #EFEFEF;
            border-bottom: 3px solid #FFFFFF;
        }

        .right-max-7 {
            width: 86.11112%;
            padding-bottom: 0.833%;
            background-color: #FFFFFF;
            padding-top: 0.833%;
            padding-left: 10px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .line-right-max-7 {
            width: 86.11112%;
            border-top: 3px solid #FFFFFF;
            border-bottom: 3px solid #EFEFEF;
        }


        .attachInfo {
            border: 1px solid #D8D8D8;
            padding: 0px 3.5% 20px;
            margin-bottom: 2%;
        }

        .attachInfo label {
            margin-top: 20px;
        }

        .attachInfo img {
            width: 100%;
            height: 100%;
            border: 1px solid;
        }

        .attachInfo .col-sm-2 {
            margin-top: 20px !important;
        }

        .attachInfo .icon-attach {
            display: block;
            position: absolute;
            background-color: #D8D8D8;
            color: #709BF3;
            width: 20px;
            height: 25px;
            right: 15px;
            top: 0px;
            text-align: center;
            line-height: 25px;
            font-size: 10px;
        }

        .payDutyInfo {
            margin-bottom: 2%;
        }

        .payDutyInfo .col-sm-5 {
            display: block;
            margin-bottom: 20px;;
            font-weight: bold;
            font-size: 17px;
            height: 210px;
        }

        .payDutyInfo .notice {
            width: 100%;
            height: 100%;
            border-radius: 10px;
            color: white;
            padding: 2% 5% 2% 2%;
        }

        .notice-no-deal{
            background-color: #EE3232;
        }
        .notice-deal {
            background-color: #3662EC;
        }
        .notice-hangup{
            background-color: #F49929;
        }
        .notice-default{
            background-color: darkgrey;
        }

        .claim_subject_area:hover {
            cursor: pointer;
        }


        .logInfo {
            margin-bottom: 2%;
        }

        .logInfo button {
            margin-top: 20px;
            margin-right: 30px;
        }

        .logListInfo {
            margin-bottom: 2%;
        }

        .logListInfo table {
            border: 1px solid #C2C2C2;
        }

        .logListInfo .detailsInfo {
            display: none;
            padding-left: 8%;
        }

        .logListInfo .detailsInfo > td {
            word-wrap: break-word;
            word-break: break-all;
        }

        .logListInfo .rowInfo:hover {
            cursor: pointer;
        }

        .minus {
            border: 1px solid black;
            border-radius: 50%;
            width: 13px;
            height: 13px;
            position: relative;
            margin-top: 3%;
        }

        .minus::before {
            content: '';
            position: absolute;
            left: 2px;
            top: 5px;
            width: 7px;
            border-top: 1px solid;
        }

        #reSendMessageContainer .radio {
            margin-left: 0px;
        }

        .subject-span {
            display: inline-block;
            margin-right: 40px;
            margin-top: 12px;
        }

        .subject-text-overflow {
            display: inline-block;
            max-width: 50%;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            vertical-align:bottom;
        }

        .span-type {
            background: #0b94ea;
            color: #fff;
            font-size: 10px;
            border-radius: 5px;
            margin:3px;
            padding: 2px;
        }
    </style>
</head>

<body>
<#--补发短信-->
<div id="reSendMessageContainer" style="display: none;width: 100%;height: 100%">
    <div class="row" style="padding-top: 30px;margin: 0px 0px">
        <div class="col-sm-3">
            <span class="pull-right" style="font-size: 10px;color: #7f7f7f">选择短信类型：</span>
        </div>
        <div class="col-sm-9">
            <#if messageType?? && (messageType.keySet()?size>0)>
                <#list messageType.keySet() as key>
                    <div class="col-sm-3" style="padding-left: 0px;margin-bottom: 10px;display: flex">
                        <input name="messageType" style="left: 0px;right: 0px;margin-left: 0px;" code="${key}"
                               type="radio">${messageType.get(key)}
                    </div>
                </#list>
            </#if>
            <textarea class="form-control" id="reissueReason" rows="5" placeholder="请输入补发原因"></textarea>
        </div>
    </div>
</div>
<#--补充材料-->
<div id="supplementaryMaterialsContainer" style="display: none;width: 100%;height: 100%">
    <ul class="nav nav-tabs" role="tablist">
        <li role="presentation" class="active"><a href="#supplementaryMaterials" role="tab" data-toggle="tab">影像补材</a></li>
        <li role="presentation"><a href="#bankCard" role="tab" data-toggle="tab">补充理赔申请书</a></li>
    </ul>

    <div class="tab-content">
<#--        影像补材-->
        <div role="tabpanel" class="tab-pane active" id="supplementaryMaterials">
            <div class="row" style="padding-top: 30px;margin: 0px 0px">
                <div class="col-sm-3">
                    <span class="pull-right" style="font-size: 10px;color: #7f7f7f">手机号：</span>
                </div>
                <div class="col-sm-9">
                    <input type="text" id="supplementaryMobile"  class="form-control">
                </div>
            </div>
            <div class="row" style="padding-top: 30px;margin: 0px 0px">
                <div class="col-sm-3">
                    <span class="pull-right" style="font-size: 10px;color: #7f7f7f">选择补充材料类型：</span>
                </div>
                <div class="col-sm-9">
                    <#if supplementaryMaterials?? && (supplementaryMaterials.keySet()?size>0)>
                        <#list supplementaryMaterials.keySet() as key>
                            <div class="col-sm-3" style="padding-left: 0px;margin-bottom: 10px;display: flex">
                                <input name="labelType" style="left: 0px;right: 0px;margin-left: 0px;" code="${key}"
                                       type="checkbox">${supplementaryMaterials.get(key)}
                            </div>
                        </#list>
                        <textarea class="form-control" id="supplementaryReason" rows="5" placeholder="请输入补材原因"></textarea>
                    </#if>
                </div>

                <div class="col-sm-12">
                    <button class="btn " style="background-color: #1676FF;color: white" id="submitModifyAppraisalAmount" onclick="supplementaryMaterialsAction()">确认
                    </button>
                    <button class="btn " style="background-color: #1676FF;color: white"
                            onclick="closeSupplementaryMaterialsContainer()">取消
                    </button>
                </div>
            </div>
        </div>

<#--        银行卡补材-->
        <div role="tabpanel" class="tab-pane" id="bankCard">
            <div class="row" style="padding-top: 30px;margin: 0px 0px">
                <div class="col-sm-3">
                    <span class="pull-right" style="font-size: 10px;color: #7f7f7f">手机号：</span>
                </div>
                <div class="col-sm-9">
                    <input type="text" id="selfBookMobile"  class="form-control">
                </div>
            </div>
            <div class="row" style="padding-top: 30px;margin: 0px 0px">
                <div class="col-sm-3">
                    <span class="pull-right" style="font-size: 10px;color: #7f7f7f">请填写补充理赔申请书原因：</span>
                </div>
                <div class="col-sm-9">
                        <textarea class="form-control" id="selfBookReason" rows="5" ></textarea>
                </div>
            </div>
            <div class="col-sm-12">
                <button class="btn " style="background-color: #1676FF;color: white" id="submitModifyAppraisalAmount" onclick="selfBookAction()">确认
                </button>
                <button class="btn " style="background-color: #1676FF;color: white"
                        onclick="closeSupplementaryMaterialsContainer()">取消
                </button>
            </div>
        </div>
    </div>

</div>


<div class="row">

    <div class="col-sm-12">

        <div class="portlet light portlet-fit bordered">
            <div class="portlet-title container-head">
                <div class="row">
                    <div class="col-sm-4 pull-left">
                        <div class="col-sm-4"><span
                                    style="border:1px solid;background-color: orange">${claimCase.productName!'暂无产品'}</span>
                        </div>
                        <div class="col-sm-4">${claimCase.treatName!'-'}&nbsp;&nbsp;${claimCase.plateNumber!'-'}</div>
                        <div class="col-sm-4">${(claimCase.treatDate?string('yyyy-MM-dd HH'))!'-'}时出险</div>
                    </div>
                    <div class="col-sm-2">

                    </div>
                    <div class="col-sm-6">
                        <button class="btn btn-bule" onclick="onloadEPolicy('${claimCase.policyPersonId}')">下载保单
                        </button>
                        <#--催办记录暂时不处理，后期添加新功能-->
                        <button class="btn btn-bule" onclick="skipClaimVerify('${claimCase.id}')">跳过</button>
                        <button class="btn btn-light-bule" onclick="pressDoLog()">催办记录（0）</button>
                        <#--<button class="btn btn-bule" onclick="reSendMessage('${claimCase.id}')">补发短信</button>-->
                        <button class="btn btn-bule" onclick="closeCase('${claimCase.id}')">关闭赔案</button>
                    </div>
                </div>
            </div>
            <div class="portlet-body container-boby">

                <div class="row">
                    <div class="block-head-label">
                        投保频率 &nbsp;&nbsp;&nbsp;<span style="font-size: 10px">最近一次统计时间：</span><span
                                style="font-size: 10px" id="lastStatisticsData"> </span> <a href="#"
                                                                                            onclick="freshStatistics('1')">刷新</a>
                        <a href="#" class="pull-right" onclick="modifyAppraisalAmount('${claimCase.id}')">修改</a>
                        <span class="pull-right" id="appraisalAmount">估损金额（旧版）：${claimCase.appraisalAmount}元</span>
                    </div>
                    <div class="row block-border">
                        <div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="left-min-5" title="总投保次数">总投保次数：</div>
                                    <div class="right-min-7" id="times"></div>
                                    <a href="#" style="position: absolute;right: 1px;top: 30%"
                                       onclick="getHistoryRecord('${claimCase.baseUserId!''}')">历史保单记录</a>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="left-min-5" title="总投保率">总投保率：</div>
                                    <div class="right-min-7" id="sumTimesRate"></div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="left-min-5" title="最近连续投保天数">最近连续投保天数：</div>
                                    <div class="right-min-7" id="continuousTimes"></div>
                                </div>

                            </div>
                        </div>

                        <#--                        下划线-->
                        <div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="line-left-min-5"></div>
                                    <div class="line-right-min-7"></div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="line-left-min-5"></div>
                                    <div class="line-right-min-7"></div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="line-left-min-5"></div>
                                    <div class="line-right-min-7"></div>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="left-min-5" title="最近30天投保率">最近30天投保率：</div>
                                    <div class="right-min-7" id="sumNear30TimesRate"></div>
                                </div>
                            </div>
                            <div class="col-sm-8">
                                <div class="block-show">
                                    <div class="left-mid-5" title="近30天投保天数">近30天投保天数：</div>
                                    <div class="right-mid-7" id="near30DayTimes"></div>
                                </div>
                            </div>
                        </div>
                        <#--                        下划线-->
                        <div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="line-left-min-5"></div>
                                    <div class="line-right-min-7"></div>
                                </div>
                            </div>
                            <div class="col-sm-8">
                                <div class="block-show">
                                    <div class="line-left-mid-5"></div>
                                    <div class="line-right-mid-7"></div>
                                </div>
                            </div>
                        </div>

                        <div>
                            <div>
                                <div class="col-sm-4">
                                    <div class="block-show">
                                        <div class="left-min-5" title="最近一次投保时间">最近一次投保时间：</div>
                                        <div class="right-min-7" id="lastTimePolicyPersonDate"></div>
                                    </div>
                                </div>
                                <div class="col-sm-8">
                                    <div class="block-show">
                                        <div class="left-mid-5" title="最近投保时间">最近投保时间：</div>
                                        <div class="right-mid-7" id="lastPolicyPersonDate"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>

                <div class="row">
                    <div class="block-head-label">
                        出险人信息
                    </div>
                    <div class="row block-border">
                        <div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="left-min-5" title="保单号">保单号：</div>
                                    <div class="right-min-7">${policyPerson.policyNo!'-'}</div>
                                    <a style="position: absolute;right: 1px;display: block;width: 60px;"
                                       onclick="seePlanName('${product.id!''}')">查看方案</a>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="left-min-5" title="保险期限">保险期限：</div>
                                    <div class="right-min-7">${(policyPerson.startDate?string("yyyy-MM-dd HH:mm:ss"))!'-'}
                                        至${(policyPerson.endDate?string("yyyy-MM-dd HH:mm:ss"))!'-'}</div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="left-min-5" title="出险人">出险人：</div>
                                    <div class="right-min-7">${claimCase.treatName!'-'}</div>
                                    <#--待人员详情地址-->
                                    <a  style="position: absolute;right: 1px;top: 30%" onclick="viewPersonDetail('${claimCase.policyPersonId}')">查看人员信息</a>
                                </div>
                            </div>
                        </div>
                        <#--                        下划线-->
                        <div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="line-left-min-5"></div>
                                    <div class="line-right-min-7"></div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="line-left-min-5"></div>
                                    <div class="line-right-min-7"></div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="line-left-min-5"></div>
                                    <div class="line-right-min-7"></div>
                                </div>
                            </div>
                        </div>

                        <div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="left-min-5" title="身份证">身份证：</div>
                                    <div class="right-min-7">
                                        ${claimCase.treatIdNum!'-'}
                                        <a href="#" style="position: absolute;right: 1px;top: 30%"
                                           onclick="seeTreatIdTypeAttach('${claimCase.id}')">查看图片</a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="left-min-5" title="车辆信息">车辆信息：</div>
                                    <div class="right-min-7">${claimCase.carBrand!'-'}</div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="left-min-5" title="车牌号">车牌号：</div>
                                    <div class="right-min-7">${claimCase.plateNumber!'-'}</div>
                                </div>
                            </div>
                        </div>
                        <#--                        下划线-->
                        <div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="line-left-min-5"></div>
                                    <div class="line-right-min-7"></div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="line-left-min-5"></div>
                                    <div class="line-right-min-7"></div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="line-left-min-5"></div>
                                    <div class="line-right-min-7"></div>
                                </div>
                            </div>
                        </div>

                        <div>
                            <div>
                                <div class="col-sm-12">
                                    <div class="block-show">
                                        <div class="left-max-5" title="联系电话">联系电话：</div>
                                        <div class="right-max-7">${claimCase.treatMobile!'-'}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="block-head-label">
                        报案信息
                    </div>
                    <div class="row block-border">
                        <div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="left-min-5" title="案件号">案件号：</div>
                                    <div class="right-min-7">${claimCase.claimCaseNo!'-'}</div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="left-min-5" title="报案时间">报案时间：</div>
                                    <div class="right-min-7">
                                        ${(claimCase.startDate?string("yyyy-MM-dd HH:mm:ss"))!'-'}&nbsp;&nbsp;&nbsp;
                                        <span style="color: red" title="出险后${diffTime!''}报案">出险后${diffTime!''}报案</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="left-min-5" title="报案人姓名">报案人姓名：</div>
                                    <div class="right-min-7">${claimCase.applyName!'-'}</div>
                                </div>
                            </div>
                        </div>

                        <#--                        下划线-->
                        <div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="line-left-min-5"></div>
                                    <div class="line-right-min-7"></div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="line-left-min-5"></div>
                                    <div class="line-right-min-7"></div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="line-left-min-5"></div>
                                    <div class="line-right-min-7"></div>
                                </div>
                            </div>
                        </div>

                        <div>
                            <div>
                                <div class="col-sm-12">
                                    <div class="block-show">
                                        <div class="left-max-5" title="联系电话">联系电话：</div>
                                        <div class="right-max-7">${claimCase.applyMobile!'-'}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="block-head-label">
                        出险信息
                    </div>
                    <div class="row block-border">
                        <div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="left-min-5" title="出险原因">出险原因：</div>
                                    <div class="right-min-7">交通事故</div>
                                </div>
                            </div>
                            <div class="col-sm-8">
                                <div class="block-show">
                                    <div class="left-mid-5" title="出险时间">出险时间：</div>
                                    <div class="right-mid-7">
                                        ${(claimCase.treatDate?string("yyyy-MM-dd HH:mm:ss"))!'-'}&nbsp;&nbsp;&nbsp;
                                        <span style="color: red">保单投保时间：${(policyPerson.startDate?string("yyyy-MM-dd"))!'-'}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <#--                        下划线-->
                        <div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="line-left-min-5"></div>
                                    <div class="line-right-min-7"></div>
                                </div>
                            </div>
                            <div class="col-sm-8">
                                <div class="block-show">
                                    <div class="line-left-mid-5"></div>
                                    <div class="line-right-mid-7"></div>
                                </div>
                            </div>
                        </div>


                        <div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="left-min-5" title="事故类别">事故类别：</div>
                                    <div class="right-min-7">
                                        <#if claimCase.applyType?contains("AA001") || claimCase.applyType?contains("AB001")>
                                            <span class="span-type">门诊</span>
                                        </#if>
                                        <#if claimCase.applyType?contains("AA002") || claimCase.applyType?contains("AB002")>
                                            <span class="span-type">住院</span>
                                        </#if>
                                        <#if claimCase.applyType?contains("AA003") || claimCase.applyType?contains("AB003")>
                                            <span class="span-type">死亡</span>
                                        </#if>
                                        <#if claimCase.applyType?contains("AA004") || claimCase.applyType?contains("AB004")>
                                            <span class="span-type">伤残</span>
                                        </#if>
                                        <#if claimCase.applyType?contains("AC")>
                                            <span class="span-type">物损</span>
                                        </#if>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-8">
                                <div class="block-show">
                                    <div class="left-mid-5" title="出险地点">出险地点：</div>
                                    <div class="right-mid-7" title="${claimCase.province!"-"}-${claimCase.city!"-"}-${claimCase.district!"-"}&nbsp;&nbsp;${claimCase.address!"-"}">
                                        ${claimCase.province!"?"}-${claimCase.city!"?"}-${claimCase.district!"?"} &nbsp;&nbsp;&nbsp;
                                        ${claimCase.address!"?"}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <#--                        下划线-->
                        <div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="line-left-min-5"></div>
                                    <div class="line-right-min-7"></div>
                                </div>
                            </div>
                            <div class="col-sm-8">
                                <div class="block-show">
                                    <div class="line-left-mid-5"></div>
                                    <div class="line-right-mid-7"></div>
                                </div>
                            </div>
                        </div>

                        <div>
                            <div>
                                <div class="col-sm-4">
                                    <div class="block-show">
                                        <div class="left-min-5" title="医院名称">医院名称：</div>
                                        <div class="right-min-7">

                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-8">
                                    <div class="block-show">
                                        <div class="left-mid-5" title="索赔金额（元）">索赔金额（元）：</div>
                                        <div class="right-mid-7">${(claimCase.applyMoney?string("0.00"))!'-'}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="line-left-min-5"></div>
                                    <div class="line-right-min-7"></div>
                                </div>
                            </div>
                            <div class="col-sm-8">
                                <div class="block-show">
                                    <div class="line-left-mid-5"></div>
                                    <div class="line-right-mid-7"></div>
                                </div>
                            </div>
                        </div>

                        <div>
                            <div>
                                <div class="col-sm-12">
                                    <div class="block-show">
                                        <div class="left-max-5" title="经过">经过：</div>
                                        <div class="right-max-7">
                                            ${claimCase.description!'-'}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>


                </div>


                <div class="row payDutyInfo">
                    <div class="block-head-label">
                        赔付责任
                        <a  onclick="editDuty('${claimCase.id}')">编辑责任</a>
                    </div>
                    <#if claimCaseSubjectList?exists && (claimCaseSubjectList?size>0)>
                    <#list claimCaseSubjectList as claimCaseSubject>
                    <div class="col-sm-5 claim_subject_area" sujectName="${claimCaseSubject.name}" sujectArea="${claimCaseSubject.id}"
                         onclick="subjectVerify('${claimCaseSubject.id}')">
                        <#switch claimCaseSubject.status>
                        <#case 0>
                        <div class="notice notice-no-deal">
                            <#break >
                            <#case 1>
                            <div class="notice notice-deal">
                                <#break >
                                <#case -1>
                                <div class="notice notice-hangup">
                                    <#break >
                                    <#default >
                                    <div class="notice notice-default">
                                        </#switch>
                                        <div>${claimCaseSubject.remark!'-'}</div>
                                    </div>
                                </div>
                                </#list>
                                </#if>
                            </div>

                            <div class="row logInfo">
                                <div class="block-head-label">
                                    沟通记录/新增备注
                                </div>
                                <div class="col-sm-12">
                                    <div class="form-group" style="text-align: center">
                                        <textarea class="form-control" id="logTextArea" rows="5"
                                                  placeholder="请输入"></textarea>
                                        <div class="col-sm-12">
                                            <button class="btn btn-orange" onclick="subimitAll('${claimCase.id}')">
                                                审核通过
                                            </button>
                                          <#--  <#if isCanCustomerTask=='true'>
                                                <button class="btn btn-bule" onclick="changeCustomerTask(this)">
                                                    <#if claimCase.label!=null && claimCase.label?contains("Aax002")>去电完成<#else >再次去电</#if>
                                                </button>
                                            </#if>
                                            <button class="btn btn-bule" onclick="markDiffcultCase(this)">
                                                <#if claimCase.isDifficultCase!=1>标记疑难<#else >取消疑难</#if>
                                            </button>-->

                                            <button class="btn btn-bule" onclick="supplementaryMaterials()">
                                                发起补材
                                            </button>
                                            <button class="btn btn-grey">
                                                启动预赔
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row logListInfo">
                                <div class="block-head-label">
                                    日志信息
                                    <input type="checkbox" id="checkSystem"> <label for="checkSystem" style="font-size: 8px;font-weight: bold;">是否展示系统日志</label>
                                </div>
                                <div class="col-sm-12">
                                    <table class="table">
                                        <thead>
                                        <tr>
                                            <td width="10%"></td>
                                            <td>岗位</td>
                                            <td>类型</td>
                                            <td>人员</td>
                                            <td>时间</td>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <#if claimCaseLogShows??>
                                            <#list claimCaseLogShows as log>
                                                <tr class="rowInfo" <#if log.creator == "系统"> style="display: none;" </#if>>
                                                    <td width="10%" align="center">
                                                        <div class="icon-plus"></div>
                                                    </td>
                                                    <td width="20%">${log.position}</td>
                                                    <td width="20%">
                                                        ${claimCaseLogTypeEnumMap.get(log.type).msg}
                                                    </td>
                                                    <td width="20%">
                                                        <#if log.creator?contains("-") && log.creator!="-1" >
                                                            ${log.creator?substring(0,log.creator?index_of("-"))}
                                                        <#else>
                                                            ${log.creator}
                                                        </#if>
                                                    </td>
                                                    <td width="30%">${log.createTime?string["yyyy-MM-dd HH:mm:ss"]}</td>
                                                </tr>
                                                <tr class="detailsInfo">
                                                    <td width="10%" align="center"></td>
                                                    <td width="90%" colspan="4"
                                                        style="overflow-x: visible;">${log.description}</td>
                                                </tr>
                                            </#list>
                                        </#if>

                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>