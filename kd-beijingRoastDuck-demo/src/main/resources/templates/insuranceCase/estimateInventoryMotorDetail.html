<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!-- 上述3个meta标签*必须*放在最前面，任何其他内容都*必须*跟随其后！ -->
    <meta name="description" content="">
    <meta name="author" content="">
    <title>机动车估损清单详情</title>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <link rel="stylesheet" type="text/css" href="${ctx}/static/css/dataCollection.css"/>
    <script src="${ctx}/carousel/dataCollection/viewer.js"></script>
    <script src="${ctx}/carousel/dataCollection/viewermain.js"></script>
    <link href="${ctx}/carousel/dataCollection/viewer.css" rel="stylesheet">
    <script src="${ctx}/static/js/BigDecimal.js" type="text/javascript"></script>
    <link href="${ctx}/plugins/select2/css/applyDuty/select2.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/plugins/select2/css/applyDuty/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/plugins/select2/css/applyDuty/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/jquery-multi-select/css/multi-select.css" rel="stylesheet"
          type="text/css"/>
    <script src="${ctx}/plugins/select2/js/select2.js" type="text/javascript"></script>
    <link href="${ctx}/metronic/global/plugins/jquery-multi-select/css/multi-select.css" rel="stylesheet"
          type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/jstree/dist/themes/default/style.min.css" rel="stylesheet"
          type="text/css"/>
    <script src="${ctx}/metronic/global/scripts/app.min.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/layouts/layout4/scripts/layout.min.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/layouts/layout4/scripts/demo.min.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/plugins/jquery-multi-select/js/jquery.multi-select.js"
            type="text/javascript"></script>
    <style>

        .clear-margin {
            margin: 0px;
        }

        .clear-padding {
            padding: 0px;
        }

        .chart-search {
            margin-bottom: 10px;
            display: none;
        }


        .search-channel-content {
            height: 100px;
            overflow-y: auto;
            overflow-x: hidden;
        }

        .search-ul-list {
            list-style-type: none;
            height: 20px;
            padding-left: 0px;
            color: black;
        }

        .channel {
            float: left;
            display: block;
        }

        .typeClick {
            height: 112px;
            margin-bottom: 10px;
            border: 2px solid black;
            border-radius: 5px !important;
            margin-left: 20px;
            cursor: pointer
        }

        .typeClickClick {
            height: 112px;
            margin-bottom: 10px;
            border: 2px solid #1676ff;
            background-color: #1676ff;
            color: white;
            border-radius: 5px !important;
            margin-left: 20px;
            cursor: pointer
        }

        .has-error {
            border-color: #ce0d0d !important;
            color: red !important;
        }

        .bckColor {
            background-color: rgba(51, 153, 241, 0.3);
        }

        .imageListChoose {
            /*background-color: #bad6ff !important;*/
            border: 5px solid #055ad7;
        }

        .collectionCompleted {
            background-color: #E8F2FF;
        }

        .collectionButton {
            /*border-color: #1ABB00;*/
            border: 1px solid #1ABB00;
            background-color: transparent;
            border-radius: 6px !important;
            color: #1ABB00;
        }

        .collectionButtonClick {
            /*border-color: #1ABB00;*/
            border: 1px solid #1ABB00;
            background-color: #1ABB00 !important;
            border-radius: 6px !important;
            color: #FFFFFF;
        }

        .collectionData:hover {
            /*border-color: #1ABB00;*/
            border: 1px solid #1ABB00;
            background-color: #1ABB00;
            color: #FFFFFF;
        }

        .middleTable {
            border: 4px solid #1767ff !important;
        }

        .hangup {
            /*margin-left: 5px;*/
            color: #fff !important;
            background-color: #ce0d0d !important;
            border-radius: 5px !important;
            height: 40px;
            width: 100%;
        }

        .error {
            color: #ce0d0d !important;
        }

        .txt-over-hide {
            color: red;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            display: block;
            width: 650px;
        }

        .jump {
            /*float: right;*/
            border-radius: 5px !important;
            color: #3399ff !important;
            background-color: #fff !important;
            border-color: #3399ff;
            height: 40px;
            width: 100%;
        }

        .genTask {
            /*margin-left: 5px;*/
            color: #fff !important;
            background-color: #3399ff !important;
            border-radius: 5px !important;
            height: 40px;
            width: 100%;
        }

        .thClass {
            text-align: center;
            color: #fff;
        }

        .typeSelect {
            border-color: #3399ff;
            background-color: #3399ff;
        }

        .thumbnail-img {
            width: 50px;
            height: 45px;
            border: 4px solid #D6D6C1;
        }

        .selected-thumbnail-img {
            border: 4px solid #3399ff !important;
        }

        .collection-thumbnail-img {
            border: 4px solid #00B176;
        }

        /*里面的代码可以根据自己需求去进行更改*/
        /* 设置滚动条的样式 */
        ::-webkit-scrollbar {
            width: 5px;
        }

        /* 滚动槽 */
        ::-webkit-scrollbar-track {
            -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.3);
            border-radius: 10px;
        }

        /* 滚动条滑块 */
        ::-webkit-scrollbar-thumb {
            border-radius: 10px;
            background: rgba(0, 0, 0, 0.1);
            -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.5);
        }

        ::-webkit-scrollbar-thumb:window-inactive {
            background: rgba(255, 0, 0, 0.4);
        }

        .ticket-information {
            height: 1050px;
            overflow-y: scroll;
        }

        .dataDisplayArea {
            width: 100%;
            height: 19%;
            border-radius: 10px !important;
            /*border: 1px solid #aca8a8;*/
            border: 1px solid #797979;
            cursor: pointer;
            margin-top: 10px;
            overflow: hidden;
        }

        .selected-dataDisplayArea {
            background-color: #91cae9;
        }

        .submit-dataDisplayArea {
            border: 3px solid #44db69 !important;
        }

        .change-dataDisplayArea {
            border: 3px solid red !important;
        }

        .dataDisplayArea > div {
            height: 20%;
            padding: 10px 0px 0px 0px !important;
        }

        .dataDisplayArea-head {
            background-color: #3399ff;
            /*border-radius: 10px 10px 0px 0px !important;*/
        }

        .dataDisplayArea-head-left {
            color: #ffffff;
            font-size: 15px;
            padding: 0px 0px 0px 5px;
        }

        .dataDisplayArea-head-right {
            text-align: right;
            padding: 0px !important;
        }

        .dataDisplayArea-head-img {
            width: 30px;
            height: 18px;
        }

        .dataDisplayArea-body-left {
            opacity: 0.7;
        }

        .dataDisplayArea-body-right {
            text-align: right;
        }

        .row {
            margin: 0px 0px;
        }

        .subject-name {
            font-size: 20px;
        }

        .estimateInventoryDataBlue {
            background-color: #1676ff;
        }

        .line-center {
            display: inline-block;
            background-color: blue;
            border-radius: 50%;
            height: 30px;
            width: 30px;
            color: white;
            text-align: center;
            line-height: 30px;
        }

        .icon-plus {
            font-size: 30px;
            line-height: 30px;
        }

        .icon-plus:hover {
            cursor: pointer;
        }

        .dataDisplayArea-head-img {
            margin: 10px 0px;
        }

        .dataDisplayArea-head-img:hover {
            cursor: pointer;
        }
    </style>

    <script type="text/javascript">

        var viewer = null;

        $(function () {

            /*图片展示工具*/
            (function () {
                viewer = new Viewer({
                    activeId: null
                });
            })();


            /*获取焦点移除报错样式*/
            $("body").on("focus", "input,textarea,select,.ggLayer", function () {
                document.onkeydown = function (w) {
                }
            });

            /*点击缩略图*/
            $('body').on('click', '.thumbnail-img', function () {
                if ($(this).hasClass('selected-thumbnail-img')) {
                    return;
                }
                let attrId = $(this).data('fileid');
                $('.selected-thumbnail-img').removeClass('selected-thumbnail-img');
                $(this).addClass('selected-thumbnail-img');
                viewer.showImageByFileId(attrId);
                let imgNumber = $(this).data('img-number');
                let offset = imgNumber * 95 - (1050 - 95) / 2;
                $('#thumbnail').scrollTop(offset);
            });

            /*A/D切换影像*/
            $('body').on('click', '.viewer-list > li', function () {
                let attchId = $(this).find('img').attr('alt');
                $('#thumbnail-' + attchId).trigger('click');
            });

            $("div[name='dataCollectArea']").on('blur', "input[validName='billCollection']", check);

            // 刷新报价维修金额，当input失去焦点的时候
            $("div[name='dataCollectArea']").on('blur', "input[name='lossAssessment']", freshLossAssessment);
            $("#residualValue").on('blur', freshLossAssessment);

            //初始话数据
            <#if assessmentReportList?exists &&(assessmentReportList?size>0)>
            <#list assessmentReportList as assessmentReportData>
                addDataRow('${assessmentReportData.code}', '${assessmentReportData}');
            </#list>
            </#if>

            let status = '${status}';
            if (status == '0' || status == '2') {
                $.each($("[validName='billCollection']"), function (index, obj) {
                    $(this).attr("disabled", "1");
                });
                $.each($(""));
                $.each($(".icon-plus"), function (index, obj) {
                    $(this).remove();
                });
                $.each($(".dataDisplayArea-head-img"), function (index, obj) {
                    $(this).remove();
                });
            }
            if(status == '' || status =='-1' || status == '0'){
                $("#nuclearLossSum").parent().css("display","none");
            }
            if(status=='1'){
                $('#residualValue').val("");
                $('#verifyAmout').val("");
                $('#verifyDetail').val("");
                $('#dutyRate').val("");
                $('#deductFee').val("");
            }

            freshLossAssessment();

            var createInput = function (name, value, type = "hidden") {
                var inputElement = document.createElement("input");
                inputElement.type = type;
                inputElement.name = name;
                if (value != null) {
                    inputElement.value = value;
                }
                return inputElement;
            }


            var postForm = function (url) {
                // 创建表单
                var formElement = document.createElement("form");
                formElement.action = url;
                formElement.method = "post";
                // 打开新标签
                formElement.target = '_blank';
                formElement.style.display = "none";
                var code = $.trim($("#assessmentReportName").val());
                var type = 2;//机动车为2
                if (code != "") {
                    formElement.appendChild(createInput("code", code));
                }
                formElement.appendChild(createInput("type", type));
                var assessmentFather = {};
                let lossAssessmentSum = $("#lossAssessmentSum").attr("sumMoney");
                if (lossAssessmentSum != "") {
                    assessmentFather.lossAssessmentSum = lossAssessmentSum;
                }
                let carNumber = $.trim($("#carNumber").val());
                if (carNumber != "") {
                    assessmentFather.carNumber = carNumber;
                }
                let carModel = $.trim($("#carModel").val());
                if (carModel != "") {
                    assessmentFather.carModel = carModel;
                }
                let carEncoding = $.trim($("#carEncoding").val());
                if (carModel != "") {
                    assessmentFather.carEncoding = carEncoding;
                }
                let firstRegistrationTime = $.trim($("#firstRegistrationTime").val());
                if (firstRegistrationTime != undefined && firstRegistrationTime.trim() != "") {
                    let firstRegistrationTimeStr = firstRegistrationTime.replace(/^(\d{4})(\d{2})(\d{2})$/, "$1-$2-$3");
                    firstRegistrationTime = new Date(firstRegistrationTimeStr+" 00:00:00").getTime();
                    assessmentFather.firstRegistrationTime = firstRegistrationTime;
                }
                let carOwner = $.trim($("#carOwner").val());
                if (carOwner != "") {
                    assessmentFather.carOwner = carOwner;
                }
                let lossAssessmentTime = $.trim($("#lossAssessmentTime").val());
                if (lossAssessmentTime != undefined && lossAssessmentTime.trim() != "") {
                    let lossAssessmentTimeStr = lossAssessmentTime.replace(/^(\d{4})(\d{2})(\d{2})$/, "$1-$2-$3");
                    lossAssessmentTime = new Date(lossAssessmentTimeStr+" 00:00:00").getTime();
                    assessmentFather.lossAssessmentTime = lossAssessmentTime;
                }
                let repairFactory = $.trim($("#repairFactory").val());
                if (repairFactory != "") {
                    assessmentFather.repairFactory = repairFactory;
                }
                let verifyAmout = $.trim($("#verifyAmout").val());
                if (verifyAmout != "") {
                    assessmentFather.verifyAmout = verifyAmout;
                }
                let residualValue = $.trim($("#residualValue").val());
                if (residualValue != "") {
                    assessmentFather.residualValue = residualValue;
                }
                let verifyDetail = $.trim($("#verifyDetail").val());
                if (verifyDetail != "") {
                    assessmentFather.verifyDetail = verifyDetail;
                }
                let is4S = $.trim($("#is4S").val());
                if (is4S != "") {
                    assessmentFather.is4S = is4S;
                }
                let dutyRate = $.trim($("#dutyRate").val());
                if (dutyRate != "") {
                    assessmentFather.dutyRate = dutyRate;
                }
                let deductFee = $.trim($("#deductFee").val());
                if (deductFee != "") {
                    assessmentFather.deductFee = deductFee;
                }

                assessmentFather.assessmentReport = [];
                $.each($("div[name='dataCollectAreaSubjet']"), function (index, obj) {
                    let code = $(this).attr("code");
                    $.each($(this).find("div[name='dataCollectAreaRow']"), function (index, obj) {
                        let repairName = $.trim($(this).find("input[name='repairName']").val());
                        let repairPrice = $.trim($(this).find("input[name='repairPrice']").val());
                        let approvedAmount = $.trim($(this).find("input[name='approvedAmount']").val());
                        let lossAssessment = $.trim($(this).find("input[name='lossAssessment']").val());

                        assessmentFather.assessmentReport.push({
                            "code": code,
                            "repairName": repairName,
                            "repairPrice": repairPrice,
                            "lossAssessment": lossAssessment,
                            "approvedAmount": approvedAmount
                        })
                    });
                });
                formElement.appendChild(createInput("assessmentFatherStr", JSON.stringify(assessmentFather)));
                document.body.appendChild(formElement);
                formElement.submit();
                formElement.remove();
                return;
            }


            //导出按钮监控器
            $("#inputForm").on("click", ".exportBtn", function (e) {
                e.stopPropagation();
                e.preventDefault();
                var _this = this;
                $(_this).removeClass("exportBtn");
                //提交之后再把class加回来 防止重复提交
                postForm("${ctx}/downloadCenterController/downloadAssessment");
                $(_this).addClass("exportBtn");

            })
        });

        //刷新报价维修金额
        function freshLossAssessment() {
            let repairPriceSum = new BigDecimal("0");
            let sumApprovedAmount = new BigDecimal("0");
            $.each($("div[name='dataCollectAreaSubjet']"), function (index, obj) {
                let sumLineRepairPrice = new BigDecimal("0");

                $.each($(this).find("div[name='dataCollectAreaRow']"), function (index, obj) {
                    let repairPrice = new BigDecimal("0");
                    let approvedAmount = new BigDecimal("0");
                    try {
                        repairPrice = new BigDecimal($(this).find("input[name='lossAssessment']").val());
                        approvedAmount = new BigDecimal($(this).find("input[name='approvedAmount']").val());
                    } catch (e) {
                    }
                    sumLineRepairPrice = sumLineRepairPrice.add(repairPrice).setScale(2, MathContext.ROUND_HALF_UP);
                    sumApprovedAmount = sumApprovedAmount.add(approvedAmount).setScale(2, MathContext.ROUND_HALF_UP);

                });
                repairPriceSum = repairPriceSum.add(sumLineRepairPrice).setScale(2, MathContext.ROUND_HALF_UP);
                let code = $(this).attr("code");
                let remark = "合计";
                switch (code) {
                    case "2-1":
                        remark = "配件金额合计：";
                        break;
                    case "2-2":
                        remark = "维修人工合计：";
                        break;
                }
                $("#" + code + "LossAssessment").html(remark + sumLineRepairPrice + "元");
            });
            //维修合计金额=配件合计+人工合计-残值
            let residualValue = new BigDecimal("0");
            try {
                residualValue = new BigDecimal($("#residualValue").val());
            } catch (e) {
            }
            repairPriceSum = repairPriceSum.subtract(residualValue).setScale(2, MathContext.ROUND_HALF_UP);
            $("#lossAssessmentSum").html("维修总金额：" + repairPriceSum + "元");
            $("#lossAssessmentSum").attr("sumMoney", repairPriceSum);
            $("#nuclearLossSum").html("核损合计：" + sumApprovedAmount.setScale(2, MathContext.ROUND_HALF_UP) + "元");
            $("#nuclearLossSum").attr("sumMoney", sumApprovedAmount.setScale(2, MathContext.ROUND_HALF_UP));
        }


        function submitData(status) {

            $("button[name='actionBtn']").attr('disabled','1');

            let claimCaseId = '${claimCase.id}';
            let id = '${assessmentReportId}';
            let code = $("#assessmentReportName").val().trim();
            let type = '${type}';
            let reason = '${(remark?replace("\n",""))!""}';

            let lossAssessmentSum = $("#lossAssessmentSum").attr("sumMoney");

            let isError = false;
            let errorMsg = "";
            let residualValue = $("#residualValue").val().trim();
            if(!residualValue && residualValue==''){
                errorMsg += "残值金额不能为空！</br>";
                isError = true;
            }
            let residualNuclearLossValue = $("#residualNuclearLossValue").val().trim();

            let verifyAmout = $("#verifyAmout").val().trim();
            if(!verifyAmout && verifyAmout==''){
                errorMsg += "理算金额不能为空！</br>";
                isError = true;
            }
            let verifyDetail = $("#verifyDetail").val().trim();
            if(!verifyDetail && verifyDetail==''){
                errorMsg += "理算描述不能为空！</br>";
                isError = true;
            }
            let dutyRate = $("#dutyRate").val().trim();
            if(!dutyRate && dutyRate==''){
                errorMsg += "责任比例不能为空！</br>";
                isError = true;
            }
            let deductFee = $("#deductFee").val().trim();
            if(!deductFee && deductFee==''){
                errorMsg += "扣除费用不能为空！</br>";
                isError = true;
            }
            if(isError){
                layer.msg(errorMsg, {icon: 2, time: 3000},function () {
                    $("button[name='actionBtn']").removeAttr("disabled");
                });
                return;
            }
            let carNumber = $("#carNumber").val().trim();
            let carModel = $("#carModel").val().trim();
            let carEncoding = $("#carEncoding").val().trim();
            let firstRegistrationTime = $("#firstRegistrationTime").val().trim();
            if (firstRegistrationTime != undefined && firstRegistrationTime.trim() != "") {
                let firstRegistrationTimeStr = firstRegistrationTime.replace(/^(\d{4})(\d{2})(\d{2})$/, "$1-$2-$3");
                firstRegistrationTime = new Date(firstRegistrationTimeStr+" 00:00:00").getTime();
                console.log(firstRegistrationTime);
            }
            let carOwner = $("#carOwner").val().trim();
            let lossAssessmentTime = $("#lossAssessmentTime").val().trim();
            if (lossAssessmentTime != undefined && lossAssessmentTime.trim() != "") {
                let lossAssessmentTimeStr = lossAssessmentTime.replace(/^(\d{4})(\d{2})(\d{2})$/, "$1-$2-$3");
                lossAssessmentTime = new Date(lossAssessmentTimeStr+" 00:00:00").getTime();
                console.log(lossAssessmentTime);
            }
            let repairFactory = $("#repairFactory").val().trim();
            let is4S = $("#is4S").val().trim();


            let data = [];
            $.each($("div[name='dataCollectAreaSubjet']"), function (index, obj) {
                let code = $(this).attr("code");
                $.each($(this).find("div[name='dataCollectAreaRow']"), function (index, obj) {
                    let rowId = $(this).find("input[name='rowId']").val().trim();
                    let repairName = $(this).find("input[name='repairName']").val();
                    let lossAssessment = $(this).find("input[name='lossAssessment']").val().trim();
                    let approvedAmount = $(this).find("input[name='approvedAmount']").val().trim();
                    data.push({"code": code, "rowId": rowId,"repairName": repairName, "lossAssessment": lossAssessment, "approvedAmount": approvedAmount})
                });
            });


            let dataJson = {};
            dataJson["assessmentReport"] = data;
            dataJson["carNumber"] = carNumber;
            dataJson["carModel"] = carModel;
            dataJson["carEncoding"] = carEncoding;
            dataJson["firstRegistrationTime"] = firstRegistrationTime;
            dataJson["carOwner"] = carOwner;
            dataJson["lossAssessmentTime"] = lossAssessmentTime;
            dataJson["repairFactory"] = repairFactory;
            dataJson["is4S"] = is4S;
            dataJson["lossAssessmentSum"] = lossAssessmentSum;
            dataJson["nuclearLossSum"] = $("#nuclearLossSum").attr("sumMoney");
            dataJson["verifyAmout"] = verifyAmout;
            dataJson["residualValue"] = residualValue;
            dataJson["residualNuclearLossValue"] = residualNuclearLossValue;
            dataJson["verifyDetail"] = verifyDetail;
            dataJson["dutyRate"] = dutyRate;
            dataJson["deductFee"] = deductFee;
            dataJson["reason"] = reason;
            dataJson["code"] = code;

            let resultData = {};
            resultData["id"] = id;
            resultData["claimCaseId"] = claimCaseId;
            resultData["code"] = code;
            resultData["type"] = type;
            resultData["code"] = code;
            resultData["reqData"] = dataJson;
            resultData["status"] = status;

            console.log(JSON.stringify(resultData));
            let formData = new FormData();
            formData.append("jsonStr", JSON.stringify(resultData))

            $.ajax({
                url: "${ctx}/insuranceCaseController/estimateInventorySubmit",
                type: 'POST',
                data: formData,
                async: true,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data1) {
                    var result = eval("(" + data1 + ")");
                    console.log(result);
                    if (result.ret == "0") {
                        layer.msg(result.msg, {
                            icon: 1,
                            time: 3000
                        }, function () {
                            window.location.href="${ctx}/insuranceCaseController/caseDetail?caseId="+claimCaseId;
                        });
                    } else {
                        layer.open({
                            content: result.msg
                            , icon: 2
                            , btn: ['关闭']
                            , yes: function (index, layero) {
                                $("button[name='actionBtn']").removeAttr("disabled");
                                layer.close(index);
                            }
                        });
                    }

                },
                error: function (data) {
                    $("button[name='actionBtn']").removeAttr("disabled");
                    console.log(data)
                }
            })
        }

        //按钮点击增加数据，click事件
        function addDataRowClick(code) {
            let data = {};
            data["rowId"] = guid();
            data["repairName"] = "";
            data["lossAssessment"] = "";
            data["approvedAmount"] = "";
            addDataRow(code, JSON.stringify(data))
        }

        //增加一条数据
        function addDataRow(code, data) {
            data = JSON.parse(data);
            console.log(data.rowId);
            let repairName=data.repairName;
            if(repairName == undefined){
                repairName = "";
            }
            let lossAssessment=data.lossAssessment;
            if(lossAssessment == undefined){
                lossAssessment = "";
            }
            let approvedAmount=data.approvedAmount;
            if(approvedAmount == undefined){
                approvedAmount = "";
            }
            let after = $("#" + code + "collectArea").append(
                '<div class="col-sm-12 " style="margin-top: 10px" name="dataCollectAreaRow">\n' + '<input type="hidden"  name="rowId" class="form-control" value="' + data.rowId + '" />' +
                '                                        <div class="col-sm-1 clear-padding" style="font-size: 15px;margin: 9px 0px;">\n' +
                '                                            <div class="clear-padding line-center"></div>\n' +
                '                                        </div>\n' +
                '                                        <div class="col-sm-4 clear-padding">\n' +
                '                                            <div class="col-sm-3 clear-padding" style="font-size: 15px;display: inline-block;text-align: center;line-height: 15px">名称</div>\n' +
                '                                            <div class="col-sm-9 " >\n' +
                '                                                <input type="text" style="padding: 0px !important;" validName="billCollection" data-valid="none" name="repairName" class="form-control" value="' + repairName + '" title="'+repairName+'" />\n' +
                '                                            </div>\n' +
                '                                        </div>\n' +
                /*'                                        <div class="col-sm-3 clear-padding" >\n' +
                '                                            <div class="col-sm-4 clear-padding" style="font-size: 15px;display: inline-block;text-align: center;line-height: 15px">维修报价</div>\n' +
                '                                            <div class="col-sm-8 " >\n' +
                '                                                <input type="text" style="padding: 0px !important;" validName="billCollection" data-valid="isNumberAndDecimalPoint" name="repairPrice" class="form-control" value="' + repairPrice + '" title="'+repairPrice+'" />\n' +
                '                                            </div>\n' +
                '                                        </div>\n' +*/
                '                                        <div class="col-sm-3 clear-padding" >\n' +
                '                                            <div class="col-sm-4 clear-padding" style="font-size: 15px;display: inline-block;text-align: center;line-height: 15px">定损金额</div>\n' +
                '                                            <div class="col-sm-8 " >\n' +
                '                                                <input type="text" style="padding: 0px !important;" validName="billCollection" name="lossAssessment" data-valid="isNumberAndDecimalPoint" class="form-control" value="' + lossAssessment + '" title="'+lossAssessment+'" />\n' +
                '                                            </div>\n' +
                '                                        </div>\n' +
                '                                        <div class="col-sm-3 clear-padding" >\n' +
                '                                            <div class="col-sm-4 clear-padding" style="font-size: 15px;display: inline-block;text-align: center;line-height: 15px">核损金额</div>\n' +
                '                                            <div class="col-sm-8 " >\n' +
                '                                                <input type="text" style="padding: 0px !important;" readonly validName="billCollection" name="approvedAmount" data-valid="isNumberAndDecimalPoint" class="form-control" value="' + approvedAmount + '" title="'+approvedAmount+'" />\n' +
                '                                            </div>\n' +
                '                                        </div>\n' +
                '                                        <div class="col-sm-1 clear-padding" >\n' +
                '                      <img src="/images/shanchu.svg"  class="dataDisplayArea-head-img" onclick="delDataRow(this,' + code + ')">' +
                '                                        </div>\n' +
                '                                    </div>'
            );
            //刷新序号
            freshLineCenter();
            //刷新维修报价金额
            freshLossAssessment();
        }

        //删除一行数据
        function delDataRow(obj, code) {
            $(obj).parent().parent().remove();
            //刷新序号
            freshLineCenter();
            //刷新维修报价金额
            freshLossAssessment();
        }

        //刷新序号
        function freshLineCenter() {
            $.each($("div[name='dataCollectAreaRow']"), function (index, obj) {
                let prevName = $(this).prev().attr("name");
                let num = 1;
                if (prevName != undefined && prevName == "dataCollectAreaRow") {
                    let prevNum = $(this).prev().find(".line-center").html();
                    num = new Number(prevNum) + 1;
                }
                $(this).find(".line-center").html(num);
            });
        }


        function seePlanName(planId) {
            if (typeof planId == 'undefined' || planId == '') {
                layer.msg('暂未数据！！！', {
                    icon: 2,
                    time: 2000
                });
                return;
            }

            var formData = new FormData();
            formData.append("planId", planId);
            $.ajax({
                url: "${ctx}/insuranceCaseController/seePlanNameByPlanId",
                type: 'POST',
                data: formData,
                async: true,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    var result = eval("(" + data + ")");
                    if (result.ret == "0") {
                        window.open(result.msg, "_blank");
                    } else {
                        layer.msg(result.msg, {
                            icon: 2,
                            time: 2000
                        });
                    }
                },
                error: function (data) {
                    var result = eval("(" + data + ")");
                    alert(result.msg);
                }
            });

        }

        function seeCaseProcessReason(claimCaseId) {
            var openWindowWidth = $(document).width() * 0.8 + "px";
            var offsetH = ($(window).height() / 5 - 20 > 120 ? 120 : $(window).height() / 5 - 20) + "px";
            layer.open({
                type: 2,
                title: '查看案件流转',
                area: openWindowWidth,
                offset: offsetH,
                fix: false, //不固定
                maxmin: true,
                content: "${ctx}/insuranceCaseController/getBSCaseAssessmentProcessReason?claimCaseId=" + claimCaseId,
                success: function (layero, index) {
                    layer.iframeAuto(index);
                }
            });
        }

        function getHistroyCaseInfo(baseUserId) {
            if (baseUserId == '') {
                layer.msg("暂无信息！！！", {icon: 2, time: 3000});
                return;
            }
            var openWindowWidth = $(document).width() * 0.8 + "px";
            var offsetH = ($(window).height() / 5 - 20 > 120 ? 120 : $(window).height() / 5 - 20) + "px";
            layer.open({
                type: 2,
                title: '查看历史案件',
                area: openWindowWidth,
                offset: offsetH,
                fix: false, //不固定
                maxmin: true,
                content: "${ctx}/insuranceCaseController/getHistroyCaseInfo?baseUserId=" + baseUserId,
                success: function (layero, index) {
                    layer.iframeAuto(index);
                }
            });
        }


        //生成uuid
        function guid() {
            return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
                var r = Math.random() * 16 | 0,
                    v = c == 'x' ? r : (r & 0x3 | 0x8);
                return v.toString(16);
            });
        }


        let check = function () {
            var _this = this;
            var val = _this.value;
            var valid = _this.getAttribute('data-valid');
            if (!checkService[valid](val)) {
                $(_this).val("");
                layer.msg("输入格式不对", {icon: 5, time: 1000});
                $(_this).focus();
                return;
            }

            $(this).attr("title", val);

            if (_this.id === "treatmentDateStr" || _this.id === "leaveHospitalDateStr") {
                //就诊/入院日期、出院日期
                let collectionDate = val.replace(/^(\d{4})(\d{2})(\d{2})$/, "$1-$2-$3");
                let date = new Date(collectionDate);
                let currentDate = new Date();
                if (currentDate.getTime() < date.getTime()) {
                    $(_this).val("");
                    layer.msg("日期大于当前时间", {icon: 5, time: 1000});
                    $(_this).focus();
                }
            }

        }
        //校验规则
        const checkService = {
            // 不校验
            none: function () {
                return true;
            },

            //非空校验
            isEmpty: function (str) {
                if (str == null || str == "") return false;
            },

            // 只能输入数字[0-9]
            isDigits: function (str) {
                if (str == null || str == "") return true;
                var reg = /^\d+$/;
                return reg.test(str);
            },
            //百分比0-100
            isRate: function(str){
                if (str == null || str == "") return true;
                var reg = /^\d+$/;
                let result = false;
                if(reg.test(str) && (str>=0 && str <=100)){
                    result = true;
                }
                return result;
            },

            // 匹配english
            isEnglish: function (str) {
                if (str == null || str == "") return true;
                var reg = /^[A-Za-z]+$/;
                return reg.test(str);
            },

            // 匹配integer(包含正负)
            isInteger: function (str) {
                if (str == null || str == "") return true;
                var reg = /^[-\+]?\d+$/;
                return reg.test(str);
            },

            // 匹配汉字
            isChinese: function (str) {
                if (str == null || str == "") return true;
                var reg = /^[\u4e00-\u9fa5]+$/;
                return reg.test(str);
            },

            // 匹配中文(双字节字符,包括汉字和符号)
            isChineseChar: function (str) {
                if (str == null || str == "") return true;
                var reg = /^[\u0391-\uFFE5]+$/;
                return reg.test(str);
            },

            //匹配中英文
            isChineseAndEnglish: function (str) {
                if (str == null || str == "") return true;
                var reg = /^[\u4e00-\u9fa5a-zA-Z]+$/;
                return reg.test(str);
            },

            // 匹配URL
            isUrl: function (str) {
                if (str == null || str == "") return true;
                var reg = /^http:\/\/[A-Za-z0-9]+\.[A-Za-z0-9]+[\/=\?%\-&_~`@[\]\’:+!]*([^<>\"])*$/;
                return reg.test(str);
            },

            // 字符验证，只能包含中文、英文、数字、下划线、空格。
            stringCheck: function (str) {
                if (str == null || str == "") return true;
                var reg = /^[a-zA-Z0-9\u4e00-\u9fa5_ ,.，。]+$/;
                return reg.test(str);
            },

            //字符长度校验（最长64位）
            stringLengthCheck: function (str, length) {
                if (str == null || str == "") return true;
                length = length || 64;
                if (str.length > length) return false;
                return true;
            },
            //IP格式验证
            isIP: function (str) {
                if (str == null || str == "") return true;
                var reg = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;
                return reg.test(str);
            },
            //YYYYMMDD格式验证
            isTime: function (str) {
                if (str == null || str == "") return true;
                var reg = /^((([0-9]{3}[1-9]|[0-9]{2}[1-9][0-9]{1}|[0-9]{1}[1-9][0-9]{2}|[1-9][0-9]{3})(((0[13578]|1[02])(0[1-9]|[12][0-9]|3[01]))|((0[469]|11)(0[1-9]|[12][0-9]|30))|(02(0[1-9]|[1][0-9]|2[0-8]))))|((([0-9]{2})(0[48]|[2468][048]|[13579][26])|((0[48]|[2468][048]|[3579][26])00))0229))$/;
                return reg.test(str);
            },
            //只能输入数字和小数点
            isNumberAndDecimalPoint: function (str) {
                if (str == null || str == "") return true;
                var reg = /^\d+(\.\d{1,2})?$/;
                return reg.test(str);
            },
            //只能输入数字和小数点
            isNumberAndDecimalPointNotDefault: function (str) {
                if (str == null || str == "") return true;
                var reg = /^\d+(\.\d{1,2})?$/;
                return reg.test(str);
            },
            //select2--->是否
            isTrueFalse: function (str) {

                return true;
            },
            //伤残等级
            isDisabilityLevel: function () {
                return true;
            },
            //伤残等级
            isDeadSurplusQuota: function () {
                return true;
            }
        };
    </script>
</head>


<body id="qc-Body" style="overflow-x:hidden;background: #fff;">
<div class="container-fluid">
    <div class="row">
        <form id="inputForm" onsubmit="return false">
            <div class="row">
                <div class="col-sm-9">
                    <div class="col-sm-12" style="margin-bottom: 5px;padding-left: 0px;padding-right: 0px;">
                        <div class="col-sm-3" style="padding-left: 0px;">
                            <div class="col-sm-12" style="padding-left: 0px;">
                                案件号：${claimCase.claimCaseNo}
                            </div>
                        </div>
                        <div class="col-sm-3" style="padding-left: 0px;">
                            <div class="col-sm-12" style="padding-left: 0px;">
                                姓名：${claimCase.applyName}
                            </div>
                        </div>
                        <div class="col-sm-3" style="padding-left: 0px;">
                            <div class="col-sm-12" style="padding-left: 0px;">
                                是否延迟报立案：<#if delayReport!=null && delayReport==1>是<#else >否</#if>
                            </div>
                        </div>
                    </div>
                    <#-- 按钮 -->
                    <div class="col-sm-12" style="margin-bottom: 5px;padding-left: 0px;padding-right: 0px;">

                        <div class="col-sm-2" style="padding-left: 0px;">
                            <div class="col-sm-12" style="padding-left: 0px;">
                                <button type="button" class="btn genTask btn-look" onclick="seePlanName('${product.id!''}')">
                                    查看产品方案
                                </button>
                            </div>
                        </div>

                        <div class="col-sm-2" style="padding-left: 0px;">
                            <button type="button" onclick="getHistroyCaseInfo('${claimCase.baseUserId}')" class="btn genTask">
                                历史案件
                            </button>
                        </div>

                        <div class="col-sm-2">
                            <button type="button" class="btn genTask circulation_reasons" onclick="seeCaseProcessReason('${claimCase.id}')"> 查看案件流转原因</button>
                        </div>

                    </div>
                    <div class="col-sm-6 col-sm-offset-1" style="color: red">
                        ${remark!''}
                    </div>
                </div>
                <div class="col-sm-3" style="margin-bottom: 5px;padding-left: 0px;padding-right: 0px;">
                    <div class="col-sm-10 pull-left" style="margin: 20px auto;display: flex;">
                        <#if status=='' || status =='-1'>
                            <div class="col-sm-4">
                                <button name="actionBtn" style="background-color: #0597FF;color: white" class="btn btn-lg" onclick="submitData('-1')">暂存</button>
                            </div>
                        </#if>

                        <#if status=='' || status =='-1' || status == '1'>
                            <div class="col-sm-4">
                                <button name="actionBtn" style="background-color: #F49929;color: white" class="btn btn-lg" onclick="submitData('0')">提交</button>
                            </div>
                        </#if>

                        <div class="col-sm-4">
                            <button style="background-color: #009F9F;color: white" class="exportBtn btn btn-lg">导出</button>
                        </div>

                    </div>
                </div>
            </div>
            <!-- 左部分 -->
            <div class="col-sm-8" style="background: none!important;padding-right: 0px !important;">
                <#--审核头-->
                <div class="row">
                    <#-- 展示图片区域以及采集数据tbale -->
                    <div class="col-sm-12" style="padding: 5px 0px 0px 0px; background: none;">
                        <div class="row" style="height: 1050px; overflow: hidden;">
                            <div class="col-sm-1" style="height: 1050px;background-color: rgba(226, 226, 226, 0.5);padding-left: 0px!important;padding-right: 10px!important; border-right: groove;">
                                <#if claimAttachList?exists && (claimAttachList?size>0)>
                                    <div id="thumbnail" style="height: 1050px;<#if (claimAttachList?size>11)>overflow-y: scroll;</#if>overflow-x:hidden">
                                        <ol style="padding-left:25px !important;/*padding-right: 15px !important;*/">
                                            <#list claimAttachList as attach>
                                                <li style="width:auto; height:auto; cursor:pointer;text-align: center;padding-top:5px">
                                                    <img id="thumbnail-${attach.id}" class="thumbnail-img <#if attach_index == 0>selected-thumbnail-img</#if>"
                                                         data-fileid="${attach.id}" data-img-number=''
                                                         title="${attach.fileName}"
                                                         src="${attach.fileObjectId}" onerror="javascript:this.src='/a/job_done.png'"/>
                                                </li>
                                            </#list>
                                        </ol>
                                    </div>
                                </#if>
                            </div>
                            <div class="col-sm-11" style="height: 1300px;">
                                <ul id="images">
                                    <#list claimAttachList as attach>
                                        <li hidden="hidden">
                                            <img data-fileid="${attach.id}"
                                                 title="${attach.fileName}"
                                                 src="${attach.fileObjectId}"
                                                 alt="${attach.id}"/>
                                        </li>
                                    </#list>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 右部分 -->
            <div class="col-sm-4" style="background: none!important;padding-left: 0px;">
                <div class="col-sm-12 clear-padding" style="font-size: 15px;margin: 9px 0px;">
                    <div class="col-sm-3 clear-padding" style="text-align: right">估损清单名称</div>
                    <div class="col-sm-9 ">
                        <input id="assessmentReportName" validName="billCollection" type="text" class="form-control" value="${assessmentReport.code!''}" style="height: 28px !important;">
                    </div>
                </div>

                <div class="row" style="margin-bottom: 20px">
                    <div style="margin-left: 5px;">
                        <img class="marginLeft15" src="${ctx}/images/u48.svg" align="left" style="margin-top: 3px;"/>
                        <span style="font-size: 16px;font-weight: bold;margin-left: 10px">机动车基本信息 </span>
                    </div>
                    <div name="dataCollectArea" class="row">
                        <div class="col-sm-12 clear-padding">
                            <div class="col-sm-6 clear-padding" style="font-size: 15px;margin: 9px 0px;">
                                <div class="col-sm-3 clear-padding" style="text-align: right">车牌号码</div>
                                <div class="col-sm-9 ">
                                    <input type="text" validName="billCollection" id="carNumber" data-valid="none" name="carNumber" value="${assessmentFather.carNumber!''}" class="form-control"/>
                                </div>
                            </div>
                            <div class="col-sm-6 clear-padding" style="font-size: 15px;margin: 9px 0px;">
                                <div class="col-sm-3 clear-padding" style="text-align: right">厂牌型号</div>
                                <div class="col-sm-9 ">
                                    <input type="text" validName="billCollection" id="carModel" data-valid="none" name="carModel" value="${assessmentFather.carModel!''}" class="form-control"/>
                                </div>
                            </div>
                            <div class="col-sm-6 clear-padding" style="font-size: 15px;margin: 9px 0px;">
                                <div class="col-sm-3 clear-padding" style="text-align: right">车辆识别代码</div>
                                <div class="col-sm-9 ">
                                    <input type="text" validName="billCollection" id="carEncoding" data-valid="none" name="carEncoding" value="${assessmentFather.carEncoding!''}" class="form-control"/>
                                </div>
                            </div>
                            <div class="col-sm-6 clear-padding" style="font-size: 15px;margin: 9px 0px;">
                                <div class="col-sm-3 clear-padding" style="text-align: right">初次登记日期</div>
                                <div class="col-sm-9 ">
                                    <input type="text" validName="billCollection" id="firstRegistrationTime" data-valid="isTime" name="firstRegistrationTime" value="${(assessmentFather.firstRegistrationTime?string('yyyyMMdd'))!''}" class="form-control"/>
                                </div>
                            </div>
                            <div class="col-sm-6 clear-padding" style="font-size: 15px;margin: 9px 0px;">
                                <div class="col-sm-3 clear-padding" style="text-align: right">行驶证车主</div>
                                <div class="col-sm-9 ">
                                    <input type="text" validName="billCollection" id="carOwner" name="carOwner" data-valid="none" value="${assessmentFather.carOwner!''}" class="form-control"/>
                                </div>
                            </div>
                            <div class="col-sm-6 clear-padding" style="font-size: 15px;margin: 9px 0px;">
                                <div class="col-sm-3 clear-padding" style="text-align: right">定损时间</div>
                                <div class="col-sm-9 ">
                                    <input type="text" validName="billCollection" data-valid="isTime" id="lossAssessmentTime" name="lossAssessmentTime" value="${(assessmentFather.lossAssessmentTime?string('yyyyMMdd'))!''}" class="form-control"/>
                                </div>
                            </div>
                            <div class="col-sm-6 clear-padding" style="font-size: 15px;margin: 9px 0px;">
                                <div class="col-sm-3 clear-padding" style="text-align: right">修理厂</div>
                                <div class="col-sm-9 ">
                                    <input type="text" validName="billCollection" id="repairFactory" data-valid="none" name="repairFactory" value="${assessmentFather.repairFactory!''}" class="form-control"/>
                                </div>
                            </div>
                            <div class="col-sm-6 clear-padding" style="font-size: 15px;margin: 9px 0px;">
                                <div class="col-sm-3 clear-padding" style="text-align: right">是否4S</div>
                                <div class="col-sm-9 ">
                                    <select style="width:100%;" validName="billCollection" class="kp-select2 inputStyle form-control" data-valid="none" id="is4S" name="is4S">
                                        <option value="是" <#if assessmentFather.is4S='是' >selected</#if>>是</option>
                                        <option value="否" <#if assessmentFather.is4S='否' >selected</#if>>否</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>


                <div class="row" style="">
                    <div style="margin-left: 5px;">
                        <img class="marginLeft15" src="${ctx}/images/u48.svg" align="left" style="margin-top: 3px;"/>
                        <span style="font-size: 16px;font-weight: bold;margin-left: 10px">维修定损 </span>
                    </div>
                    <div name="dataCollectArea" class="row" style="height:700px;overflow-y: scroll">

                        <#if enumMapByParentCode?exists>
                            <#list enumMapByParentCode.keySet() as code>
                                <div class="row" name="dataCollectAreaSubjet" code="${code}" name="${enumMapByParentCode.get(code)}">
                                    <div class="col-sm-8" style="margin: 15px;">
                                        <div class="subject-name">${enumMapByParentCode.get(code)}</div>
                                    </div>
                                    <div class="col-sm-12 ">
                                        <div class="col-sm-1 clear-padding" style="font-size: 15px;margin: 9px 0px;">

                                        </div>
                                        <div class="col-sm-4 clear-padding">
                                            <div class="col-sm-3 clear-padding" style="font-size: 15px;display: inline-block;text-align: center;line-height: 15px">名称</div>
                                            <div class="col-sm-9 ">
                                                <input type="text" style="padding: 0px !important;" class="form-control" disabled/>
                                            </div>
                                        </div>
                                        <#--<div class="col-sm-3 clear-padding">
                                            <div class="col-sm-4 clear-padding" style="font-size: 15px;display: inline-block;text-align: center;line-height: 15px">维修报价</div>
                                            <div class="col-sm-8 ">
                                                <input type="text" style="padding: 0px !important;" class="form-control" disabled/>
                                            </div>
                                        </div>-->
                                        <div class="col-sm-3 clear-padding">
                                            <div class="col-sm-4 clear-padding" style="font-size: 15px;display: inline-block;text-align: center;line-height: 15px">定损金额</div>
                                            <div class="col-sm-8 ">
                                                <input type="text" style="padding: 0px !important;" class="form-control" disabled/>
                                            </div>
                                        </div>

                                        <div class="col-sm-3 clear-padding">
                                            <div class="col-sm-4 clear-padding" style="font-size: 15px;display: inline-block;text-align: center;line-height: 15px">核损金额</div>
                                            <div class="col-sm-8 ">
                                                <input type="text" style="padding: 0px !important;" class="form-control" disabled/>
                                            </div>
                                        </div>
                                        <div class="col-sm-1 clear-padding">
                                            <div class="icon-plus" id="${code}AddDataRow" onclick="addDataRowClick('${code}')"></div>
                                        </div>
                                    </div>
                                    <div class="row" id="${code}collectArea">

                                    </div>
                                    <div class="row">
                                        <div class="col-sm-12" style="margin: 15px;">
                                            <span style="font-size: 15px;float:right;color: red;font-weight: bold" id="${code}LossAssessment"></span>
                                        </div>
                                    </div>
                                </div>
                            </#list>
                        </#if>

                        <div class="row">
                            <div class="col-sm-12 clear-padding" style="margin: 15px;">
                                <div class="col-sm-3 clear-padding" style="font-size: 24px;font-weight: bold;text-align: center">残值</div>
                                <div class="col-sm-9">
                                    <input validName="billCollection" id="residualValue" name="residualValue" type="text" class="form-control" value="${assessmentFather.residualValue!''}"  data-valid="isNumberAndDecimalPoint" style="height: 28px !important;"/>
                                </div>
                            </div>
                            <div class="col-sm-12 clear-padding" style="margin: 15px;">
                                <div class="col-sm-3 clear-padding" style="font-size: 15px;font-weight: bold;text-align: center">残值核损金额</div>
                                <div class="col-sm-9">
                                    <input validName="billCollection" readonly id="residualNuclearLossValue" name="residualNuclearLossValue" type="text" class="form-control" value="${assessmentFather.residualNuclearLossValue!''}"  data-valid="isNumberAndDecimalPoint" style="height: 28px !important;"/>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12 clear-padding" style="margin: 15px;">
                                <div class="col-sm-3 clear-padding" style="font-size: 24px;font-weight: bold;text-align: center">理算金额</div>
                                <div class="col-sm-9">
                                    <input validName="billCollection" id="verifyAmout" name="verifyAmout" type="text" class="form-control" value="${assessmentFather.verifyAmout!''}" data-valid="isNumberAndDecimalPoint" style="height: 28px !important;"/>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12 clear-padding" style="margin: 15px;">
                                <div class="col-sm-3 clear-padding" style="font-size: 24px;font-weight: bold;text-align: center">理算描述</div>
                                <div class="col-sm-9">
                                    <input validName="billCollection" id="verifyDetail" name="verifyDetail" type="text" class="form-control" value="${assessmentFather.verifyDetail!''}" data-valid="none" style="height: 28px !important;"/>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12 clear-padding" style="margin: 15px;">
                                <div class="col-sm-3 clear-padding" style="font-size: 15px;font-weight: bold;text-align: center">责任比例（百分制）</div>
                                <div class="col-sm-9">
                                    <input validName="billCollection" id="dutyRate" name="dutyRate" type="text" class="form-control" value="${assessmentFather.dutyRate!''}" data-valid="isRate" style="height: 28px !important;"/>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12 clear-padding" style="margin: 15px;">
                                <div class="col-sm-3 clear-padding" style="font-size: 24px;font-weight: bold;text-align: center">扣除费用</div>
                                <div class="col-sm-9">
                                    <input validName="billCollection" id="deductFee" name="deductFee" type="text" class="form-control" value="${assessmentFather.deductFee!''}" data-valid="isNumberAndDecimalPoint" style="height: 28px !important;"/>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12" style="margin: 15px;">
                                <span style="font-size: 24px;color: red;font-weight: bold" id="lossAssessmentSum" sumMoney="0.00">定损合计：0 元</span>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12" style="margin: 15px;">
                                <span style="font-size: 24px;color: red;font-weight: bold" id="nuclearLossSum" sumMoney="${assessmentFather.nuclearLossSum!''}">核损合计：${assessmentFather.nuclearLossSum!''}元</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

</body>
</html>
