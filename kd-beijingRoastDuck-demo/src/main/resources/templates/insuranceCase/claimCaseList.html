<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <link href="${ctx}/metronic/global/plugins/select2/css/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/select2/css/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>

    <script src="${ctx}/metronic/global/plugins/select2/js/select2.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/plugins/select2/js/select2.full.min.js" type="text/javascript"></script>
    <script type="text/javascript">
        $(document).ready(function () {
            // 业务标签 select2初始化
            var labelList = [];
            <#if labelShowMap?? && (labelShowMap.keySet()?size>0)>
                <#list labelShowMap.keySet() as key>
                    labelList.push({id:'${key}',text:'${labelShowMap.get(key).msg}'})
                </#list>
            </#if>
            console.log(labelList);
            $("#label").select2({
                placeholder: "请选择",
                width: null,
                data: labelList
            });
            var nowLabel =[];
            <#if claimCaseVo.label?exists>
                <#list claimCaseVo.label?split(",") as code>
                    nowLabel.push("${code}");
                </#list>
                console.log(nowLabel);
                $("#label").val(nowLabel).trigger('change');
            </#if>


            var products = [];
            <#if productList?? && (productList?size>0)>
            <#list productList as product>
                $("#productList").append(`<option value="${product.name}">${product.name}</option>`);
            </#list>
            </#if>
            /*$("#productName").select2({
                placeholder: "请选择",
                width: null,
                data: products
            });*/



            var applyTypeList = [];
            applyTypeList.push({id:" ",text:"请选择"})
            var nowAppyType ="";
            <#if appyTypeMap?? && (appyTypeMap.keySet()?size>0)>
            <#list appyTypeMap.keySet() as key>
            applyTypeList.push({id:'${key}',text:'${appyTypeMap.get(key).parentTypeName}-${appyTypeMap.get(key).childTypeName}'})
            <#if (claimCaseVo.applyType!'-')==key>
                nowAppyType = '${key}';
            </#if>
            </#list>
            </#if>
            console.log(applyTypeList);
            $("#applyType").select2({
                placeholder: "请选择",
                width: null,
                data: applyTypeList
            });
            if(nowAppyType!=""){
                $("#applyType").val([nowAppyType]).trigger('change');
            }

            $(".logListInfo").on("click", ".rowInfo", function (e) {
                e.stopPropagation();
                let icon = $(this).children(1).children(1);
                var iconClass = icon.attr("class");
                if (iconClass == 'icon-plus') {
                    icon.attr("class", "minus");
                    $(this).next().show();
                }
                if (iconClass == 'minus') {
                    icon.attr("class", "icon-plus");
                    $(this).next().hide();
                }
            });
        });


        var scrollTop;  // 定义滚动高度

        function tabChange(obj) {
            let tabClass = $(obj).attr("class");
            if(tabClass.indexOf("li-blue")==-1){
                $("#tabType").val($(obj).val());
                $("#searchForm").submit();
            }
        }

        function page(n,s){
            $("#pageNum").val(n);
            $("#pageSize").val(s);
            $("#searchForm").submit();
            return false;
        }

        //补发短信
        function reSendMessage(claimCaseId) {
            layer.open({
                title: "补发短信",
                type: 1,
                content: $('#reSendMessageContainer'),
                area: ['600px', '300px'],
                fixed: false,
                offset: 't',
                btn: ['确认', '取消'],
                closeBtn: 0,
                yes: function (index, layero) {
                    let check = $('#reSendMessageContainer').find('span.checked');
                    let children = check.children();
                    let code = children.attr("code");
                    if (typeof code == "undefined" || code == null) {
                        layer.msg("请选择短信类型！！！", {
                            icon: 2,
                            time: 2000
                        });
                        return;
                    }
                    let reissueReason = $("#reissueReason").val();
                    if (reissueReason.trim() == "") {
                        layer.msg("请输入补发原因！！！", {
                            icon: 2,
                            time: 2000
                        });
                        return;
                    }
                    var formData = new FormData();
                    formData.append("claimCaseId", claimCaseId);
                    formData.append("messageCode", code);
                    formData.append("reissueReason", reissueReason);
                    $.ajax({
                        url: "${ctx}/insuranceCaseController/reSendMessage",
                        type: 'POST',
                        data: formData,
                        async: true,
                        cache: false,
                        contentType: false,
                        processData: false,
                        success: function (data) {
                            var result = eval("(" + data + ")");
                            if (result.ret == "0") {
                                layer.msg("发送成功", {
                                    icon: 1,
                                    time: 2000
                                },function () {
                                    layer.closeAll();
                                });
                            } else {
                                layer.msg(result.msg, {
                                    icon: 2,
                                    time: 2000
                                });
                            }
                        },
                        error: function (data) {
                            var result = eval("(" + data + ")");
                            alert(result.msg);
                        }
                    });
                },
                btn2: function (index, layero) {
                    layer.closeAll();
                }
            });
        }


        //编辑标签
        function openLabelAction(obj,claimCaseId,labelStr) {
            scrollTop = calculationScrollTop();
            $("#editLabelContainer").find("input").parent().attr("class","");
            if(labelStr){
                for( let data of labelStr.split(",")){
                    $("#editLabelContainer").find("input[code='"+data+"']").parent().attr("class","checked");
                }
            }
            layer.open({
                title: "编辑标签",
                type: 1,
                content: $('#editLabelContainer'),
                area: ['650px', '300px'],
                fixed: false,
                offset: 't',
                btn: ['确认', '取消'],
                closeBtn: 0,
                shadeClose: true,
                offset: scrollTop,
                yes: function (index, layero) {
                    let dataList = [];
                    let check = $('#editLabelContainer').find('span.checked');
                    $.each(check,function (index,obj) {
                        let code=$(this).children().attr("code");
                        if(!(typeof code == "undefined" || code == null)){
                            dataList.push(code);
                        }
                    })
                    /*if (dataList.length == 0) {
                        layer.msg("至少选择一个标签类型！！！", {
                            icon: 2,
                            time: 2000,
                            offset: scrollTop
                        });
                        return;
                    }*/
                    var formData = new FormData();
                    formData.append("claimCaseId", claimCaseId);
                    formData.append("labelCode", dataList.join(","));
                    $.ajax({
                        url: "${ctx}/insuranceCaseController/editCaseLabelAction",
                        type: 'POST',
                        data: formData,
                        async: true,
                        cache: false,
                        contentType: false,
                        processData: false,
                        success: function (data) {
                            var result = eval("(" + data + ")");
                            if (result.ret == "0") {
                                layer.msg("修改成功", {
                                    icon: 1,
                                    time: 3000,
                                    offset: scrollTop
                                },function () {
                                    window.location.reload();
                                });
                            } else {
                                layer.msg(result.msg, {
                                    icon: 2,
                                    time: 2000,
                                    offset: scrollTop
                                });
                            }
                        },
                        error: function (data) {
                            var result = eval("(" + data + ")");
                            alert(result.msg);
                        }
                    });
                },
                btn2: function (index, layero) {
                    layer.closeAll();
                }
            });
        }

        function closeCase(caseId) {
            var content = '<div class="row" style="padding: 0px 30px" ><div class="col-sm-3">&nbsp;</div><textarea rows="5" class="col-sm-6 form-control" name="closeCaseMsg" id="closeCaseMsg" autocomplete="off" placeholder="请输入关闭案件原因"></textarea></div>' ;
            layer.open({
                type: 1,
                content: content,
                title: '关闭案件',
                area:  ['500px', '300px'],
                btn: ['确认','取消'],
                yes: function(index,obj){
                    console.log(typeof $("#closeCaseMsg").val());
                    var closeCaseMsg = $("#closeCaseMsg").val();
                    if(typeof closeCaseMsg != 'string' || closeCaseMsg.trim()==''){
                        layer.msg("关闭案件原因不能为空", {icon: 2, time: 3000,offset: 'r'});
                    }else {
                        var formData = new FormData();
                        formData.append("claimCaseId", caseId);
                        formData.append("type", 100);
                        formData.append("description", closeCaseMsg);
                        $.ajax({
                            url: "${ctx}/insuranceCaseController/closeCase",
                            type: 'POST',
                            data: formData,
                            async: false,
                            cache: false,
                            contentType: false,
                            processData: false,
                            success: function (data) {
                                var result = eval("(" + data + ")");
                                if (result.ret == "0") {
                                    setTimeout(function(){
                                        layer.msg('关闭案件成功', {
                                            icon: 1,
                                            time: 1000 //1秒关闭（如果不配置，默认是3秒）
                                        }, function () {
                                            window.location.reload();
                                        });
                                    },1200);
                                } else {
                                    layer.msg(result.msg, {
                                        icon: 1,
                                        time: 1500 //1秒关闭（如果不配置，默认是3秒）
                                    }, function (index) {
                                        layer.close(index);
                                    });
                                }
                            },
                            error: function (data) {
                                var result = eval("(" + data + ")");
                                alert(result.msg);
                            }
                        });
                    }
                }
            });
        }


        //查看详情
        function caseDetail(claimCaseId) {
            window.location.href="${ctx}/insuranceCaseController/caseDetail?caseId="+claimCaseId+"&type=1";
        }

        function caseVerify(claimCaseId) {
            window.open("${ctx}/insuranceCaseController/claimVerify?claimCaseId="+claimCaseId);
        }

        function claimVerify() {
            window.open("${ctx}/insuranceCaseController/claimVerify");
        }

        //时间格式化
        function timeStamp2String(time) {
            var datetime = new Date();
            datetime.setTime(time);
            var year = datetime.getFullYear();
            var month = datetime.getMonth() + 1 < 10 ? "0" + (datetime.getMonth() + 1) : datetime.getMonth() + 1;
            var date = datetime.getDate() < 10 ? "0" + datetime.getDate() : datetime.getDate();
            var hour = datetime.getHours() < 10 ? "0" + datetime.getHours() : datetime.getHours();
            var minute = datetime.getMinutes() < 10 ? "0" + datetime.getMinutes() : datetime.getMinutes();
            var second = datetime.getSeconds() < 10 ? "0" + datetime.getSeconds() : datetime.getSeconds();
            return year + "-" + month + "-" + date + " " + hour + ":" + minute + ":" + second;
        }

        function openExportCaseInfo(caseId) {
            $("#push-elm-log").html("");
            $('#exportCaseInfoContainer').find('span.checked').removeClass("checked");
            var formData = new FormData();
            formData.append("claimCaseId", caseId);
            $.ajax({
                url: "${ctx}/insuranceCaseController/getPushElmLog",
                type: 'POST',
                data: formData,
                async: false,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    var result = eval("(" + data + ")");
                    if (result.ret == "0") {
                        var data = eval("(" + result.msg + ")");
                        $.each(data, function(index, log) {
                            var trs = `<tr class="rowInfo">
                                            <td width="10%" align="center">
                                                <div class="icon-plus"></div>
                                            </td>
                                            <td width="20%">`+log.position+`</td>
                                            <td width="20%">`+log.status+`</td>
                                            <td width="20%">`+log.creator+`</td>
                                            <td width="30%">`+timeStamp2String(log.createTime)+`</td>
                                        </tr>
                                        <tr class="detailsInfo">
                                            <td width="10%" align="center"></td>
                                            <td width="90%" colspan="4" style="overflow-x: visible;">
                                                请求参数：`+log.reqData+`<br>
                                                响应结果：`+log.resData+`<br>
                                            </td>
                                        </tr>`;
                            $("#push-elm-log").append(trs);
                        });
                    }else {
                        layer.msg(result.msg, {
                            icon: 2,
                            time: 2000,
                            offset: scrollTop
                        }, function (index) {
                            layer.close(index);
                        });
                        return;
                    }
                },
                error: function (data) {
                    var result = eval("(" + data + ")");
                    alert(result);
                }
            });

            scrollTop = calculationScrollTop();
            $("#modifyExportCaseStatus").attr("data-modify-status-case-id",caseId);
            layer.open({
                title: "导出案件信息",
                type: 1,
                content: $('#exportCaseInfoContainer'),
                area: ['900px', '500px'],
                fixed: false,
                offset: 't',
                btn: ['确认', '取消'],
                yes: function (index, layero) {
                    let check = $('#exportCaseInfoContainer').find('span.checked');
                    let checkChoose = [];
                    let reqStr = "claimCaseIdList="+caseId;
                    $.each(check,function (index,obj) {
                        let children = $(this).children();
                        let code = children.attr("code");
                        checkChoose.push(code);
                        reqStr+="&"+code+"=1";
                    });
                    if(checkChoose.length==0){
                        layer.msg("请选择导出案件信息类型！！！", {
                            icon: 2,
                            time: 2000,
                            offset: scrollTop
                        });
                        return;
                    }
                    window.location.href="${ctx}/downloadCenterController/downloadCaseAttach?"+reqStr;
                    layer.close(index);
                },
                btn2: function (index, layero) {
                    layer.closeAll();
                }
            });
        }

        // 计算滚动高度
        function calculationScrollTop() {
            var ifm = parent.document.getElementById("contentFrame");
            scrollTop = $(window.parent).scrollTop() + 150;
            if (!ifm) {
                scrollTop = 300;
            }
            return scrollTop + "px";
        }


        //补充银行卡界面
        function supplementContactInformation(claimCaseId){
            var openWindowWidth = $(document).width() * 0.5 + "px";
            layer.open({
                title: "补充信息",
                type: 2,
                area: openWindowWidth,
                offset: 't',
                fix: false, //不固定
                maxmin: true,
                shadeClose: true,
                content: '${ctx}/insuranceCaseController/supplementContactInformation?claimCaseId='+claimCaseId,
                success: function (layero, index) {
                    layer.iframeAuto(index);
                }
            });
        }

        //案件导出数据后修改状态
        function modifyExportCaseStatus(obj) {
            let caseId = $("#modifyExportCaseStatus").attr("data-modify-status-case-id");
            scrollTop = calculationScrollTop();
            layer.confirm("是否修改状态！！！",{offset: scrollTop},function (index) {
                var formData = new FormData();
                formData.append("claimCaseId", caseId);
                $.ajax({
                    url: "${ctx}/insuranceCaseController/modifyExportCaseStatus",
                    type: 'POST',
                    data: formData,
                    async: false,
                    cache: false,
                    contentType: false,
                    processData: false,
                    success: function (data) {
                        var result = eval("(" + data + ")");
                        if (result.ret != "0") {
                            layer.msg(result.msg, {
                                icon: 2,
                                time: 1500,//1秒关闭（如果不配置，默认是3秒）
                                offset: scrollTop
                            }, function (index1) {
                                layer.close(index1);
                            });
                        }else {
                            layer.msg(result.msg, {
                                icon: 1,
                                time: 3000,//1秒关闭（如果不配置，默认是3秒）
                                offset: scrollTop
                            }, function (index1) {
                                window.location.reload();
                            });
                        }
                    },
                    error: function (data) {
                        var result = eval("(" + data + ")");
                    }
                });
            })
        }
    </script>
    <style>
        .form-active > div {
            border-top-left-radius: 5px;
            border-top-right-radius: 5px;
            border: 1px solid #e7ecf1;
            border-bottom: 0;
        }

        .form-active > ul {
            margin: 0px;
            padding: 0px;
            list-style: none;
        }

        .li-default {
            display: table-cell;
            border-bottom: 0;
            padding: 6px 22px;
            font-size: 16px;
            font-weight: bold;
            color: #3D3D3D;
            cursor: pointer;
        }

        .li-default:hover {
            background: #0b94ea;
            color: #fff;
        }

        .li-default:nth-of-type(2) {
            border-left: 1px solid #e7ecf1;
            border-right: 1px solid #e7ecf1;
        }

        .li-default:nth-of-type(3) {
            border-right: 1px solid #e7ecf1;
        }

        .li-blue {
            background: #0b94ea;
            color: #fff;
        }

        .labelGroup span{
            margin-left: 5px;
            margin-right: 5px;
            background-color: #1676FF;
            color: white;
            border-radius: 2px;
        }

        .applyTypeGroup span{
            margin-left: 5px;
            margin-right: 5px;
            background-color: #c9c8c8;
            border-radius: 2px;
        }
        .applyTypeGroup .AA001,.AA002,.AA003,.AA004{
            background-color: #1676ff !important;
            color: white;
        }
        .applyTypeGroup .AB001,.AB002,.AB003,.AB004{
            background-color: #ff6633 !important;
            color: white;
        }
        .applyTypeGroup .AC001,.AC002,.AC003,.AC004{
            background-color: #CA0000 !important;
            color: white;
        }

        .td-overflow {
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            max-width: 100px;
        }

        td > a , td > span {
            display: inline-block;
            margin: 3px;
        }

        .span-type {
            display: inline-block;
            background: #0b94ea;
            color: #fff;
            font-size: 10px;
            border-radius: 5px;
            margin:3px;
            padding: 2px;
        }

        .logListInfo {
            margin-bottom: 2%;
        }

        .logListInfo table {
            border: 1px solid #C2C2C2;
        }

        .logListInfo .detailsInfo {
            display: none;
            padding-left: 8%;
        }

        .minus {
            border: 1px solid black;
            border-radius: 50%;
            width: 13px;
            height: 13px;
            position: relative;
            margin-top: 3%;
        }

        .minus::before {
            content: '';
            position: absolute;
            left: 2px;
            top: 5px;
            width: 7px;
            border-top: 1px solid;
        }

        .logListInfo .detailsInfo > td {
            word-wrap:break-word;
            word-break:break-all;
            text-align: left;
        }
        .logListInfo .rowInfo:hover{
            cursor: pointer;
        }

        .label-title {
            font-size: 18px !important;
        }
    </style>
</head>
<body>
<#--补发短信-->
<div id="reSendMessageContainer" style="display: none;width: 100%;height: 100%">
    <div class="row" style="padding-top: 30px;margin: 0px 0px">
        <div class="col-sm-3">
            <span class="pull-right" style="font-size: 10px;color: #7f7f7f">选择短信类型：</span>
        </div>
        <div class="col-sm-9">
            <#if messageType?? && (messageType.keySet()?size>0)>
                <#list messageType.keySet() as key>
                    <div class="col-sm-3" style="padding-left: 0px;margin-bottom: 10px;display: flex">
                        <input name="messageType" style="left: 0px;right: 0px;margin-left: 0px;" code="${key}"
                               type="radio">${messageType.get(key)}
                    </div>
                </#list>
            </#if>
            <textarea class="form-control" id="reissueReason" rows="5" placeholder="请输入补发原因"></textarea>
        </div>
    </div>
</div>

<#--补发短信-->
<div id="editLabelContainer" style="display: none;width: 100%;height: 100%">
    <div class="row" style="padding-top: 30px;margin: 0px 0px">
        <div class="col-sm-3">
            <span class="pull-right" style="font-size: 10px;color: #7f7f7f">请选择标签：</span>
        </div>
        <div class="col-sm-9">
            <#if labelShowMap?? && (labelShowMap.keySet()?size>0)>
                <#list labelShowMap.keySet() as key>
                    <div class="col-sm-3" style="padding-left: 0px;margin-bottom: 10px;">
                        <input name="messageType" style="left: 0px;right: 0px;margin-left: 0px;" code="${key}"
                               type="checkbox"><span class="${key} span-type" style="color: ${labelShowMap.get(key).fontColor};background-color: ${labelShowMap.get(key).color}"><#if labelShowMap.get(key)??>${labelShowMap.get(key).msg}<#else>${key}</#if></span>
                    </div>
                </#list>
            </#if>
        </div>
    </div>
</div>

<#--导出案件信息-->
<div id="exportCaseInfoContainer" style="display: none;width: 100%;height: 100%">
    <div class="row" style="padding-top: 30px;margin: 0px 0px">
        <div class="col-sm-3">
            <span class="pull-right" style="font-size: 10px;color: #7f7f7f">选择导出类型：</span>
        </div>
        <div class="col-sm-9">
            <div class="col-sm-3" style="padding-left: 0px;margin-bottom: 10px;">
                <input name="messageType" style="left: 0px;right: 0px;margin-left: 0px;" code="isNeedAttach"
                       type="checkbox" id="isNeedAttach"><label for="isNeedAttach">导出影像</label>
            </div>
            <div class="col-sm-3" style="padding-left: 0px;margin-bottom: 10px;">
                <input name="messageType" style="left: 0px;right: 0px;margin-left: 0px;" code="isNeedExcel"
                       type="checkbox" id="isNeedExcel"><label for="isNeedExcel">导出文件</label>
            </div>
            <div class="col-sm-3" style="padding-left: 0px;margin-bottom: 10px;">
                <input name="messageType" style="left: 0px;right: 0px;margin-left: 0px;" code="isNeedPushElm"
                       type="checkbox" id="isNeedPushElm"><label for="isNeedPushElm">推送风神</label>
            </div>
        </div>
    </div>
    <div class="row" style="padding: 15px 30px;margin: 0px 0px">
        <button type="button" class="btn pull-right" style="background-color: #1676FF;color: white"
                id="modifyExportCaseStatus" onclick="modifyExportCaseStatus(this)">
            修改状态
        </button>
    </div>
    <div class="row logListInfo" style="margin-left: 20px;margin-right: 20px;">
        <div class="block-head-label col-sm-12" style="margin-bottom: 20px;">
            <span class="label-title">日志信息</span>
        </div>
        <div class="col-sm-12">
            <table class="table">
                <thead>
                <tr>
                    <td width="10%"></td>
                    <td width="20%">岗位</td>
                    <td width="20%">类型</td>
                    <td width="20%">人员</td>
                    <td width="30%">时间</td>
                </tr>
                </thead>
                <tbody id="push-elm-log">
                </tbody>
            </table>
        </div>
    </div>
</div>
<div class="row">
    <div class="col-sm-12">
        <div class="portlet light portlet-fit bordered">
            <div class="portlet-title">
                <ul class="page-breadcrumb breadcrumb">
                    <li><span>风神</span> <i class="fa fa-circle"></i></li>
                    <li><span class="active">赔案管理</span></li>
                </ul>
            </div>
            <div class="portlet-body">

                <!-- BEGIN FORM-->
                <form id="searchForm" class="form-horizontal" action="${ctx}/insuranceCaseController/claimCaseList"
                      method="post">
                    <input id="pageNum" name="pageNum" type="hidden" value="${page.pageNum}"/>
                    <input id="pageSize" name="pageSize" type="hidden" value="${page.pageSize}"/>
                    <div class="form-body">
                        <div class="row">
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">报案号：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <input type="text" class="form-control" name="claimCaseNo" id="claimCaseNo"
                                               value="${claimCaseVo.claimCaseNo}"
                                               placeholder="请输入"/>
                                    </div>
                                </div>
                            </div>

                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">报案人姓名：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <input type="text" class="form-control" name="applyName" id="applyName"
                                               value="${claimCaseVo.applyName}"
                                               placeholder="请输入"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">标签：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <select class="form-control select2-multiple"  name="label"
                                                id="label" multiple>
                                            <option value="">请选择</option>
                                            <#--<#if labelShowMap?? && (labelShowMap.keySet()?size>0)>
                                                <#list labelShowMap.keySet() as key>
                                                    <option <#if (claimCaseVo.label!'-')==key>selected</#if>value="${key}">
                                                        ${labelShowMap.get(key).msg}
                                                    </option>
                                                </#list>
                                            </#if>-->
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">出险人姓名：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <input type="text" class="form-control" name="treatName" id="treatName"
                                               value="${claimCaseVo.treatName}"
                                               placeholder="请输入"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">出险人身份证：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <input type="text" class="form-control" name="treatIdNum" id="treatIdNum"
                                               value="${claimCaseVo.treatIdNum}"
                                               placeholder="请输入"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">产品名称：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <input type="text" class="form-control" name="productName" id="productName" list="productList" autocomplete="off"
                                               value="${claimCaseVo.productName}"
                                               placeholder="请输入"/>
                                        <datalist id="productList">
                                            <option value="">请选择</option>
                                        </datalist>
                                        <#--<select class="form-control select2-multiple"  name="productName"
                                                id="productName">
                                            <option value="">请选择</option>
                                        </select>-->
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">报案时间：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <div class="input-group date-picker input-daterange"
                                             data-date-format="yyyy-mm-dd">
                                            <input type="text" class="form-control" name="startDateStart"
                                                   id="startDateStart"
                                                   value="${claimCaseVo.startDateStart}">
                                            <span class="input-group-addon"><font style="vertical-align: inherit;"><font
                                                            style="vertical-align: inherit;"> 至 </font></font></span>
                                            <input type="text" class="form-control" name="startDateEnd"
                                                   id="startDateEnd"
                                                   value="${claimCaseVo.startDateEnd}">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <#--<div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">估损金额：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <div class="input-group">
                                            <input type="text" class="form-control" name="appraisalAmountMin"
                                                   id="appraisalAmountMin"
                                                   value="${claimCaseVo.appraisalAmountMin}">
                                            <span class="input-group-addon"><font style="vertical-align: inherit;"><font
                                                            style="vertical-align: inherit;"> 至 </font></font></span>
                                            <input type="text" class="form-control" name="appraisalAmountMax"
                                                   id="appraisalAmountMax"
                                                   value="${claimCaseVo.appraisalAmountMax}">
                                        </div>
                                    </div>
                                </div>
                            </div>-->
                            <#--<div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">出险类型：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <select class="form-control select2-multiple"  name="applyType"
                                                id="applyType">
                                            <option value="">请选择</option>
                                            &lt;#&ndash;<#if appyTypeMap?? && (appyTypeMap.keySet()?size>0)>
                                                <#list appyTypeMap.keySet() as key>
                                                    <option <#if (claimCaseVo.applyType!'-')==key>selected</#if>value="${key}">
                                                        ${appyTypeMap.get(key).parentTypeName}-${appyTypeMap.get(key).childTypeName}
                                                    </option>
                                                </#list>
                                            </#if>&ndash;&gt;
                                        </select>
                                    </div>
                                </div>
                            </div>-->
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">出险时间：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <div class="input-group date-picker input-daterange"
                                             data-date-format="yyyy-mm-dd">
                                            <input type="text" class="form-control" name="treatDateStart"
                                                   id="treatDateStart"
                                                   value="${claimCaseVo.treatDateStart}">
                                            <span class="input-group-addon"><font style="vertical-align: inherit;"><font
                                                            style="vertical-align: inherit;"> 至 </font></font></span>
                                            <input type="text" class="form-control" name="treatDateEnd"
                                                   id="treatDateEnd"
                                                   value="${claimCaseVo.treatDateEnd}">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="form-actions">
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="btn-group pull-right" style="margin-bottom: 10px;">
                                    <button id="query" type="submit" class="btn green" style="margin-bottom: 10px;">查询
                                    </button>
                                    <button id="query" type="button" class="btn btn-info" style="margin-left: 10px;" onclick="claimVerify()">
                                        理赔审核
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="form-active">
                        <div class="col-sm-4" style="padding: 0px">
                            <ul class="row" style="padding: 0px;margin: 0px">
                                <li class="<#if tabType==0>li-blue</#if> li-default col-sm-6" value="0" onclick="tabChange(this)">全部
                                </li>
                                <#--<li class="<#if tabType==1>li-blue</#if> li-default col-sm-4" value="1" onclick="tabChange(this)">超时
                                </li>-->
                                <li class="<#if tabType==2>li-blue</#if> li-default col-sm-6" value="2" onclick="tabChange(this)">疑难
                                </li>
                            </ul>
                        </div>
                    </div>
                    <input type="hidden" id="tabType" value="${tabType}" name="tabType">
                </form>
                <table class="table table-striped table-bordered table-hover table-header-fixed">
                    <thead>
                    <tr>
                        <th width="8%">报案号</th>
                        <th width="5%">报案人姓名</th>
                        <th width="10%">标签</th>
                        <th width="7%">出险人姓名</th>
                        <th width="8%">出险人身份证</th>
                        <th width="8%">产品名称</th>
                        <th width="5%">估损金额</th>
                        <th width="7%">报案时间</th>
                        <th width="7%">出险时间</th>
                        <th width="10%">出险类型</th>
                        <th width="5%">赔款金额</th>
                        <th width="7%">案件状态</th>
                        <th width="3%">责任人</th>
                        <th width="10%">功能</th>
                    </tr>
                    </thead>
                    <tbody>
                    <#list page.list as vo>
                        <tr>
                            <td title="">${vo.claimCaseNo}</td>
                            <td title="">${vo.applyName}</td>
                            <td title="" class="labelGroup">
                                <#if vo.label??>
                                    <#list vo.label.split(",") as key>
                                        <#if (key?trim)!="">
                                            <span class="${key} span-type" style="color: ${labelShowMap.get(key).fontColor};background-color: ${labelShowMap.get(key).color}"><#if labelShowMap.get(key)??>${labelShowMap.get(key).msg}<#else>${key}</#if></span>
                                        </#if>
                                    </#list>
                                </#if>
                            </td>
                            <td title="">${vo.treatName}</td>
                            <td title="">${vo.treatIdNum}</td>
                            <td>${vo.productName}</td>
                            <td>${vo.appraisalAmount}</td>
                            <td>${vo.startDate?string["yyyy-MM-dd HH:mm:ss"]}</td>
                            <td>${vo.treatDate?string["yyyy-MM-dd HH:mm:ss"]}</td>
                            <td title="" class="applyTypeGroup">
                                <#if vo.applyType??>
                                    <#list vo.applyType.split(",") as key>
                                        <span class="${key} span-type"><#if appyTypeMap.get(key)??>${appyTypeMap.get(key).parentTypeName}-${appyTypeMap.get(key).childTypeName}<#else>${key}</#if></span>
                                    </#list>
                                </#if>
                            </td>
                            <td>${vo.payAmount!'--'}</td>
                            <td class="td-overflow" title="${claimCaseStatusEumMap.get(vo.status).msg}">
                                ${claimCaseStatusEumMap.get(vo.status).msg}
                            </td>
                            <td>
                                <#if vo.modifier??>
                                    <#if vo.modifier?contains("-") && vo.modifier!="-1" >
                                        ${vo.modifier?substring(0,vo.modifier?index_of("-"))}
                                    <#else>
                                        ${vo.modifier}
                                    </#if>
                                </#if>
                            </td>
                            <#--功能-->
                            <td>
                                    <#if !vo.status?contains("-1") && !vo.status?contains("aex")>
                                        <#--<a  onclick="reSendMessage('${vo.id}')">补发短信</a>-->
                                        <a  onclick="closeCase('${vo.id}')">关闭报案</a>
                                    </#if>
                                    <#if vo.status=='abx10'><a href="#" onclick="caseVerify('${vo.id}')">加急审核</a></#if>

                                    <a href="#" onclick="caseDetail('${vo.id}')">查看详情</a>
                                <a href="#"
                                       onclick="supplementContactInformation('${vo.id}')"
                                >银行信息</a>
                                <@shiro.hasPermission name="EDIT_LABEL_ACTION_EDIT">
                                    <a  onclick="openLabelAction(this,'${vo.id}','${vo.label!''}')">编辑标签</a>
                                </@shiro.hasPermission>
                                <@shiro.hasPermission name="EXPROT_CASE_INFO">
                                    <a href="#"  onclick="openExportCaseInfo('${vo.id}')">导出案件信息</a>
                                </@shiro.hasPermission>
                            </td>
                        </tr>
                    </#list>
                    </tbody>
                </table>
                <div class="modal fade" id="saveBtnModal" tabindex="-1" role="basic" aria-hidden="true">
                    <div class="modal-dialog">
                        <img src="${ctx}/images/load.gif">
                    </div>
                </div>
            </div>
            <!-- END FORM-->
        </div>
    </div>
</div>
<@sc.pagination page=page />
</body>
</html>