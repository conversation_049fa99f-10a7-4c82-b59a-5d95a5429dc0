<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>清空审核人员</title>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <link href="${ctx}/static/metronic/global/plugins/select2/css/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/static/metronic/global/plugins/select2/css/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>

    <script src="${ctx}/static/metronic/global/plugins/select2/js/select2.js" type="text/javascript"></script>
    <script src="${ctx}/static/metronic/global/plugins/select2/js/select2.full.min.js" type="text/javascript"></script>
    <style>
        .row {
            margin-top: 20px;
            margin-left: 20px;
            margin-right: 20px;
        }

        .btn-div {
            text-align: right;
            margin: 10px;
        }

        .btn-div > button {
            margin-right: 20px;
        }


        .objectListInfo {
            margin-bottom: 2%;
        }

        .objectListInfo table {
            border: 1px solid #C2C2C2;
        }

        .objectListInfo .detailsInfo {
            display: none;
            padding-left: 8%;
        }

        .minus {
            border: 1px solid black;
            border-radius: 50%;
            width: 13px;
            height: 13px;
            position: relative;
            margin-top: 3%;
        }

        .minus::before {
            content: '';
            position: absolute;
            left: 2px;
            top: 5px;
            width: 7px;
            border-top: 1px solid;
        }

        .objectListInfo .detailsInfo > td {
            word-wrap: break-word;
            word-break: break-all;
            text-align: left;
        }

        .label-title {
            font-size: 18px !important;
        }

        .blue {
            background: #0597FF;
            border-color: #ecebeb;
        }

        .clearAudit{
            color: #1676ff;
            cursor: pointer;
        }
    </style>
    <script>

        $(function () {
            // 计算滚动高度
            function calculationScrollTop() {
                var ifm = parent.document.getElementById("contentFrame");
                scrollTop = $(window.parent).scrollTop() + 150;
                if (!ifm) {
                    scrollTop = 300;
                }
                return scrollTop + "px";
            }

            $(".rowInfo").on("click",".clearAudit",function(e){
                e.stopPropagation();
                let _this = this;
                let claimCaseObjectId = $(_this).closest(".rowInfo").attr("code");
                let code = $(_this).attr("data-code");

                layer.confirm("是否确认清空该人员？",{offset: "100px"},function (index) {
                    let json = {"claimCaseObjectId":claimCaseObjectId,"code":code};

                    $.ajax({
                        url: "${ctx}/claimCaseDistributionController/clearCheckAuditer",
                        type: 'POST',
                        data: JSON.stringify(json),
                        async: false,
                        cache: false,
                        contentType: "application/json",
                        processData: false,
                        success: function (data) {
                            var result = eval("(" + data + ")");
                            if (result.ret == "0") {
                                layer.msg("成功", {
                                    icon: 1,
                                    time: 1500
                                }, function (index) {
                                    window.location.reload();
                                });
                            } else {
                                layer.msg(result.msg, {
                                    icon: 2,
                                    time: 2000
                                });
                            }
                        },
                        error: function (data) {
                            alert(data);
                            console.log(data);
                        }
                    });
                });
            })
        });

    </script>
</head>
<body>
<div>

    <div class="row">
        <div class="col-sm-12" style="margin-bottom: 20px;padding-left: 0px;padding-right: 0px;">
            <div class="col-sm-4" style="padding: 0px;">
                <div class="col-sm-12">
                    案件号：${claimCase.claimCaseNo}
                </div>
            </div>

            <div class="col-sm-4" style="padding: 0px;">
                <div class="col-sm-12" style="padding: 0px;">
                    当前状态：${claimCaseStatusEumMap.get(claimCase.status).msg}
                </div>
            </div>

        </div>


    </div>


    <div class="row objectListInfo">
        <div class="block-head-label col-sm-12" style="margin-bottom: 20px;">
            <span class="label-title">赔付列表</span>
        </div>
        <div class="col-sm-12">
            <table class="table">
                <thead>
                <tr>

                    <th width="10%">类型</th>
                    <th width="20%">赔付对象名称</th>
                    <th width="20%">预估金额</th>
                    <th width="20%">状态</th>
                    <th width="15%">初审人员</th>
                    <th width="15%">复核人员</th>
                </tr>
                </thead>
                <tbody>
                <#if claimCaseObjectList??>
                    <#list claimCaseObjectList as claimCaseObject>
                        <tr class="rowInfo" name="roleInfo" code="${claimCaseObject.id}">
                            <td width="10%">${applyTypeNewEnumMap.get(claimCaseObject.type + '-'+ claimCaseObject.category).msg}</td>
                            <td width="20%">${claimCaseObject.name}</td>
                            <td width="20%">${claimCaseObject.estimatedApprovedMoney}</td>
                            <td width="20%">${claimCaseObjectStatusEnum[claimCaseObject.status]}</td>
                            <#if  claimCaseObject.checkAuditer??>
                                <#if claimCaseObject.status!="BAX99" >
                                    <td width="15%"  class="clearAudit" data-code="check">${auditerMap[claimCaseObject.checkAuditer]}</td>
                                <#else >
                                    <td width="15%"  class="" data-code="check">${auditerMap[claimCaseObject.checkAuditer]}</td>
                                </#if>

                            <#else>
                                <td width="15%">--</td>
                            </#if>
                            <#if   claimCaseObject.insAuditer??>
                                <#if claimCaseObject.status!="BAX99" >
                                    <td width="15%"  class="clearAudit" data-code="ins">${auditerMap[claimCaseObject.insAuditer]}</td>
                                <#else >
                                    <td width="15%"  class="" data-code="ins">${auditerMap[claimCaseObject.insAuditer]}</td>
                                </#if>
                            <#else>
                                <td width="15%">--</td>
                            </#if>
                        </tr>
                    </#list>
                </#if>
                </tbody>
            </table>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-sm-12 btn-div">
        <button class="btn btn-warning" onclick="parent.layer.closeAll();">取消</button>
    </div>
</div>
</body>
</html>