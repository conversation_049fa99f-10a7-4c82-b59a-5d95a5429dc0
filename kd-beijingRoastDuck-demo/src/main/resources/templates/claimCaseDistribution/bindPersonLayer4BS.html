<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>任务分配</title>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <link href="${ctx}/static/metronic/global/plugins/select2/css/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/static/metronic/global/plugins/select2/css/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>

    <script src="${ctx}/static/metronic/global/plugins/select2/js/select2.js" type="text/javascript"></script>
    <script src="${ctx}/static/metronic/global/plugins/select2/js/select2.full.min.js" type="text/javascript"></script>
    <style>
        .row {
            margin-top: 20px;
            margin-left: 20px;
            margin-right: 20px;
        }

        .btn-div {
            text-align: right;
            margin: 10px;
        }

        .btn-div > button {
            margin-right: 20px;
        }



        .objectListInfo {
            margin-bottom: 2%;
        }

        .objectListInfo table {
            border: 1px solid #C2C2C2;
        }

        .objectListInfo .detailsInfo {
            display: none;
            padding-left: 8%;
        }

        .minus {
            border: 1px solid black;
            border-radius: 50%;
            width: 13px;
            height: 13px;
            position: relative;
            margin-top: 3%;
        }

        .minus::before {
            content: '';
            position: absolute;
            left: 2px;
            top: 5px;
            width: 7px;
            border-top: 1px solid;
        }

        .objectListInfo .detailsInfo > td {
            word-wrap:break-word;
            word-break:break-all;
            text-align: left;
        }
        .objectListInfo .rowInfo:hover{
            cursor: pointer;
        }

        .label-title {
            font-size: 18px !important;
        }

        .blue {
            background: #0597FF;
            border-color: #ecebeb;
        }
    </style>
    <script>

        $(function() {

        })

        function push() {

            var bindInfo = [];

            $.each($("tr[name='roleInfo']"), function (index, obj) {
                // 估损id
                let objectId = $(this).attr("code");
                // 分配人员id
                let auditerId = $.trim($(this).find("select[name='auditerId']").children("option:selected").val());

                // if (auditerId) {
                    bindInfo.push({
                        "objectId": objectId,
                        "auditerId": auditerId
                    })
                // }

            });

            // if (bindInfo.length  != $("tr[name='roleInfo']").size() ) {
            //     layer.msg("请选择完成所有分配人员", {icon: 2, time: 1500});
            //     return;
            // }

            // // 审核人员id
            // var auditerId = $("#auditerId").val()
            // // 估损表id
            // var objectId = $("input[name='objectId']:checked").val();
            // console.log(auditerId);
            // console.log(objectId);
            // if (!auditerId) {
            //     layer.msg("请选择分配人员", {icon: 2, time: 1500});
            //     return;
            // }
            // if (!objectId) {
            //     layer.msg("请选择估损信息", {icon: 2, time: 1500});
            //     return;
            // }
            layer.confirm("请再次确认是否分配完成？", {icon: 3}, function (index) {
                    var formData = new FormData();
                    formData.append("bindInfo", JSON.stringify(bindInfo));
                    $.ajax({
                        url: "${ctx}/claimCaseDistributionController/bindAuditer",
                        type: 'POST',
                        data: formData,
                        async: true,
                        cache: false,
                        contentType: false,
                        processData: false,
                        success: function (data) {
                            var result = eval("(" + data + ")");
                            if (result.ret == "0") {
                                layer.msg(result.msg, {icon: 1, time: 2000}, function(index) {
                                    parent.window.location.reload();
                                    layer.close(index);
                                });
                            } else {
                                layer.msg(result.msg, {
                                    icon: 2,
                                    time: 1500
                                });
                            }
                        },
                        error: function (data) {
                            var result = eval("(" + data + ")");
                            alert(result.msg);
                        }
                    });
                }
            );
        }
    </script>
</head>
<body>
    <div>

        <div class="row">
            <div class="col-sm-12" style="margin-bottom: 20px;padding-left: 0px;padding-right: 0px;">
                <div class="col-sm-4" style="padding: 0px;">
                    <div class="col-sm-12">
                        案件号：${claimCase.claimCaseNo}
                    </div>
                </div>

                <div class="col-sm-4" style="padding: 0px;">
                    <div class="col-sm-12" style="padding: 0px;">
                        当前状态：${claimCaseStatusEumMap.get(claimCase.status).msg}
                    </div>
                </div>

            </div>


            <div class="col-sm-12" style="margin-bottom: 20px;padding-left: 0px;padding-right: 0px;">
                <div class="col-sm-12" style="margin-bottom: 10px;">
                    人伤处理岗(任务数)：
                </div>
                <#if rsAuditerMap?? && (rsAuditerMap.keySet()?size>0)>
                    <#list rsAuditerMap.keySet() as key>
                        <div class="col-sm-3 clear-padding" style="margin-bottom: 10px;">
                            ${rsAuditerMap.get(key)}
                        </div>
                    </#list>
                </#if>

            </div>

            <div class="col-sm-12" style="margin-bottom: 20px;padding-left: 0px;padding-right: 0px;">
                <div class="col-sm-12" style="margin-bottom: 10px;">
                    物损处理岗(任务数)：
                </div>
                <#if wsAuditerMap?? && (wsAuditerMap.keySet()?size>0)>
                    <#list wsAuditerMap.keySet() as key>
                        <div class="col-sm-3 clear-padding" style="margin-bottom: 10px;">
                            ${wsAuditerMap.get(key)}
                        </div>
                    </#list>
                </#if>
            </div>

        </div>



        <div class="row objectListInfo">
            <div class="block-head-label col-sm-12" style="margin-bottom: 20px;">
                <span class="label-title">赔付列表</span>
            </div>
            <div class="col-sm-12">
                <table class="table">
                    <thead>
                    <tr>

                        <th width="20%">类型</th>
                        <th width="20%">赔付对象名称</th>
                        <th width="20%">手机号</th>
                        <th width="15%">预估金额</th>
                        <th width="20%">操作</th>
                    </tr>
                    </thead>
                    <tbody>
                    <#if claimCaseObjectList??>
                        <#list claimCaseObjectList as claimCaseObject>
                            <tr class="rowInfo" name="roleInfo" code="${claimCaseObject.id}">

                                <td width="20%">${applyTypeNewEnumMap.get(claimCaseObject.type + '-'+ claimCaseObject.category).msg}
                                    <#if claimCaseObject.insCode == "GY" >
                                        (国元)
                                    <#else>
                                        (大地)
                                    </#if>
                                </td>
                                <td width="20%">${claimCaseObject.name}</td>
                                <td width="20%">${claimCaseObject.mobile}</td>
                                <td width="15%">${claimCaseObject.estimatedApprovedMoney}</td>
                                <td width="20%">
                                  <#if claimCaseObject.category == 1 >

                                      <select class="form-control select2" name="auditerId" <#if claimCaseObject.status == "BAX99">disabled</#if> >
                                          <option value="">-请选择分配人员-</option>
                                          <#if rsAuditerMap?? && (rsAuditerMap.keySet()?size>0) >
                                              <#list rsAuditerMap.keySet() as key>
                                                  <option value="${key}" <#if claimCaseObject.auditer == key>selected</#if>>${rsAuditerMap.get(key)}</option>
                                              </#list>
                                          </#if>
                                          <#if rsDeleteAuditerMap.containsKey(claimCaseObject.auditer)>
                                              <option value="${claimCaseObject.auditer}" disabled selected style="background-color: grey">${rsDeleteAuditerMap.get(claimCaseObject.auditer)}</option>
                                          </#if>

                                          <#--<#if deleteAuditerMap?? && (deleteAuditerMap.keySet()?size>0) >
                                              <#list deleteAuditerMap.keySet() as key>
                                                  <option disabled style="background-color: grey" value="${key}" <#if claimCaseObject.auditer == key>selected</#if>>${deleteAuditerMap.get(key)}</option>
                                              </#list>
                                          </#if>-->
                                      </select>

                                  <#else>
                                          <select class="form-control select2" name="auditerId" <#if claimCaseObject.status == "BAX99">disabled</#if>>
                                              <option value="">-请选择分配人员-</option>
                                              <#if wsAuditerMap?? && (wsAuditerMap.keySet()?size>0) >
                                                  <#list wsAuditerMap.keySet() as key>
                                                      <option value="${key}" <#if claimCaseObject.auditer == key>selected</#if>>${wsAuditerMap.get(key)}</option>
                                                  </#list>
                                              </#if>

                                              <#if wsDeleteAuditerMap.containsKey(claimCaseObject.auditer)>
                                                  <option value="${claimCaseObject.auditer}" disabled selected style="background-color: grey">${wsDeleteAuditerMap.get(claimCaseObject.auditer)}</option>
                                              </#if>

                                             <#-- <#if deleteAuditerMap?? && (deleteAuditerMap.keySet()?size>0) >
                                                  <#list deleteAuditerMap.keySet() as key>
                                                      <option disabled style="background-color: grey" value="${key}" <#if claimCaseObject.auditer == key>selected</#if>>${deleteAuditerMap.get(key)}</option>
                                                  </#list>
                                              </#if>-->
                                          </select>
                                  </#if>
                                </td>
                            </tr>

                        </#list>
                    </#if>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-sm-12 btn-div" >
            <button class="btn btn-primary" onclick="push()">分配</button>
            <button class="btn btn-warning" onclick="parent.layer.closeAll();">取消</button>
        </div>
    </div>
</body>
</html>