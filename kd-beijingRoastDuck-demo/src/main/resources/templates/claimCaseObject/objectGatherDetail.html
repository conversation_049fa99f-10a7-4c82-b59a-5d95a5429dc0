<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!-- 上述3个meta标签*必须*放在最前面，任何其他内容都*必须*跟随其后！ -->
    <meta name="description" content="">
    <meta name="author" content="">
    <title>人伤估损清单详情</title>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <script src="${ctx}/carousel/dataCollection/viewer.js"></script>
    <script src="${ctx}/carousel/dataCollection/viewermain.js"></script>
    <link href="${ctx}/carousel/dataCollection/viewer.css" rel="stylesheet">
    <link href="${ctx}/plugins/select2/css/applyDuty/select2.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/plugins/select2/css/applyDuty/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/plugins/select2/css/applyDuty/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/jquery-multi-select/css/multi-select.css" rel="stylesheet"
          type="text/css"/>
    <script src="${ctx}/plugins/select2/js/select2.js" type="text/javascript"></script>
    <link href="${ctx}/metronic/global/plugins/jquery-multi-select/css/multi-select.css" rel="stylesheet"
          type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/jstree/dist/themes/default/style.min.css" rel="stylesheet"
          type="text/css"/>
    <script src="${ctx}/metronic/global/scripts/app.min.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/layouts/layout4/scripts/layout.min.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/layouts/layout4/scripts/demo.min.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/plugins/jquery-multi-select/js/jquery.multi-select.js"
            type="text/javascript"></script>

    <script src="${ctx}/js/genInjury.js" type="text/javascript"></script>
    <script src="${ctx}/js/idCardCheck.js" type="text/javascript"></script>
    <style>

        .clear-margin {
            margin: 0px;
        }

        .clear-padding {
            padding: 0px;
        }

        .clear-padding-left {
            padding-left: 0px;
        }

        .chart-search {
            margin-bottom: 10px;
            display: none;
        }


        .search-channel-content {
            height: 100px;
            overflow-y: auto;
            overflow-x: hidden;
        }

        .search-ul-list {
            list-style-type: none;
            height: 20px;
            padding-left: 0px;
            color: black;
        }

        .channel {
            float: left;
            display: block;
        }

        .typeClick {
            height: 112px;
            margin-bottom: 10px;
            border: 2px solid black;
            border-radius: 5px !important;
            margin-left: 20px;
            cursor: pointer
        }

        .typeClickClick {
            height: 112px;
            margin-bottom: 10px;
            border: 2px solid #1676ff;
            background-color: #1676ff;
            color: white;
            border-radius: 5px !important;
            margin-left: 20px;
            cursor: pointer
        }

        .has-error {
            border-color: #ce0d0d !important;
            color: red !important;
        }

        .bckColor {
            background-color: rgba(51, 153, 241, 0.3);
        }

        .imageListChoose {
            /*background-color: #bad6ff !important;*/
            border: 5px solid #055ad7;
        }

        .collectionCompleted {
            background-color: #E8F2FF;
        }

        .collectionButton {
            /*border-color: #1ABB00;*/
            border: 1px solid #1ABB00;
            background-color: transparent;
            border-radius: 6px !important;
            color: #1ABB00;
        }

        .collectionButtonClick {
            /*border-color: #1ABB00;*/
            border: 1px solid #1ABB00;
            background-color: #1ABB00 !important;
            border-radius: 6px !important;
            color: #FFFFFF;
        }

        .collectionData:hover {
            /*border-color: #1ABB00;*/
            border: 1px solid #1ABB00;
            background-color: #1ABB00;
            color: #FFFFFF;
        }

        .middleTable {
            border: 4px solid #1767ff !important;
        }

        .hangup {
            /*margin-left: 5px;*/
            color: #fff !important;
            background-color: #ce0d0d !important;
            border-radius: 5px !important;
            height: 40px;
            width: 100%;
        }

        .error {
            color: #ce0d0d !important;
        }

        .txt-over-hide {
            color: red;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            display: block;
            width: 650px;
        }

        .jump {
            /*float: right;*/
            border-radius: 5px !important;
            color: #3399ff !important;
            background-color: #fff !important;
            border-color: #3399ff;
            height: 40px;
            width: 100%;
        }

        .genTask {
            /*margin-left: 5px;*/
            color: #fff !important;
            background-color: #3399ff !important;
            border-radius: 5px !important;
            height: 40px;
            width: 100%;
        }

        .thClass {
            text-align: center;
            color: #fff;
        }

        .typeSelect {
            border-color: #3399ff;
            background-color: #3399ff;
        }

        .thumbnail-img {
            width: 50px;
            height: 45px;
            border: 4px solid #D6D6C1;
        }

        .selected-thumbnail-img {
            border: 4px solid #3399ff !important;
        }

        .collection-thumbnail-img {
            border: 4px solid #00B176;
        }

        /*里面的代码可以根据自己需求去进行更改*/
        /* 设置滚动条的样式 */
        ::-webkit-scrollbar {
            width: 5px;
        }

        /* 滚动槽 */
        ::-webkit-scrollbar-track {
            -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.3);
            border-radius: 10px;
        }

        /* 滚动条滑块 */
        ::-webkit-scrollbar-thumb {
            border-radius: 10px;
            background: rgba(0, 0, 0, 0.1);
            -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.5);
        }

        ::-webkit-scrollbar-thumb:window-inactive {
            background: rgba(255, 0, 0, 0.4);
        }

        .ticket-information {
            height: 1050px;
            overflow-y: scroll;
        }

        .dataDisplayArea {
            width: 100%;
            height: 19%;
            border-radius: 10px !important;
            /*border: 1px solid #aca8a8;*/
            border: 1px solid #797979;
            cursor: pointer;
            margin-top: 10px;
            overflow: hidden;
        }

        .selected-dataDisplayArea {
            background-color: #91cae9;
        }

        .submit-dataDisplayArea {
            border: 3px solid #44db69 !important;
        }

        .change-dataDisplayArea {
            border: 3px solid red !important;
        }

        .dataDisplayArea > div {
            height: 20%;
            padding: 10px 0px 0px 0px !important;
        }

        .dataDisplayArea-head {
            background-color: #3399ff;
            /*border-radius: 10px 10px 0px 0px !important;*/
        }

        .dataDisplayArea-head-left {
            color: #ffffff;
            font-size: 15px;
            padding: 0px 0px 0px 5px;
        }

        .dataDisplayArea-head-right {
            text-align: right;
            padding: 0px !important;
        }

        .dataDisplayArea-head-img {
            width: 30px;
            height: 18px;
        }

        .dataDisplayArea-body-left {
            opacity: 0.7;
        }

        .dataDisplayArea-body-right {
            text-align: right;
        }

        .row {
            margin: 0px 0px;
        }

        .subject-name {
            font-size: 20px;
        }

        .estimateInventoryDataBlue {
            background-color: #5199ff;
            border-color: #fff;
        }

        button[code] {
            margin-top: 7px;
            margin-right: 7px;
        }

        .form-control {
            height: 28px !important;
        }

        .label-hide-overflow {
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }

        textarea[class='layui-layer-input'] {
            outline: 0 !important;
            width: 700px !important;
            height: 350px !important;
            padding-left: 10px !important;
            border: 1px solid #e6e6e6 !important;
            color: #333 !important;
            box-shadow: none !important;
        }

        button[name='actionBtn'] {
            color: white;
        }

        select {
            padding: 0px 12px !important;
        }

        .label-right {
            text-align: right;
        }

        .align-item-center {
            display: flex;
            align-items: center;

        }
    </style>

    <script type="text/javascript">

        var viewer = null;

        const proGenInjuryData = new GenInjury().getValue();

        $(function () {

            /*图片展示工具*/
            (function () {
                viewer = new Viewer({
                    activeId: null
                });
            })();

            $("#injuredDesc").select2({
                placeholder: "请选择 / 请输入",
                width: null,
                tags: true,
                allowClear: true
            });
            var injuredDesc = "${claimCaseObject.injuredDesc}";
            var injuredDescBool = false;
            //初始化 伤情
            proGenInjuryData.forEach(function (item, index) {
                var injuredDescOption = document.createElement("option");
                injuredDescOption.innerText = item['text'];
                injuredDescOption.value = item['id'];
                if (item['id'] == injuredDesc) {
                    injuredDescOption.selected = true;
                    injuredDescBool = true;
                }
                $("#injuredDesc").append(injuredDescOption);
            });
            if (injuredDesc && !injuredDescBool) {
                let option = document.createElement("option");
                option.innerText = injuredDesc;
                option.value = injuredDesc;
                option.selected = true;
                $("#injuredDesc").append(option);
                $("#injuredDesc").select2("val", injuredDesc);
            }


            $("div[name='dataCollectArea']").on('blur', "input[name='treatIdNum']", function() {
                let val = $(this).val();
                if (val && checkService.isIdCard(val)) {
                    $("#age").val(checkService.idCardToAge(val));
                    $("#gender").val(checkService.idCardToGender(val));
                } else {
                    $("#age").val("");
                    $("#gender").val("");
                }
            });

            // 初始化年龄 性别
            let treatIdNum = "${claimCaseObject.treatIdNum}";
            let age = "${claimCaseObject.age}";
            let gender = "${claimCaseObject.gender}";
            if (treatIdNum && checkService.isIdCard(treatIdNum)) {
                if (!age) {
                    $("#age").val(checkService.idCardToAge(treatIdNum));
                }
                if (!gender) {
                    $("#gender").val(checkService.idCardToGender(treatIdNum));
                }
            }

            /*获取焦点移除报错样式*/
            $("body").on("focus", "input,textarea,select,.ggLayer", function () {
                document.onkeydown = function (w) {
                }
            });

            /*点击缩略图*/
            $('body').on('click', '.thumbnail-img', function () {
                if ($(this).hasClass('selected-thumbnail-img')) {
                    return;
                }
                let attrId = $(this).data('fileid');
                $('.selected-thumbnail-img').removeClass('selected-thumbnail-img');
                $(this).addClass('selected-thumbnail-img');
                viewer.showImageByFileId(attrId);
                let imgNumber = $(this).data('img-number');
                let offset = imgNumber * 95 - (1050 - 95) / 2;
                $('#thumbnail').scrollTop(offset);
            });

            /*A/D切换影像*/
            $('body').on('click', '.viewer-list > li', function () {
                let attchId = $(this).find('img').attr('alt');
                $('#thumbnail-' + attchId).trigger('click');
            });

            $("#dataCollectArea").on('change', "input[name='billCollection']", check);
            $("div[name='dataCollectArea']").on('change', "input[validName='billCollection']", check);

            $("#dataCollectArea").on('blur', "input[id='lossAssessment'],input[id='approvedAmount']", function () {
                changeLossAssessmentForPageData();
            });

            var createInput = function (name, value) {
                var inputElement = document.createElement("input");
                inputElement.type = "hidden";
                inputElement.name = name;
                if (value != null) {
                    inputElement.value = value;
                }
                return inputElement;
            }

            var postForm = function (url) {
                // 创建表单
                var formElement = document.createElement("form");
                formElement.action = url;
                formElement.method = "post";
                // 打开新标签
                formElement.target = '_blank';
                formElement.style.display = "none";
                var code = $.trim($("#objectName").val());
                var type = 1;//人伤为1
                if (code != "") {
                    formElement.appendChild(createInput("code", code));
                }
                formElement.appendChild(createInput("type", 1));
                formElement.appendChild(createInput("assessmentFather.verifyAmout", $.trim($("#verifyAmout").val())));
                formElement.appendChild(createInput("assessmentFather.verifyDetail", $.trim($("#verifyDetail").val())));
                formElement.appendChild(createInput("assessmentFather.dutyRate", $.trim($("#dutyRate").val())));
                formElement.appendChild(createInput("assessmentFather.deductFee", $.trim($("#deductFee").val())));

                var index = 0 ;
                $("#dataCollectArea>div[dataareasubjectcode]").each(function(idx,obj){
                    var code =$(obj).attr("dataareasubjectcode");
                    var title= $(obj).attr("dataareasujectname");
                    var appeal= $.trim($(obj).find("#appeal").val());
                    var lossAssessment= $.trim($(obj).find("#lossAssessment").val());
                    var trafficAccidentsInsurance=$.trim($(obj).find("#trafficAccidentsInsurance").val());
                    var commercialInsurance=$.trim($(obj).find("#commercialInsurance").val());
                    var remark= $.trim($(obj).find("#remark").val());
                    var approvedAmount= $.trim($(obj).find("#approvedAmount").val());
                    formElement.appendChild(createInput("assessmentFather.assessmentReport["+index+"].code",code));
                    formElement.appendChild(createInput("assessmentFather.assessmentReport["+index+"].title",title));
                    formElement.appendChild(createInput("assessmentFather.assessmentReport["+index+"].appeal",appeal));
                    formElement.appendChild(createInput("assessmentFather.assessmentReport["+index+"].lossAssessment",lossAssessment));
                    formElement.appendChild(createInput("assessmentFather.assessmentReport["+index+"].trafficAccidentsInsurance",trafficAccidentsInsurance));
                    formElement.appendChild(createInput("assessmentFather.assessmentReport["+index+"].commercialInsurance",commercialInsurance));
                    formElement.appendChild(createInput("assessmentFather.assessmentReport["+index+"].remark",remark));
                    formElement.appendChild(createInput("assessmentFather.assessmentReport["+index+"].approvedAmount",approvedAmount));
                    index ++;
                });

                document.body.appendChild(formElement);
                formElement.submit();
                formElement.remove();
                return;
            }

            //导出按钮监控器
            $("#inputForm").on("click", ".exportBtn", function (e) {
                e.stopPropagation();
                e.preventDefault();
                var _this = this;
                $(_this).removeClass("exportBtn");
                //提交之后再把class加回来 防止重复提交
                postForm("${ctx}/downloadCenterController/downloadAssessment");
                $(_this).addClass("exportBtn");

            })

            //初始话数据
            <#if objectAssessmentList?exists &&(objectAssessmentList?size>0)>
            <#list objectAssessmentList as assessment>
                $("button[code='${assessment.code}']").addClass("estimateInventoryDataBlue");
            </#list>
            </#if>

            let status = '${status}';
            if (status == '0' || status == '2') {
                $.each($("input[name='billCollection']"), function (index, obj) {
                    $(this).attr("disabled", "1");
                });
            }
            if(status=='1'){
                $('#verifyAmout').val("");
                $('#verifyDetail').val("");
                $('#dutyRate').val("");
                $('#deductFee').val("");
            }


            var bankSubbranchInitName = "请输入支行名称查询...";
            <#if bankInfo??>
            bankSubbranchInitName = "${bankInfo.bankName}";
            </#if>

            $("#bankSubbranchSelect").select2({
                ajax: {
                    type: 'POST',
                    url: "${ctx}/insuranceCaseController/getBankInfo",
                    dataType: 'json',
                    delay: 500,
                    contentType: "application/x-www-form-urlencoded; charset=utf-8",
                    data: function (params) {
                        return {
                            totalBankId: $('#totalBankSelect').val(),
                            paramMsg: params.term
                        };
                    },
                    processResults: function (data) {
                        return {
                            results: data.bankList,
                        };
                    },
                },
                escapeMarkup: function (markup) {
                    return markup;
                },
                width: '100%',
                placeholder: "请输入支行名称查询...",
                minimumInputLength: 2,
                minimumResultsForSearch: Infinity, // 隐藏搜索框
                templateResult: function (data) {
                    if (data.id != undefined) {
                        return "<div>" + data.bankName+"("+data.provinceName+"-"+data.cityName+")" + "</div>";
                    }
                    return "";
                },
                templateSelection: function (data) {
                    if (data.id != "") {
                        $("#bankInfoId").val(data.id);
                        let bankText = data.bankName+"("+data.provinceName+"-"+data.cityName+")";
                        return "<span title='"+bankText+"'>" + bankText  + "</span>";
                    }
                    return bankSubbranchInitName;
                }
            });


            //查询总行信息
            $("#totalBankSelect").select2({
                ajax: {
                    type: 'POST',
                    url: "${ctx}/insuranceCaseController/getTotalBankInfo",
                    dataType: 'json',
                    delay: 500,
                    contentType: "application/x-www-form-urlencoded; charset=utf-8",
                    data: function (params) {
                        return {
                            paramMsg: params.term
                        };
                    },
                    processResults: function (data) {
                        return {
                            results: data.bankList,
                        };
                    },
                },
                escapeMarkup: function (markup) {
                    return markup;
                },
                width: '100%',
                placeholder: "请输入总行名称查询...",
                minimumInputLength: 2,
                minimumResultsForSearch: Infinity, // 隐藏搜索框
                templateResult: function (data) {
                    if (data.id != undefined) {
                        return "<div>" + data.bankName + "</div>";
                    }
                    return "";
                },
                templateSelection: function (data) {
                    if (data.id != "") {
                        $("#bankName").val(data.bankName);
                        $('#bankSubbranchSelect').prop('disabled', false);
                        $("#bankInfoId").val("");
                        $("#bankSubbranchSelect").select2("val", "");
                        $("#select2-bankSubbranchSelect-container .select2-selection__placeholder").html("");
                        return "<span>" + data.bankName + "</span>";
                    }
                    $("#bankName").val("");
                    $("#bankInfoId").val("");
                    $("#bankSubbranchSelect").select2("val", "");
                    $('#bankSubbranchSelect').prop('disabled', true);
                    return "请输入总行名称查询...";
                }
            });

            // 初始化 银行信息
            $("#select2-totalBankSelect-container .select2-selection__placeholder").html("${claimCaseObject.bankName!''}");
            $("#select2-bankSubbranchSelect-container .select2-selection__placeholder").html("${bankInfo.bankName!''}");
            $("#bankName").val("${claimCaseObject.bankName!''}");
            $("#bankInfoId").val("${claimCaseObject.bankInfoId!''}");

            // 责任变更同步比例
            $("#dataCollectArea").on("change", "#accidentLiability", function() {
                let dutyRate = $(this).find("option:selected").attr("data-dutyRate");
                $("#dutyRate").val(dutyRate);
            });

            // 判断是否查看
            var isShow = "${isShow}";
            if (isShow) {
                $("input").attr("disabled", "1");
                $("select").attr("disabled", "1");
                $("textarea").attr("disabled", "1");
            }

            var layerTop = top.layer;
            var errorMsg = "${errorMsg}";
            if (errorMsg) {
                layerTop.msg(errorMsg, {
                    icon: 2,
                    time: 3000
                });
            }
        });

        // 触发修改某一字段合计金额,更具页面数据修改
        function changeLossAssessmentForPageData() {
            let lossAssessmentSum = new BigDecimal("0");
            let nuclearLossSum = new BigDecimal("0");
            let dataArea = $("div[dataarea='1']");
            $.each(dataArea, function (index, obj) {
                let lossAssessment = $(obj).find("#lossAssessment").val();
                if (lossAssessment != undefined && lossAssessment.trim() != '') {
                    lossAssessment = new BigDecimal(lossAssessment).setScale(2, MathContext.ROUND_HALF_UP);;
                    lossAssessmentSum = lossAssessmentSum.add(lossAssessment).setScale(2, MathContext.ROUND_HALF_UP);
                }

                let approvedAmount = $(obj).find("#approvedAmount").val();
                if (approvedAmount != undefined && approvedAmount.trim() != '') {
                    approvedAmount = new BigDecimal(approvedAmount).setScale(2, MathContext.ROUND_HALF_UP);;
                    nuclearLossSum = nuclearLossSum.add(approvedAmount).setScale(2, MathContext.ROUND_HALF_UP);
                }
            });
            changeLossAssessment(lossAssessmentSum, nuclearLossSum);
        }

        // 触发修改某一字段合计金额
        function changeLossAssessment(lossAssessmentSum, nuclearLossSum) {
            $("#lossAssessmentSum").html("估损合计：" + lossAssessmentSum + " 元");
            $("#lossAssessmentSum").attr("sumMoney", lossAssessmentSum);

            $("#nuclearLossSum").html("核损合计：" + nuclearLossSum + " 元");
            $("#nuclearLossSum").attr("sumMoney", nuclearLossSum);
        }


        function seePlanName(planId) {
            if (typeof planId == 'undefined' || planId == '') {
                layer.msg('暂未数据！！！', {
                    icon: 2,
                    time: 2000
                });
                return;
            }

            var formData = new FormData();
            formData.append("planId", planId);
            $.ajax({
                url: "${ctx}/insuranceCaseController/seePlanNameByPlanId",
                type: 'POST',
                data: formData,
                async: true,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    var result = eval("(" + data + ")");
                    if (result.ret == "0") {
                        window.open(result.msg, "_blank");
                    } else {
                        layer.msg(result.msg, {
                            icon: 2,
                            time: 2000
                        });
                    }
                },
                error: function (data) {
                    var result = eval("(" + data + ")");
                    alert(result.msg);
                }
            });

        }

        function seeCaseProcessReason(claimCaseId) {
            var openWindowWidth = $(document).width() * 0.8 + "px";
            var offsetH = ($(window).height() / 5 - 20 > 120 ? 120 : $(window).height() / 5 - 20) + "px";
            layer.open({
                type: 2,
                title: '查看案件流转',
                area: openWindowWidth,
                offset: offsetH,
                fix: false, //不固定
                maxmin: true,
                content: "${ctx}/insuranceCaseController/getBSCaseAssessmentProcessReason?claimCaseId=" + claimCaseId,
                success: function (layero, index) {
                    layer.iframeAuto(index);
                }
            });
        }

        function getHistroyCaseInfo(baseUserId) {
            if (baseUserId == '') {
                layer.msg("暂无信息！！！", {icon: 2, time: 3000});
                return;
            }
            var openWindowWidth = $(document).width() * 0.8 + "px";
            var offsetH = ($(window).height() / 5 - 20 > 120 ? 120 : $(window).height() / 5 - 20) + "px";
            layer.open({
                type: 2,
                title: '查看历史案件',
                area: openWindowWidth,
                offset: offsetH,
                fix: false, //不固定
                maxmin: true,
                content: "${ctx}/insuranceCaseController/getHistroyCaseInfo?baseUserId=" + baseUserId,
                success: function (layero, index) {
                    layer.iframeAuto(index);
                }
            });
        }


        // 添加项目
        function buttonClick(obj) {
            let subjectCode = $(obj).attr("code");
            let subjectName = $(obj).attr("name");
            let classStr = $(obj).attr("class");
            if (classStr != undefined && classStr.indexOf("estimateInventoryDataBlue") > -1) {
                deleteEstimateInventoryData(subjectCode);
            } else {
                let data = {};
                data["id"] = "";
                addEstimateInventoryData(subjectCode, subjectName, data);
            }
        }

        //生成uuid
        function guid() {
            return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
                var r = Math.random() * 16 | 0,
                    v = c == 'x' ? r : (r & 0x3 | 0x8);
                return v.toString(16);
            });
        }

        function addEstimateInventoryData(subjectCode, subjectName, basicData) {
            let status = `${status}`;
            let head = $("<div class='row' dataarea='1' dataareasubjectcode='" + subjectCode + "' dataareasujectname='" + subjectName + "'></div>");
            head.append('<div class="col-sm-8" style="margin: 15px;margin-left: 0px;">' +
                '                                <div class="subject-name">' + subjectName + '</div>' +
                '                            </div>');
            let collectField = getCollectField(status, subjectCode);
            let containerData = $('<div class="col-sm-12 clear-padding"></div>');
            for (let field in collectField) {
                let attr = collectField[field];
                let labelName = attr["labelName"];
                let row = attr["row"];
                let labelRow = attr["labelRow"];
                let dataRow = attr["dataRow"];
                let isChange = attr["isChange"];
                let blurValid = attr["blurValid"];
                let dataInput = basicData[field];
                if (dataInput == undefined) {
                    dataInput = '';
                }

                //是否隐藏
                let isHidden = attr["isHidden"];
                if(isHidden !=undefined && isHidden !=null && isHidden=="1"){
                    containerData.append(
                        '<input name="billCollection" id="' + field + '" type="hidden" ' +
                        'value="' + dataInput + '"/>' );
                    continue;
                }


                let dataHead = $('<div class="col-sm-' + row + ' clear-padding" style="font-size: 15px;margin: 9px 0px;"></div>');
                if (isChange == "1") {
                    dataHead.append('<div class="col-sm-' + labelRow + ' clear-padding" style="text-align: right">' + labelName + '</div>');
                    dataHead.append('<div class="col-sm-' + dataRow + ' " >\n' +
                        '<input name="billCollection" id="' + field + '" type="text" class="form-control" ' +
                        'value="' + dataInput + '" data-valid="' + blurValid + '"/>' +
                        '                                    </div>');
                } else {
                    dataHead.append('<div class="col-sm-' + labelRow + ' clear-padding" style="text-align: right">' + labelName + '</div>');
                    dataHead.append('<div class="col-sm-' + dataRow + ' " >\n' +
                        '<input disabled name="billCollection" id="' + field + '" type="text" class="form-control" ' +
                        'value="' + dataInput + '"/>' +
                        '                                    </div>');
                }
                containerData.append(dataHead);
            }
            head.append(containerData);
            let result = $("<div></div>");
            result.append(head);
            $('#dataCollectArea').prepend(result.html());
            $("button[code='" + subjectCode + "']").addClass("estimateInventoryDataBlue");

            //计算定损金额合计
            changeLossAssessmentForPageData();
        }

        function deleteEstimateInventoryData(subjectCode) {
            $("button[code='" + subjectCode + "']").removeClass("estimateInventoryDataBlue");
            $("div[dataAreaSubjectCode='" + subjectCode + "']").remove();

            //计算定损金额合计
            changeLossAssessmentForPageData();
        }


        //根据估损清单表状态和code决定显示
        function getCollectField(status, subjectCode) {
            switch (status + "|" + subjectCode) {
                // 创建
                case "|1-1":
                case "|1-2":
                case "|1-3":
                case "|1-4":
                case "|1-5":
                case "|1-6":
                case "|1-7":
                case "|1-8":
                case "|1-9":
                case "|1-10":
                case "|1-11":
                case "|1-12":
                case "|1-13":
                case "|1-14":
                case "|1-15":
                case "|1-16":
                case "|1-17":
                case "|1-18":
                case "|1-19":
                    return {
                        "id":{"isHidden":"1"},
                        "appeal": {"labelName": "理赔标准", "row": "6", "labelRow": "4", "dataRow": "6", "isChange": "1", "blurValid": "isNumberAndDecimalPoint isEmpty"},
                        "lossAssessment": {"labelName": "定损金额", "row": "6", "labelRow": "4", "dataRow": "6", "isChange": "1", "blurValid": "isNumberAndDecimalPoint isEmpty"},
                        /*"trafficAccidentsInsurance": {"labelName": "交强险赔付", "row": "6", "labelRow": "6", "dataRow": "6", "isChange": "1", "blurValid": "isNumberAndDecimalPoint"},
                        "commercialInsurance": {"labelName": "商业险赔付", "row": "6", "labelRow": "6", "dataRow": "6", "isChange": "1", "blurValid": "isNumberAndDecimalPoint"},*/
                        "remark": {"labelName": "备注", "row": "12", "labelRow": "2", "dataRow": "9", "isChange": "1", "blurValid": "none"},
                        "approvedAmount": {"labelName": "核损金额", "row": "12", "labelRow": "2", "dataRow": "9", "isChange": "0"}
                    };
                // 暂存
                case "-1|1-1":
                case "-1|1-2":
                case "-1|1-3":
                case "-1|1-4":
                case "-1|1-5":
                case "-1|1-6":
                case "-1|1-7":
                case "-1|1-8":
                case "-1|1-9":
                case "-1|1-10":
                case "-1|1-11":
                case "-1|1-12":
                case "-1|1-13":
                case "-1|1-14":
                case "-1|1-15":
                case "-1|1-16":
                case "-1|1-17":
                case "-1|1-18":
                case "-1|1-19":
                    return {
                        "id":{"isHidden":"1"},
                        "appeal": {"labelName": "理赔标准", "row": "6", "labelRow": "6", "dataRow": "6", "isChange": "1", "blurValid": "isNumberAndDecimalPoint"},
                        "lossAssessment": {"labelName": "定损金额", "row": "6", "labelRow": "6", "dataRow": "6", "isChange": "1", "blurValid": "isNumberAndDecimalPoint"},
                        /*"trafficAccidentsInsurance": {"labelName": "交强险赔付", "row": "6", "labelRow": "6", "dataRow": "6", "isChange": "1", "blurValid": "isNumberAndDecimalPoint"},
                        "commercialInsurance": {"labelName": "商业险赔付", "row": "6", "labelRow": "6", "dataRow": "6", "isChange": "1", "blurValid": "isNumberAndDecimalPoint"},*/
                        "remark": {"labelName": "备注", "row": "12", "labelRow": "3", "dataRow": "9", "isChange": "1", "blurValid": "none"}
                    };

                // 生成待审核
                case "0|1-1":
                case "0|1-2":
                case "0|1-3":
                case "0|1-4":
                case "0|1-5":
                case "0|1-6":
                case "0|1-7":
                case "0|1-8":
                case "0|1-9":
                case "0|1-10":
                case "0|1-11":
                case "0|1-12":
                case "0|1-13":
                case "0|1-14":
                case "0|1-15":
                case "0|1-16":
                case "0|1-17":
                case "0|1-18":
                case "0|1-19":
                    return {
                        "id":{"isHidden":"1"},
                        "appeal": {"labelName": "理赔标准", "row": "6", "labelRow": "6", "dataRow": "6", "isChange": "0"},
                        "lossAssessment": {"labelName": "定损金额", "row": "6", "labelRow": "6", "dataRow": "6", "isChange": "0"},
                        /*"trafficAccidentsInsurance": {"labelName": "交强险赔付", "row": "6", "labelRow": "6", "dataRow": "6", "isChange": "0"},
                        "commercialInsurance": {"labelName": "商业险赔付", "row": "6", "labelRow": "6", "dataRow": "6", "isChange": "0"},*/
                        "remark": {"labelName": "备注", "row": "12", "labelRow": "3", "dataRow": "9", "isChange": "0"},
                    };


                // 驳回
                case "1|1-1":
                case "1|1-2":
                case "1|1-3":
                case "1|1-4":
                case "1|1-5":
                case "1|1-6":
                case "1|1-7":
                case "1|1-8":
                case "1|1-9":
                case "1|1-10":
                case "1|1-11":
                case "1|1-12":
                case "1|1-13":
                case "1|1-14":
                case "1|1-15":
                case "1|1-16":
                case "1|1-17":
                case "1|1-18":
                case "1|1-19":
                    return {
                        "id":{"isHidden":"1"},
                        "appeal": {"labelName": "理赔标准", "row": "6", "labelRow": "6", "dataRow": "6", "isChange": "1", "blurValid": "isNumberAndDecimalPoint"},
                        "lossAssessment": {"labelName": "定损金额", "row": "6", "labelRow": "6", "dataRow": "6", "isChange": "1", "blurValid": "isNumberAndDecimalPoint"},
                        /*"trafficAccidentsInsurance": {"labelName": "交强险赔付", "row": "6", "labelRow": "6", "dataRow": "6", "isChange": "1", "blurValid": "isNumberAndDecimalPoint"},
                        "commercialInsurance": {"labelName": "商业险赔付", "row": "6", "labelRow": "6", "dataRow": "6", "isChange": "1", "blurValid": "isNumberAndDecimalPoint"},*/
                        "remark": {"labelName": "备注", "row": "12", "labelRow": "3", "dataRow": "9", "isChange": "1", "blurValid": "none"},
                        "approvedAmount": {"labelName": "核损金额", "row": "12", "labelRow": "3", "dataRow": "9", "isChange": "0"}
                    };
                // 通过
                case "2|1-1":
                case "2|1-2":
                case "2|1-3":
                case "2|1-4":
                case "2|1-5":
                case "2|1-6":
                case "2|1-7":
                case "2|1-8":
                case "2|1-9":
                case "2|1-10":
                case "2|1-11":
                case "2|1-12":
                case "2|1-13":
                case "2|1-14":
                case "2|1-15":
                case "2|1-16":
                case "2|1-17":
                case "2|1-18":
                case "2|1-19":
                    return {
                        "id":{"isHidden":"1"},
                        "appeal": {"labelName": "理赔标准", "row": "6", "labelRow": "6", "dataRow": "6", "isChange": "0"},
                        "lossAssessment": {"labelName": "定损金额", "row": "6", "labelRow": "6", "dataRow": "6", "isChange": "0"},
                        /*"trafficAccidentsInsurance": {"labelName": "交强险赔付", "row": "6", "labelRow": "6", "dataRow": "6", "isChange": "0"},
                        "commercialInsurance": {"labelName": "商业险赔付", "row": "6", "labelRow": "6", "dataRow": "6", "isChange": "0"},*/
                        "remark": {"labelName": "备注", "row": "12", "labelRow": "3", "dataRow": "9", "isChange": "0"},
                        "approvedAmount": {"labelName": "核损金额", "row": "12", "labelRow": "3", "dataRow": "9", "isChange": "0"},
                    };

            }
        }

        let check = function () {
            var _this = this;
            var val = _this.value;
            var valid = _this.getAttribute('data-valid').trim();
            valid = valid.split(" ");
            for (let check of valid) {
                if (!checkService[check](val)) {
                    $(_this).val("");
                    $(_this).focus();
                    if (check == "isEmpty") {
                        layer.tips("值不能为空", $(_this), {time: 1500, shade: [0.0001, '#000']});
                    } else {
                        layer.tips("输入格式不对", $(_this), {time: 1500, shade: [0.0001, '#000']});
                    }
                    return;
                }
            }

            $(this).attr("title", val);
        }

        //校验规则
        const checkService = {
            // 不校验
            none: function () {
                return true;
            },

            //非空校验
            isEmpty: function (str) {
                let result = true;

                if (str == null || str == "") {
                    result = false;
                }
                if (str == "0") {
                    result = true;
                }

                return result;
            },

            // 只能输入数字[0-9]
            isDigits: function (str) {
                if (str == null || str == "") return true;
                var reg = /^\d+$/;
                return reg.test(str);
            },
            //百分比0-100
            isRate: function(str){
                if (str == null || str == "") return true;
                var reg = /^\d+$/;
                let result = false;
                if(reg.test(str) && (str>=0 && str <=100)){
                    result = true;
                }
                return result;
            },

            //身份证校验
            isIdCard: function (str) {
                const idCardCheck = new IdCardCheck();
                if (idCardCheck.checkIdCard(str)) {
                    return false;
                }
                return true;
            },

            //更具身份证获取性别
            idCardToGender: function (str) {
                var sex = "";
                if (parseInt(str.substr(16, 1)) % 2 == 1) {
                    sex = "男";
                } else {
                    sex = "女";
                }
                return sex;
            },

            //更具身份证获取年龄
            idCardToAge: function (str) {
                var myDate = new Date();
                var month = myDate.getMonth() + 1;
                var day = myDate.getDate();
                var age = myDate.getFullYear() - str.substring(6, 10) - 1;
                if (str.substring(10, 12) < month || str.substring(10, 12) == month && str.substring(12, 14) <= day) {
                    age++;
                }
                return age;
            },

            // 匹配english
            isEnglish: function (str) {
                if (str == null || str == "") return true;
                var reg = /^[A-Za-z]+$/;
                return reg.test(str);
            },

            // 匹配integer(包含正负)
            isInteger: function (str) {
                if (str == null || str == "") return true;
                var reg = /^[-\+]?\d+$/;
                return reg.test(str);
            },

            // 匹配汉字
            isChinese: function (str) {
                if (str == null || str == "") return true;
                var reg = /^[\u4e00-\u9fa5]+$/;
                return reg.test(str);
            },

            // 匹配中文(双字节字符,包括汉字和符号)
            isChineseChar: function (str) {
                if (str == null || str == "") return true;
                var reg = /^[\u0391-\uFFE5]+$/;
                return reg.test(str);
            },

            //匹配中英文
            isChineseAndEnglish: function (str) {
                if (str == null || str == "") return true;
                var reg = /^[\u4e00-\u9fa5a-zA-Z]+$/;
                return reg.test(str);
            },

            // 匹配URL
            isUrl: function (str) {
                if (str == null || str == "") return true;
                var reg = /^http:\/\/[A-Za-z0-9]+\.[A-Za-z0-9]+[\/=\?%\-&_~`@[\]\’:+!]*([^<>\"])*$/;
                return reg.test(str);
            },

            // 字符验证，只能包含中文、英文、数字、下划线、空格。
            stringCheck: function (str) {
                if (str == null || str == "") return true;
                var reg = /^[a-zA-Z0-9\u4e00-\u9fa5_ ,.，。]+$/;
                return reg.test(str);
            },

            //字符长度校验（最长64位）
            stringLengthCheck: function (str, length) {
                if (str == null || str == "") return true;
                length = length || 64;
                if (str.length > length) return false;
                return true;
            },
            //IP格式验证
            isIP: function (str) {
                if (str == null || str == "") return true;
                var reg = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;
                return reg.test(str);
            },
            //YYYYMMDD格式验证
            isTime: function (str) {
                if (str == null || str == "") return true;
                var reg = /^((([0-9]{3}[1-9]|[0-9]{2}[1-9][0-9]{1}|[0-9]{1}[1-9][0-9]{2}|[1-9][0-9]{3})(((0[13578]|1[02])(0[1-9]|[12][0-9]|3[01]))|((0[469]|11)(0[1-9]|[12][0-9]|30))|(02(0[1-9]|[1][0-9]|2[0-8]))))|((([0-9]{2})(0[48]|[2468][048]|[13579][26])|((0[48]|[2468][048]|[3579][26])00))0229))$/;
                return reg.test(str);
            },
            //只能输入数字和小数点
            isNumberAndDecimalPoint: function (str) {
                if (str == null || str == "") return true;
                var reg = /^\d+(\.\d{1,2})?$/;
                return reg.test(str);
            },
            //只能输入数字和小数点
            isNumberAndDecimalPointNotDefault: function (str) {
                if (str == null || str == "") return true;
                var reg = /^\d+(\.\d{1,2})?$/;
                return reg.test(str);
            },
            //select2--->是否
            isTrueFalse: function (str) {

                return true;
            },
            //伤残等级
            isDisabilityLevel: function () {
                return true;
            },
            //伤残等级
            isDeadSurplusQuota: function () {
                return true;
            }
        };

        // 组装数据
        function assembleData(status) {

            let isError = false;
            let errorMsg = "";

            changeLossAssessmentForPageData(); // 刷新估损总金额

            $("button[name='actionBtn']").attr('disabled','1');

            let objectName = $("#objectName").val().trim();
            if (!objectName) {
                isError = true;
                errorMsg += "估损清单名称不能为空！</br>";
            }

            let treatName = $("#treatName").val().trim();
            if (!treatName) {
                isError = true;
                errorMsg += "伤情姓名不能为空！</br>";
            }

            let gender = $("#gender").val().trim();
            let treatIdNum = $("#treatIdNum").val().trim();
            let age = $("#age").val().trim();
            let injuredArea = $("#injuredArea").val().trim();
            let injuredDesc = $("#injuredDesc").val().trim();

            if (!gender) {
                isError = true;
                errorMsg += "性别不能为空！</br>";
            }
            if (!treatIdNum) {
                isError = true;
                errorMsg += "身份证号不能为空！</br>";
            }
            if (!age) {
                isError = true;
                errorMsg += "年龄不能为空！</br>";
            }
            if (!injuredArea) {
                isError = true;
                errorMsg += "受伤部位不能为空！</br>";
            }
            if (!injuredDesc) {
                isError = true;
                errorMsg += "伤情描述不能为空！</br>";
            }

            let lossAssessmentSum = $("#lossAssessmentSum").attr("sumMoney").trim();
            let nuclearLossSum = $("#nuclearLossSum").attr("sumMoney").trim();

            let dataArea = $("div[dataarea='1']");

            if (dataArea.length == 0) {
                errorMsg += "必须存在一条项目数据！</br>";
                isError = true;
            }

            let objectAssessmentList = [];
            $.each(dataArea, function (index, obj) {
                let rowData = {};
                let subjectCode = $(obj).attr("dataareasubjectcode").trim();
                rowData["code"] = subjectCode;
                let dataareasujectname = $(obj).attr("dataareasujectname").trim();
                rowData["name"] = dataareasujectname;
                let collectField = getCollectField('${status}', subjectCode);
                for (let field in collectField) {
                    let fieldData = $(obj).find("#" + field).val().trim();
                    if (field == "appeal" || field == "lossAssessment") {
                        if (!checkService["isEmpty"](fieldData)) {
                            errorMsg += "理赔标准和定损金额不能为空！</br>";
                            isError = true;
                            return false;
                        }
                    }
                    rowData[field] = fieldData;
                }
                objectAssessmentList.push(rowData);
            });


            let object = {};
            object["id"] = "${claimCaseObject.id}";
            object["claimCaseId"] = "${claimCaseObject.claimCaseId}";
            object["claimCaseNo"] = "${claimCaseObject.claimCaseNo}";
            object["name"] = objectName;
            object["treatName"] = treatName;
            object["gender"] = gender;
            object["treatIdNum"] = treatIdNum;
            object["age"] = age;
            object["injuredArea"] = injuredArea;
            object["injuredDesc"] = injuredDesc;
            object["type"] = "${claimCaseObject.type}";
            object["category"] = "${claimCaseObject.category}";
            object["lossAssessmentSum"] = lossAssessmentSum;
            object["nuclearLossSum"] = nuclearLossSum;
            object["remark"] = $("#objectRemark").val().trim();
            object["claimCaseObjectAssessmentList"] = objectAssessmentList;
            object["status"] = status;

            if (status.startsWith("BAX2") && status != "BAX21" && status != "BAX22") {
                for (let assessment of objectAssessmentList) {
                    if (!checkService["isEmpty"](assessment.approvedAmount)) {
                        errorMsg += "各项核损金额不能为空！</br>";
                        isError = true;
                        break;
                    }
                }
            }

            if (status.startsWith("BAX3")) {
                let accidentLiability = $("#accidentLiability").val();
                let dutyRate = $("#dutyRate").val();
                let deductFee = $("#deductFee").val();
                let verifyAmout = $("#verifyAmout").val();
                let bankAccount = $("#bankAccount").val();
                let bankCard = $("#bankCard").val();
                let bankName = $("#bankName").val();
                let bankInfoId = $("#bankInfoId").val();
                object["accidentLiability"] = accidentLiability;
                object["dutyRate"] = dutyRate;
                object["deductFee"] = deductFee;
                object["verifyAmout"] = verifyAmout;
                object["verifyDetail"] = $("#verifyDetail").val();
                object["bankAccount"] = bankAccount;
                object["bankCard"] = bankCard;
                object["bankName"] = bankName;
                object["bankInfoId"] = bankInfoId;
                if (!accidentLiability) {
                    errorMsg += "责任不能为空！</br>";
                    isError = true;
                }
                if (!dutyRate) {
                    errorMsg += "赔付比例不能为空！</br>";
                    isError = true;
                }
                if (!deductFee) {
                    errorMsg += "扣减费用不能为空！</br>";
                    isError = true;
                }
                if (!verifyAmout) {
                    errorMsg += "理算金额不能为空！</br>";
                    isError = true;
                }
                if (verifyAmout && eval(verifyAmout) > eval(nuclearLossSum)) {
                    errorMsg += "理算金额不能大于核损总金额！</br>";
                    isError = true;
                }
                if (!bankAccount) {
                    errorMsg += "开户名不能为空！</br>";
                    isError = true;
                }
                if (!bankCard) {
                    errorMsg += "银行账号不能为空！</br>";
                    isError = true;
                }
                if (!bankName) {
                    errorMsg += "银行名称不能为空！</br>";
                    isError = true;
                }
                if (!bankInfoId) {
                    errorMsg += "银行支行不能为空！</br>";
                    isError = true;
                }
            }

            if(isError){
                layer.msg(errorMsg, {icon: 2, time: 3000},function () {
                    $("button[name='actionBtn']").removeAttr("disabled");
                });
                return;
            }

            console.log(JSON.stringify(object));

            return object;

        }

        // 保存 / 提审
        function submitData(status) {
            let claimCaseObject = assembleData(status);
            if (claimCaseObject) {
                $.ajax({
                    url: "${ctx}/claimCaseObjectV2Controller/claimCaseObjectSubmit",
                    type: 'POST',
                    data: JSON.stringify(claimCaseObject),
                    async: true,
                    cache: false,
                    contentType: false,
                    processData: false,
                    success: function (data) {
                        var result = eval("(" + data + ")");
                        if (result.ret == "0") {
                            layer.msg(result.msg, {
                                icon: 1,
                                time: 2000,
                                shade: [0.0001, '#000']
                            }, function () {
                                window.location.href = "${ctx}/claimCaseObjectController/claimCaseObjectList?comeFrom=${comeFrom}&status=0";
                            });
                        } else {
                            layer.msg(result.msg, {
                                icon: 2,
                                time: 2000,
                                shade: [0.0001, '#000']
                            },function(){
                                $("button[name='actionBtn']").removeAttr("disabled");
                            });
                        }

                    },
                    error: function (data) {
                        var result = eval("(" + data + ")");
                        alert(result.msg);
                    }
                })
            }
        }

        // 审核通过
        function auditPass(status) {
            let claimCaseObject = assembleData(status);

            var lossAssessmentSum = $("#lossAssessmentSum").attr("sumMoney");

            var nuclearLossSum = $("#nuclearLossSum").attr("sumMoney");

            if (lossAssessmentSum != nuclearLossSum) {
                layer.msg("估损总金额不等于核损总金额！", {icon: 2, time: 3000},function () {
                    $("button[name='actionBtn']").removeAttr("disabled");
                });
                return;
            }

            if (claimCaseObject) {
                $.ajax({
                    url: "${ctx}/claimCaseObjectController/auditPass",
                    type: 'POST',
                    data: JSON.stringify(claimCaseObject),
                    async: true,
                    cache: false,
                    contentType: false,
                    processData: false,
                    success: function (data) {
                        var result = eval("(" + data + ")");
                        if (result.ret == "0") {
                            layer.msg(result.msg, {
                                icon: 1,
                                time: 2000,
                                shade: [0.0001, '#000']
                            }, function () {
                                var comeFrom = "${comeFrom}";
                                if (comeFrom == 2) {
                                    window.location.href = "${ctx}/claimCaseObjectController/claimCaseObjectList4BS?comeFrom=${comeFrom}&status=3";
                                }
                                if (comeFrom == 3) {
                                    window.location.href = "${ctx}/claimCaseObjectController/claimCaseObjectList4BS?comeFrom=${comeFrom}&status=5";
                                }
                            });
                        } else {
                            layer.msg(result.msg, {
                                icon: 2,
                                time: 2000,
                                shade: [0.0001, '#000']
                            },function(){
                                $("button[name='actionBtn']").removeAttr("disabled");
                            });
                        }

                    },
                    error: function (data) {
                        var result = eval("(" + data + ")");
                        alert(result.msg);
                    }
                });
            }
        }

        var layerTop = top.layer;

        // 审核驳回
        function auditReject(status) {
            let claimCaseObject = assembleData(status);
            if (claimCaseObject) {
                layer.prompt({
                    formType: 2,        //0（文本）默认1（密码）2（文本域）
                    title: '请输入驳回原因',
                    fixed: false,
                    area: ['800px', '480px'],
                    closeBtn: 1,
                    yes: function (index, layero) {
                        let value = layero.find(".layui-layer-input").val().trim();
                        if (!value) {
                            layerTop.msg("驳回原因不能为空", {
                                icon: 2,
                                time: 2000
                            });
                            return false;
                        }
                        claimCaseObject["reason"] = value;
                        console.log(claimCaseObject, "对象数据");
                        $.ajax({
                            url: "${ctx}/claimCaseObjectController/auditReject",
                            type: 'POST',
                            data: JSON.stringify(claimCaseObject),
                            async: true,
                            cache: false,
                            contentType: false,
                            processData: false,
                            success: function (data) {
                                var result = eval("(" + data + ")");
                                if (result.ret == "0") {
                                    layer.msg(result.msg, {
                                        icon: 1,
                                        time: 2000,
                                        shade: [0.0001, '#000']
                                    }, function () {
                                        var comeFrom = "${comeFrom}";
                                        if (comeFrom == 2) {
                                            window.location.href = "${ctx}/claimCaseObjectController/claimCaseObjectList4BS?comeFrom=${comeFrom}&status=3";
                                        }
                                        if (comeFrom == 3) {
                                            window.location.href = "${ctx}/claimCaseObjectController/claimCaseObjectList4BS?comeFrom=${comeFrom}&status=5";
                                        }
                                    });
                                } else {
                                    layer.msg(result.msg, {
                                        icon: 2,
                                        time: 2000,
                                        shade: [0.0001, '#000']
                                    },function(){
                                        $("button[name='actionBtn']").removeAttr("disabled");
                                    });
                                }
                            },
                            error: function (data) {
                                var result = eval("(" + data + ")");
                                alert(result.msg);
                            }
                        });
                    },
                    end: function () {
                        $("button[name='actionBtn']").removeAttr("disabled");
                    }
                });
            }
        }
    </script>
</head>


<body id="qc-Body" style="overflow-x:hidden;background: #fff;">
<div class="container-fluid">
    <div class="row">
        <form id="inputForm" onsubmit="return false">
            <div class="row">
                <div class="col-sm-8">
                    <div class="col-sm-12" style="margin-bottom: 5px;padding-left: 0px;padding-right: 0px;">
                        <div class="col-sm-3" style="padding-left: 0px;">
                            案件号：${claimCase.claimCaseNo}
                        </div>
                        <div class="col-sm-3" style="padding-left: 0px;">
                            姓名：${claimCase.treatName}
                        </div>
                        <div class="col-sm-3" style="padding-left: 0px;">
                            是否延迟报立案：<#if claimCase.delayReport?? && claimCase.delayReport == 1>是<#else >否</#if>
                        </div>
                        <div class="col-sm-6 label-hide-overflow" style="padding-left: 0px;" title="${claimCase.productName}">
                            产品名称：${claimCase.productName}
                        </div>
                        <div class="col-sm-5" style="padding-left: 0px;">
                            起保时间：${(policyPerson.startDate?string["yyyy-MM-dd HH:mm:ss"])!''}
                        </div>
                        <div class="col-sm-12" style="padding-left: 0px;">
                            承保公司：
                            <#if claimCase.province == '辽宁省' && claimCase.treatDate?datetime gte "2023-09-29 00:00:00"?datetime>
                                <span style="display:inline-block;background-color: #067ee5;color: #fff;padding: 2px 6px;border-radius: 2px;">国元</span>
                            <#else >
                                <span style="display:inline-block;background-color: #26c281;color: #fff;padding: 2px 6px;border-radius: 2px;">大地</span>
                            </#if>
                        </div>
                    </div>
                    <#-- 按钮 -->
                    <div class="col-sm-12 clear-padding" style="margin-bottom: 5px;">

                        <#--<div class="col-sm-2" style="padding-left: 0px;">
                            <div class="col-sm-12" style="padding-left: 0px;">
                                <button type="button" class="btn genTask btn-look" onclick="seePlanName('${product.id!''}')">
                                    查看产品方案
                                </button>
                            </div>
                        </div>-->

                        <div class="col-sm-2" style="padding-left: 0px;">
                            <button type="button" onclick="getHistroyCaseInfo('${claimCase.baseUserId}')" class="btn genTask">
                                历史案件
                            </button>
                        </div>

                        <div class="col-sm-2">
                            <button type="button" class="btn genTask circulation_reasons" onclick="seeCaseProcessReason('${claimCase.id}')"> 查看案件流转原因</button>
                        </div>

                    </div>
                    <#if claimCaseObject.reason != "">
                    <div class="col-sm-12 clear-padding" style="color: red">
                        驳回原因：${claimCaseObject.reason} （最新一次驳回原因）
                    </div>
                    </#if>
                </div>
                <div class="col-sm-4" style="margin-bottom: 5px;padding-left: 0px;padding-right: 0px;">
                    <#if !isShow>
                    <div class="col-sm-12 pull-left" style="margin: 20px auto;display: flex;">
                        <#if comeFrom == 1>
                            <!--采集按钮-->
                            <#if !readonly>
                                <div class="col-sm-3 col-sm-offset-3">
                                    <button name="actionBtn" style="background-color: #0597FF;" class="btn btn-lg" onclick="submitData('BAX21')">保存</button>
                                </div>
                                <div class="col-sm-3">
                                    <button name="actionBtn" style="background-color: #F49929;" class="btn btn-lg" onclick="submitData('BAX22')">提审</button>
                                </div>
                            </#if>
                            <!--理算按钮-->
                            <#if !verifyReadonly>
                                <div class="col-sm-3 col-sm-offset-3">
                                    <button name="actionBtn" style="background-color: #0597FF;" class="btn btn-lg" onclick="submitData('BAX31')">保存</button>
                                </div>
                                <div class="col-sm-3">
                                    <button name="actionBtn" style="background-color: #F49929;" class="btn btn-lg" onclick="submitData('BAX32')">提审</button>
                                </div>
                            </#if>
                        </#if>
                        <#if comeFrom == 2>
                            <#if claimCaseObject.status == "BAX22">
                                <div class="col-sm-3 col-sm-offset-3">
                                    <button name="actionBtn" style="background-color: #0597FF;" class="btn btn-lg" onclick="auditPass('BAX23')">通过</button>
                                </div>
                                <div class="col-sm-3">
                                    <button name="actionBtn" style="background-color: #F49929;" class="btn btn-lg" onclick="auditReject('BAX24')">驳回</button>
                                </div>
                            </#if>
                            <#if claimCaseObject.status == "BAX32">
                                <div class="col-sm-3 col-sm-offset-3">
                                    <button name="actionBtn" style="background-color: #0597FF;" class="btn btn-lg" onclick="auditPass('BAX33')">通过</button>
                                </div>
                                <div class="col-sm-3">
                                    <button name="actionBtn" style="background-color: #F49929;" class="btn btn-lg" onclick="auditReject('BAX34')">驳回</button>
                                </div>
                            </#if>
                        </#if>
                        <#if comeFrom == 3>
                            <#if claimCaseObject.status == "BAX25">
                                <div class="col-sm-3 col-sm-offset-3">
                                    <button name="actionBtn" style="background-color: #0597FF;" class="btn btn-lg" onclick="auditPass('BAX26')">通过</button>
                                </div>
                                <div class="col-sm-3">
                                    <button name="actionBtn" style="background-color: #F49929;" class="btn btn-lg" onclick="auditReject('BAX27')">驳回</button>
                                </div>
                            </#if>
                            <#if claimCaseObject.status == "BAX35">
                                <div class="col-sm-3 col-sm-offset-3">
                                    <button name="actionBtn" style="background-color: #0597FF;" class="btn btn-lg" onclick="auditPass('BAX36')">通过</button>
                                </div>
                                <div class="col-sm-3">
                                    <button name="actionBtn" style="background-color: #F49929;" class="btn btn-lg" onclick="auditReject('BAX37')">驳回</button>
                                </div>
                            </#if>
                        </#if>
                     <#--   <div class="col-sm-4">
                            <button style="background-color: #009F9F;color: white" class="exportBtn btn btn-lg">导出</button>
                        </div>-->

                    </div>
                    </#if>
                </div>
            </div>
            <!-- 左部分 -->
            <div class="col-sm-8" style="background: none!important;padding-right: 0px !important;">
                <#--审核头-->
                <div class="row">
                    <#-- 展示图片区域以及采集数据tbale -->
                    <div class="col-sm-12" style="padding: 5px 0px 0px 0px; background: none;">
                        <div class="row" style="height: 1050px; overflow: hidden;">
                            <div class="col-sm-1" style="height: 1050px;background-color: rgba(226, 226, 226, 0.5);padding-left: 0px!important;padding-right: 10px!important; border-right: groove;">
                                <#if claimAttachList?exists && (claimAttachList?size>0)>
                                    <div id="thumbnail" style="height: 1050px;<#if (claimAttachList?size>11)>overflow-y: scroll;</#if>overflow-x:hidden">
                                        <ol style="padding-left:25px !important;/*padding-right: 15px !important;*/">
                                            <#list claimAttachList as attach>
                                                <li style="width:auto; height:auto; cursor:pointer;text-align: center;padding-top:5px">
                                                    <img id="thumbnail-${attach.id}" class="thumbnail-img <#if attach_index == 0>selected-thumbnail-img</#if>"
                                                         data-fileid="${attach.id}" data-img-number=''
                                                         title="${attach.fileName}"
                                                         src="${attach.fileObjectId}" onerror="javascript:this.src='/a/job_done.png'"/>
                                                </li>
                                            </#list>
                                        </ol>
                                    </div>
                                </#if>
                            </div>
                            <div class="col-sm-11" style="height: 1300px;">
                                <ul id="images">
                                    <#list claimAttachList as attach>
                                        <li hidden="hidden">
                                            <img data-fileid="${attach.id}"
                                                 title="${attach.fileName}"
                                                 src="${attach.fileObjectId}"
                                                 alt="${attach.id}"/>
                                        </li>
                                    </#list>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 右部分 -->
            <div class="col-sm-4" style="padding-left: 15px;">
                <div class="row" style="font-size: 15px;margin: 9px 0px;display: flex;align-items: center;">
                    <div class="col-sm-3 clear-padding" style="text-align: right">估损清单名称</div>
                    <div class="col-sm-9 ">
                        <input id="objectName" type="text" name="billCollection" data-valid="isEmpty"  class="form-control" value="${claimCaseObject.name!''}" <#if readonly?? >readonly</#if> />
                    </div>
                </div>
                <div class="row" style="margin-bottom: 20px">
                    <div style="margin-left: 5px;margin-bottom: 10px;">
                        <img class="marginLeft15" src="${ctx}/images/u48.svg" align="left" style="margin-top: 3px;"/>
                        <span style="font-size: 16px;font-weight: bold;margin-left: 7px">人伤基本信息 </span>
                    </div>
                    <div name="dataCollectArea" class="row" >
                        <div class="col-sm-12 align-item-center clear-padding-left" style="margin: 7px 0px;">
                            <div class="col-sm-2 clear-padding label-right">伤者姓名</div>
                            <div class="col-sm-4">
                                <input type="text" validName="billCollection" id="treatName" data-valid="isEmpty" name="treatName" title="${claimCaseObject.treatName}" value="${claimCaseObject.treatName}" class="form-control" <#if readonly?? || claimCaseObject.type == 1>readonly</#if> />
                            </div>
                            <div class="col-sm-2 clear-padding label-right">性别</div>
                            <div class="col-sm-4">
                                <select id="gender" name="gender" class="form-control " <#if readonly??>disabled</#if> >
                                    <option value="">--请选择--</option>
                                    <option value="男" <#if claimCaseObject.gender == "男">selected</#if>>男</option>
                                    <option value="女" <#if claimCaseObject.gender == "女">selected</#if>>女</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-sm-12 align-item-center clear-padding-left" style="margin: 7px 0px;">
                            <div class="col-sm-2 clear-padding label-right" >身份证号</div>
                            <div class="col-sm-10 ">
                                <input type="text" validName="billCollection" id="treatIdNum" data-valid="isIdCard isEmpty" name="treatIdNum" title="${claimCaseObject.treatIdNum}" value="${claimCaseObject.treatIdNum}" class="form-control" <#if readonly?? || claimCaseObject.type == 1>readonly</#if> />
                            </div>
                        </div>
                        <div class="col-sm-12 align-item-center clear-padding-left" style="margin: 7px 0px;">
                            <div class="col-sm-2 clear-padding label-right" >年龄</div>
                            <div class="col-sm-10 ">
                                <input type="text" validName="billCollection" id="age" data-valid="isDigits isEmpty" name="age" title="${claimCaseObject.age}" value="${claimCaseObject.age}" class="form-control" <#if readonly??>readonly</#if> />
                            </div>
                        </div>
                        <div class="col-sm-12 align-item-center clear-padding-left" style="margin: 7px 0px;">
                            <div class="col-sm-2 clear-padding label-right">受伤部位</div>
                            <div class="col-sm-10 ">
                                <input type="text" validName="billCollection" id="injuredArea" data-valid="isEmpty" name="injuredArea" title="${claimCaseObject.injuredArea}" value="${claimCaseObject.injuredArea}" class="form-control" <#if readonly??>readonly</#if> />
                            </div>
                        </div>
                        <div class="col-sm-12 align-item-center clear-padding-left" style="margin-top: 7px;">
                            <div class="col-sm-2 clear-padding label-right">伤情描述</div>
                            <div class="col-sm-10 ">
                                <select type="text" class="form-control js-example-tags kp-select2" id="injuredDesc" name="injuredDesc" <#if readonly??>disabled</#if> >
                                    <option value="">请选择</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <#if !readonly>
                <div class="row" style="margin-bottom: 20px">
                    <div style="margin-left: 5px;margin-bottom: 10px;">
                        <img class="marginLeft15" src="${ctx}/images/u48.svg" align="left" style="margin-top: 3px;"/>
                        <span style="font-size: 16px;font-weight: bold;margin-left: 7px">项目 </span>
                    </div>
                    <div style="margin-left: 10px" class="row" label="enumMapByParentCodeInfo">
                        <#if enumMapByParentCode?exists>
                            <#list enumMapByParentCode.keySet() as code>
                                <button onclick="buttonClick(this)" code='${code}' name='${enumMapByParentCode.get(code)}'>
                                    ${enumMapByParentCode.get(code)}
                                </button>
                            </#list>
                        </#if>
                    </div>
                </div>
                </#if>

                <div class="row" style="">
                    <div style="margin-left: 5px;">
                        <img class="marginLeft15" src="${ctx}/images/u48.svg" align="left" style="margin-top: 3px;"/>
                        <span style="font-size: 16px;font-weight: bold;margin-left: 7px">估损 </span>
                    </div>
                    <div id="dataCollectArea" class="row" style="height:550px;overflow-y: scroll;overflow-x: hidden">
                        <#list objectAssessmentList as assessment>
                            <div class="row" dataarea="1" dataareasubjectcode="${assessment.code}" dataareasujectname="${assessment.name}">
                                <div class="col-sm-8" style="margin: 15px;margin-left: 0px;">
                                    <div class="subject-name">${assessment.name}</div>
                                </div>
                                <div class="col-sm-12 clear-padding">
                                    <input id="id" type="hidden" value="${assessment.id}">
                                    <div class="col-sm-6 clear-padding" style="font-size: 15px;margin: 9px 0px;">
                                        <div class="col-sm-4 clear-padding" style="text-align: right">理赔标准</div>
                                        <div class="col-sm-6 ">
                                            <input name="billCollection" id="appeal" type="text" class="form-control" title="${assessment.appeal!''}" value="${assessment.appeal!''}" data-valid="isNumberAndDecimalPoint isEmpty" <#if readonly?? >readonly</#if>>
                                        </div>
                                    </div>
                                    <div class="col-sm-6 clear-padding" style="font-size: 15px;margin: 9px 0px;">
                                        <div class="col-sm-4 clear-padding" style="text-align: right">定损金额</div>
                                        <div class="col-sm-6 ">
                                            <input name="billCollection" id="lossAssessment" type="text" class="form-control" title="${assessment.lossAssessment!''}" value="${assessment.lossAssessment!''}" data-valid="isNumberAndDecimalPoint isEmpty" <#if readonly?? >readonly</#if>>
                                        </div>
                                    </div>
                                    <div class="col-sm-12 clear-padding" style="font-size: 15px;margin: 9px 0px;">
                                        <div class="col-sm-2 clear-padding" style="text-align: right">备注</div>
                                        <div class="col-sm-9 ">
                                            <input name="billCollection" id="remark" type="text" class="form-control" title="${assessment.remark!''}" value="${assessment.remark!''}" data-valid="none" <#if readonly?? >readonly</#if>>
                                        </div>
                                    </div>
                                    <div class="col-sm-12 clear-padding" style="font-size: 15px;margin: 9px 0px;">
                                        <div class="col-sm-2 clear-padding" style="text-align: right">核损金额</div>
                                        <div class="col-sm-9 ">
                                            <input name="billCollection" id="approvedAmount" type="text" class="form-control" title="${assessment.approvedAmount!''}" value="${assessment.approvedAmount!''}" data-valid="isNumberAndDecimalPoint isEmpty" <#if claimCaseObject.status != "BAX22" && claimCaseObject.status != "BAX25">readonly</#if>>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </#list>
                        <div class="row" style="">
                            <div style="display: flex;align-items: center;margin: 5px 15px;margin-top: 25px;">
                                <div class="col-sm-3 clear-padding" style="font-size: 24px;font-weight: bold;">估损描述</div>
                                <div class="col-sm-9 clear-padding">
                                    <input type="text" id="objectRemark" name="objectRemark" class="form-control" title="${claimCaseObject.remark!''}" value="${claimCaseObject.remark!''}" <#if readonly?? >readonly</#if>>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div style="margin: 5px 15px;display: flex;align-items: center;">
                                <span style="font-size: 24px;color: red;font-weight: bold" id="lossAssessmentSum" sumMoney="${(claimCaseObject.lossAssessmentSum?string('0.00'))!''}">估损合计：${(claimCaseObject.lossAssessmentSum?string('0.00'))!''} 元</span>
                            </div>
                        </div>
                        <div class="row">
                            <div style="margin: 5px 15px;display: flex;align-items: center;">
                                <span style="font-size: 24px;color: red;font-weight: bold" id="nuclearLossSum" sumMoney="${(claimCaseObject.nuclearLossSum?string('0.00'))!''}">核损合计：${(claimCaseObject.nuclearLossSum?string('0.00'))!''} 元</span>
                            </div>
                        </div>

                        <!--理算部分-->
                        <#if claimCaseObject.status?starts_with("BAX3") || claimCaseObject.status == "BAX99" >
                        <div>
                            <div class="row">
                                <div style="margin: 15px;display: flex;align-items: center;">
                                    <div class="col-sm-3 clear-padding" style="font-size: 24px;font-weight: bold;">责任比例</div>
                                    <div class="col-sm-4 clear-padding-left">
                                        <select type="text" class="form-control" id="accidentLiability" name="accidentLiability" disabled>
                                            <option value="全责" <#if claimCaseObject.accidentLiability == "全责">selected</#if> data-dutyRate="100" >全责</option>
                                            <option value="主责" <#if claimCaseObject.accidentLiability == "主责">selected</#if> data-dutyRate="70" >主责</option>
                                            <option value="同责" <#if claimCaseObject.accidentLiability == "同责">selected</#if> data-dutyRate="50" >同责</option>
                                            <option value="次责" <#if claimCaseObject.accidentLiability == "次责">selected</#if> data-dutyRate="30" >次责</option>
                                            <option value="无责" <#if claimCaseObject.accidentLiability == "无责">selected</#if> data-dutyRate="0" >无责</option>
                                            <option value="责任待确认" <#if claimCaseObject.accidentLiability == "责任待确认">selected</#if> data-dutyRate="" >责任待确认</option>
                                        </select>
                                    </div>
                                    <div class="col-sm-4 clear-padding-left">
                                        <input name="billCollection" placeholder="赔付比例" title="赔付比例" id="dutyRate" type="text" class="form-control" value="${claimCaseObject.dutyRate!claimCaseObject.accidentProportion}" data-valid="isRate isEmpty" <#if verifyReadonly??>readonly</#if>/>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div style="margin: 15px;display: flex;align-items: center;">
                                    <div class="col-sm-3 clear-padding" style="font-size: 24px;font-weight: bold;">扣减费用</div>
                                    <div class="col-sm-9 clear-padding-left">
                                        <input name="billCollection" id="deductFee" type="text" class="form-control" title="${claimCaseObject.deductFee!''}" value="${claimCaseObject.deductFee!''}" data-valid="isNumberAndDecimalPoint isEmpty" <#if verifyReadonly??>readonly</#if>/>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div style="margin: 15px;display: flex;align-items: center;">
                                    <div class="col-sm-3 clear-padding" style="font-size: 24px;font-weight: bold;">理算金额</div>
                                    <div class="col-sm-9 clear-padding-left">
                                        <input name="billCollection" id="verifyAmout" type="text" class="form-control" title="${claimCaseObject.verifyAmout!''}" value="${claimCaseObject.verifyAmout!''}" data-valid="isNumberAndDecimalPoint isEmpty" <#if verifyReadonly??>readonly</#if>/>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div style="margin: 15px;display: flex;align-items: center;">
                                    <div class="col-sm-3 clear-padding" style="font-size: 24px;font-weight: bold;">理算描述</div>
                                    <div class="col-sm-9 clear-padding-left">
                                        <input name="billCollection" id="verifyDetail" type="text" class="form-control" title="${claimCaseObject.verifyDetail!''}" value="${claimCaseObject.verifyDetail!''}" data-valid="none" <#if verifyReadonly??>readonly</#if>/>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div style="margin: 15px;display: flex;align-items: center;">
                                    <div class="col-sm-3 clear-padding" style="font-size: 24px;font-weight: bold;">开户名</div>
                                    <div class="col-sm-9 clear-padding-left">
                                        <input name="billCollection" id="bankAccount" type="text" class="form-control" title="${claimCaseObject.bankAccount!''}" value="${claimCaseObject.bankAccount!''}" data-valid="isEmpty" <#if verifyReadonly??>readonly</#if>/>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div style="margin: 15px;display: flex;align-items: center;">
                                    <div class="col-sm-3 clear-padding" style="font-size: 24px;font-weight: bold;">银行账号</div>
                                    <div class="col-sm-9 clear-padding-left">
                                        <input name="billCollection" id="bankCard" type="text" class="form-control"  title="${claimCaseObject.bankCard!''}" value="${claimCaseObject.bankCard!''}" data-valid="isEmpty" <#if verifyReadonly??>readonly</#if>/>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div style="margin: 15px;display: flex;align-items: center;">
                                    <div class="col-sm-3 clear-padding" style="font-size: 24px;font-weight: bold;">银行名称</div>
                                    <div class="col-sm-9 clear-padding-left">
                                        <select id="totalBankSelect" name="totalBankSelect" <#if verifyReadonly??>disabled</#if>
                                                class="js-data-example-ajax">
                                        </select>
                                        <input id="bankName" name="bankName" type="hidden" value="${claimCaseObject.bankName}">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div style="margin: 15px;display: flex;align-items: center;">
                                    <div class="col-sm-3 clear-padding" style="font-size: 24px;font-weight: bold;">银行支行</div>
                                    <div class="col-sm-9 clear-padding-left">
                                        <select id="bankSubbranchSelect" name="bankSubbranchSelect" class="js-data-example-ajax" disabled>
                                        </select>
                                        <input id="bankInfoId" name="bankInfoId" type="hidden" value="${claimCaseObject.bankInfoId}">
                                    </div>
                                </div>
                            </div>
                        </div>
                        </#if>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

</body>
</html>
