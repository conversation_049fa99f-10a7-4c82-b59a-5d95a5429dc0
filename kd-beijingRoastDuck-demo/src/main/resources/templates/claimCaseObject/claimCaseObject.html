<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!-- 上述3个meta标签*必须*放在最前面，任何其他内容都*必须*跟随其后！ -->
    <meta name="description" content="">
    <meta name="author" content="">
    <title>损失项目</title>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <script src="${ctx}/carousel/dataCollection/viewer.js"></script>
    <script src="${ctx}/carousel/dataCollection/viewermain.js"></script>
    <link href="${ctx}/carousel/dataCollection/viewer.css" rel="stylesheet">
    <link href="${ctx}/plugins/select2/css/applyDuty/select2.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/plugins/select2/css/applyDuty/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/plugins/select2/css/applyDuty/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/jquery-multi-select/css/multi-select.css" rel="stylesheet"
          type="text/css"/>
    <script src="${ctx}/plugins/select2/js/select2.js" type="text/javascript"></script>
    <link href="${ctx}/metronic/global/plugins/jquery-multi-select/css/multi-select.css" rel="stylesheet"
          type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/jstree/dist/themes/default/style.min.css" rel="stylesheet"
          type="text/css"/>
    <script src="${ctx}/metronic/global/scripts/app.min.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/layouts/layout4/scripts/layout.min.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/layouts/layout4/scripts/demo.min.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/plugins/jquery-multi-select/js/jquery.multi-select.js"
            type="text/javascript"></script>

    <script src="${ctx}/js/genInjury.js" type="text/javascript"></script>
    <script src="${ctx}/js/idCardCheck.js" type="text/javascript"></script>
    <style>

        .container-fluid {
            margin: 20px;
        }

        .clear-margin {
            margin: 0px;
        }

        .clear-padding {
            padding: 0px;
        }

        .chart-search {
            margin-bottom: 10px;
            display: none;
        }


        .search-channel-content {
            height: 100px;
            overflow-y: auto;
            overflow-x: hidden;
        }

        .search-ul-list {
            list-style-type: none;
            height: 20px;
            padding-left: 0px;
            color: black;
        }

        .channel {
            float: left;
            display: block;
        }

        .typeClick {
            height: 112px;
            margin-bottom: 10px;
            border: 2px solid black;
            border-radius: 5px !important;
            margin-left: 20px;
            cursor: pointer
        }

        .typeClickClick {
            height: 112px;
            margin-bottom: 10px;
            border: 2px solid #1676ff;
            background-color: #1676ff;
            color: white;
            border-radius: 5px !important;
            margin-left: 20px;
            cursor: pointer
        }

        .has-error {
            border-color: #ce0d0d !important;
            color: red !important;
        }

        .bckColor {
            background-color: rgba(51, 153, 241, 0.3);
        }

        .imageListChoose {
            /*background-color: #bad6ff !important;*/
            border: 5px solid #055ad7;
        }

        .collectionCompleted {
            background-color: #E8F2FF;
        }

        .collectionButton {
            /*border-color: #1ABB00;*/
            border: 1px solid #1ABB00;
            background-color: transparent;
            border-radius: 6px !important;
            color: #1ABB00;
        }

        .collectionButtonClick {
            /*border-color: #1ABB00;*/
            border: 1px solid #1ABB00;
            background-color: #1ABB00 !important;
            border-radius: 6px !important;
            color: #FFFFFF;
        }

        .collectionData:hover {
            /*border-color: #1ABB00;*/
            border: 1px solid #1ABB00;
            background-color: #1ABB00;
            color: #FFFFFF;
        }

        .middleTable {
            border: 4px solid #1767ff !important;
        }

        .hangup {
            /*margin-left: 5px;*/
            color: #fff !important;
            background-color: #ce0d0d !important;
            border-radius: 5px !important;
            height: 40px;
            width: 100%;
        }

        .error {
            color: #ce0d0d !important;
        }

        .txt-over-hide {
            color: red;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            display: block;
            width: 650px;
        }

        .jump {
            /*float: right;*/
            border-radius: 5px !important;
            color: #3399ff !important;
            background-color: #fff !important;
            border-color: #3399ff;
            height: 40px;
            width: 100%;
        }

        .genTask {
            /*margin-left: 5px;*/
            color: #fff !important;
            background-color: #3399ff !important;
            border-radius: 5px !important;
            height: 40px;
            width: 100%;
        }

        .thClass {
            text-align: center;
            color: #fff;
        }

        .typeSelect {
            border-color: #3399ff;
            background-color: #3399ff;
        }

        .thumbnail-img {
            width: 50px;
            height: 45px;
            border: 4px solid #D6D6C1;
        }

        .selected-thumbnail-img {
            border: 4px solid #3399ff !important;
        }

        .collection-thumbnail-img {
            border: 4px solid #00B176;
        }

        /*里面的代码可以根据自己需求去进行更改*/
        /* 设置滚动条的样式 */
        ::-webkit-scrollbar {
            width: 5px;
        }

        /* 滚动槽 */
        ::-webkit-scrollbar-track {
            -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.3);
            border-radius: 10px;
        }

        /* 滚动条滑块 */
        ::-webkit-scrollbar-thumb {
            border-radius: 10px;
            background: rgba(0, 0, 0, 0.1);
            -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.5);
        }

        ::-webkit-scrollbar-thumb:window-inactive {
            background: rgba(255, 0, 0, 0.4);
        }

        .ticket-information {
            height: 1050px;
            overflow-y: scroll;
        }

        .dataDisplayArea {
            width: 100%;
            height: 19%;
            border-radius: 10px !important;
            /*border: 1px solid #aca8a8;*/
            border: 1px solid #797979;
            cursor: pointer;
            margin-top: 10px;
            overflow: hidden;
        }

        .selected-dataDisplayArea {
            background-color: #91cae9;
        }

        .submit-dataDisplayArea {
            border: 3px solid #44db69 !important;
        }

        .change-dataDisplayArea {
            border: 3px solid red !important;
        }

        .dataDisplayArea > div {
            height: 20%;
            padding: 10px 0px 0px 0px !important;
        }

        .dataDisplayArea-head {
            background-color: #3399ff;
            /*border-radius: 10px 10px 0px 0px !important;*/
        }

        .dataDisplayArea-head-left {
            color: #ffffff;
            font-size: 15px;
            padding: 0px 0px 0px 5px;
        }

        .dataDisplayArea-head-right {
            text-align: right;
            padding: 0px !important;
        }

        .dataDisplayArea-head-img {
            width: 30px;
            height: 18px;
        }

        .dataDisplayArea-body-left {
            opacity: 0.7;
        }

        .dataDisplayArea-body-right {
            text-align: right;
        }

        .row {
            margin: 0px 0px;
        }

        .subject-name {
            font-size: 20px;
        }

        .estimateInventoryDataBlue {
            background-color: #3581ee;
        }

        #enumMapByParentCodeInfo > button {
            margin: 5px 6px;
        }

        .line-center {
            display: inline-block;
            background-color: blue;
            border-radius: 50%;
            height: 30px;
            width: 30px;
            color: white;
            text-align: center;
            line-height: 30px;
        }

        .icon-plus {
            font-size: 30px;
            line-height: 30px;
        }

        .icon-plus:hover {
            cursor: pointer;
        }

        .select2-selection {
            background-color: #fff !important;
            padding: 6px 12px !important;
            height: 32px !important;
            font-size: 14px !important;
            line-height: 1.42857 !important;
            color: #4d6b8a !important;
        }

    </style>

    <script type="text/javascript">

        const proGenInjuryData = new GenInjury().getValue();

        var objectId = "${claimCaseObjectSup.objectId}";

        var layerBool = true;

        $(function () {

            $("select").filter("#injuredDesc").each(function(index) {
                $(this).select2({
                    placeholder: "请选择 | 请输入",
                    width: null,
                    tags: true,
                    allowClear: true
                });
            });

            //初始化 伤情
            proGenInjuryData.forEach(function (item, index) {
                var injuredDescOption = document.createElement("option");
                injuredDescOption.innerText = item['text'];
                injuredDescOption.value = item['id'];
                $("select").filter("#injuredDesc").each(function(index) {
                    let clonedNode = injuredDescOption.cloneNode(true);
                    $(this).append(clonedNode);
                });
            });

            // 初始化 骑手人伤信息
            var treatName = parent.$("#treatName-text").text().trim();
            var treatIdNum = parent.$("#treatIdNum-text").text().trim();
            var mobile = parent.$("#treatMobile").val();
            if (!mobile) {
                mobile = parent.$("#mobile-text").text();
            }
            mobile = mobile.trim();

            $("div[code='骑手人伤']").find("#treatName").val(treatName);
            $("div[code='骑手人伤']").find("#treatIdNum").val(treatIdNum);
            $("div[code='骑手人伤']").find("#mobile").val(mobile);

            if (checkService.isIdCard(treatIdNum)) {
                $("div[code='骑手人伤']").find("#gender").val(checkService.idCardToGender(treatIdNum));
            }

            var injuredDesc;
            if (objectId) {
                console.log(objectId, "objectId");
                // 初始化 数据
                let object = localStorage.getItem(objectId);
                object = JSON.parse(object);
                console.log(object);
                $("#typeCategory").val(object.type + "-" + object.category);
                switch(object.type + "-" + object.category) {
                    case "1-1":
                        $("div[code='骑手人伤']").find("input").each(function() {
                            var name = $(this).attr("name");
                            $(this).val(object[name]);
                        });

                        $("div[code='骑手人伤']").find("select").each(function() {
                            var name = $(this).attr("name");
                            $(this).val(object[name]);

                            let val =  $(this).val();
                            if (!val) {
                                let option = document.createElement("option");
                                option.innerText = object.injuredDesc;
                                option.value = object.injuredDesc;
                                option.selected = true;
                                $("div[code='骑手人伤']").find("#injuredDesc").append(option);
                            }
                            if (name == "injuredDesc") {
                                $("div[code='骑手人伤']").find("#select2-injuredDesc-container").text(object[name]);
                            }
                        });

                        $("div[code='骑手人伤']").show();
                        $("div[code='骑手人伤']").siblings("div[code]").hide();
                        break;
                    case "2-1":
                        $("div[code='三者人伤']").find("input").each(function() {
                            var name = $(this).attr("name");
                            $(this).val(object[name]);
                        });
                        $("div[code='三者人伤']").find("select").each(function() {
                            var name = $(this).attr("name");
                            $(this).val(object[name]);

                            let val =  $(this).val();
                            if (!val) {
                                let option = document.createElement("option");
                                option.innerText = object.injuredDesc;
                                option.value = object.injuredDesc;
                                option.selected = true;
                                $("div[code='三者人伤']").find("#injuredDesc").append(option);
                            }
                            if (name == "injuredDesc") {
                                $("div[code='三者人伤']").find("#select2-injuredDesc-container").text(object[name]);
                            }
                        });

                        $("div[code='三者人伤']").show();
                        $("div[code='三者人伤']").siblings("div[code]").hide();

                        break;
                    case "2-2":
                        $("div[code='物损']").find("input").each(function() {
                            var name = $(this).attr("name");
                            $(this).val(object[name]);
                        });
                        $("div[code='物损']").find("select").each(function() {
                            var name = $(this).attr("name");
                            $(this).val(object[name]);
                        });
                        $("div[code='物损']").show();
                        $("div[code='物损']").siblings("div[code]").hide();

                        break;
                    case "2-3":
                        $("div[code='车损']").find("input").each(function() {
                            var name = $(this).attr("name");
                            $(this).val(object[name]);
                        });
                        $("div[code='车损']").find("select").each(function() {
                            var name = $(this).attr("name");
                            $(this).val(object[name]);
                        });
                        $("div[code='车损']").show();
                        $("div[code='车损']").siblings("div[code]").hide();
                        break;
                }
            }


            $("#object>div[code]").on('blur', "input[name='treatIdNum']", function() {
                let val = $(this).val();
                if (val && !checkService.isIdCard(val)) {
                    layer.open({
                        title: '提示',
                        content: '当前证件号非身份证，请确认填写是否为其他证件号',
                        icon: 7
                    });
                    return;
                } else if(val) {
                    $("div[code='三者人伤']").find("#age").val(checkService.idCardToAge(val));
                    $("div[code='三者人伤']").find("#gender").val(checkService.idCardToGender(val));
                }
            });

            // input绑定check校验
            $("div[code]").on('blur', "input", check);

            // 类型切换
            $("#inputForm").on("change", "select[name='typeCategory']", function(){
                let objectName = $(this).val();
                $("#inputForm")[0].reset();         // 重置表单
                $("div").find("input").val("");
                $("div").find("select").val("");
                $("#select2-injuredDesc-container").text("请选择");
                $("#typeCategory").val(objectName);
                switch (objectName) {
                    case "1-1":

                        $("div[code='骑手人伤']").find("#treatName").val(treatName);
                        $("div[code='骑手人伤']").find("#treatIdNum").val(treatIdNum);
                        $("div[code='骑手人伤']").find("#mobile").val(mobile);

                        if (checkService.isIdCard(treatIdNum)) {
                            $("div[code='骑手人伤']").find("#gender").val(checkService.idCardToGender(treatIdNum));
                        }

                        $("div[code='骑手人伤']").show();
                        $("div[code='骑手人伤']").siblings("div[code]").hide();
                        break;
                    case "2-1":
                        $("div[code='三者人伤']").show();
                        $("div[code='三者人伤']").siblings("div[code]").hide();

                        break;
                    case "2-2":
                        $("div[code='物损']").show();
                        $("div[code='物损']").siblings("div[code]").hide();

                        break;
                    case "2-3":
                        $("div[code='车损']").show();
                        $("div[code='车损']").siblings("div[code]").hide();
                        break;
                }
                var index = parent.layer.getFrameIndex(window.name);    //获取窗口索引
                parent.layer.iframeAuto(index);                         //layer.open 高度自适应
                window.parent.iframeH();
            });
        });

        //生成uuid
        function guid() {
            return 'xxxxxxxxxxxx4xxxyxxxxxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
                var r = Math.random() * 16 | 0,
                    v = c == 'x' ? r : (r & 0x3 | 0x8);
                return v.toString(16).replace("-","");
            });
        }


        let check = function () {
            var _this = this;
            var val = _this.value;
            var valid = _this.getAttribute('data-valid');
            if (val && valid && !checkService[valid](val)) {
                $(_this).val("");
                layerBool = false;
                layer.msg("输入格式不对", {icon: 5, time: 1500, id: "LAYER_MSG"}, function(index) {
                    layerBool = true;
                    layer.close(index);
                });
                $(_this).focus();
                return;
            }
        }
        //校验规则
        const checkService = {
            // 不校验
            none: function () {
                return true;
            },

            //非空校验
            isEmpty: function (str) {
                if (str == null || str == "") return false;
            },

            //身份证校验
            isIdCard: function (str) {
                const idCardCheck = new IdCardCheck();
                if (idCardCheck.checkIdCard(str)) {
                    return false;
                }
                return true;
            },

            //更具身份证获取性别
            idCardToGender: function (str) {
                var sex = "";
                if (parseInt(str.substr(16, 1)) % 2 == 1) {
                    sex = "男";
                } else {
                    sex = "女";
                }
                return sex;
            },

            //更具身份证获取年龄
            idCardToAge: function (str) {
                var myDate = new Date();
                var month = myDate.getMonth() + 1;
                var day = myDate.getDate();
                var age = myDate.getFullYear() - str.substring(6, 10) - 1;
                if (str.substring(10, 12) < month || str.substring(10, 12) == month && str.substring(12, 14) <= day) {
                    age++;
                }
                return age;
            },

            //手机号校验
            isMobilePhone: function (str) {
                const mobileCheck = /^1[3456789][0-9]\d{8}$/;
                if (!mobileCheck.test(str)) {
                    return false;
                }
                return true;
            },

            // 只能输入数字[0-9]
            isDigits: function (str) {
                if (str == null || str == "") return true;
                var reg = /^\d+$/;
                return reg.test(str);
            },
            //百分比0-100
            isRate: function(str){
                if (str == null || str == "") return true;
                var reg = /^\d+$/;
                let result = false;
                if(reg.test(str) && (str>=0 && str <=100)){
                    result = true;
                }
                return result;
            },

            // 匹配english
            isEnglish: function (str) {
                if (str == null || str == "") return true;
                var reg = /^[A-Za-z]+$/;
                return reg.test(str);
            },

            // 匹配integer(包含正负)
            isInteger: function (str) {
                if (str == null || str == "") return true;
                var reg = /^[-\+]?\d+$/;
                return reg.test(str);
            },

            // 匹配汉字
            isChinese: function (str) {
                if (str == null || str == "") return true;
                var reg = /^[\u4e00-\u9fa5]+$/;
                return reg.test(str);
            },

            // 匹配中文(双字节字符,包括汉字和符号)
            isChineseChar: function (str) {
                if (str == null || str == "") return true;
                var reg = /^[\u0391-\uFFE5]+$/;
                return reg.test(str);
            },

            //匹配中英文
            isChineseAndEnglish: function (str) {
                if (str == null || str == "") return true;
                var reg = /^[\u4e00-\u9fa5a-zA-Z]+$/;
                return reg.test(str);
            },

            // 匹配URL
            isUrl: function (str) {
                if (str == null || str == "") return true;
                var reg = /^http:\/\/[A-Za-z0-9]+\.[A-Za-z0-9]+[\/=\?%\-&_~`@[\]\’:+!]*([^<>\"])*$/;
                return reg.test(str);
            },

            // 字符验证，只能包含中文、英文、数字、下划线、空格。
            stringCheck: function (str) {
                if (str == null || str == "") return true;
                var reg = /^[a-zA-Z0-9\u4e00-\u9fa5_ ,.，。]+$/;
                return reg.test(str);
            },

            //字符长度校验（最长64位）
            stringLengthCheck: function (str, length) {
                if (str == null || str == "") return true;
                length = length || 64;
                if (str.length > length) return false;
                return true;
            },
            //IP格式验证
            isIP: function (str) {
                if (str == null || str == "") return true;
                var reg = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;
                return reg.test(str);
            },
            //YYYYMMDD格式验证
            isTime: function (str) {
                if (str == null || str == "") return true;
                var reg = /^((([0-9]{3}[1-9]|[0-9]{2}[1-9][0-9]{1}|[0-9]{1}[1-9][0-9]{2}|[1-9][0-9]{3})(((0[13578]|1[02])(0[1-9]|[12][0-9]|3[01]))|((0[469]|11)(0[1-9]|[12][0-9]|30))|(02(0[1-9]|[1][0-9]|2[0-8]))))|((([0-9]{2})(0[48]|[2468][048]|[13579][26])|((0[48]|[2468][048]|[3579][26])00))0229))$/;
                return reg.test(str);
            },
            //只能输入数字和小数点
            isNumberAndDecimalPoint: function (str) {
                if (str == null || str == "") return true;
                var reg = /^\d+(\.\d{1,2})?$/;
                return reg.test(str);
            },
            //只能输入数字和小数点
            isNumberAndDecimalPointNotDefault: function (str) {
                if (str == null || str == "") return true;
                var reg = /^\d+(\.\d{1,2})?$/;
                return reg.test(str);
            },
            //select2--->是否
            isTrueFalse: function (str) {

                return true;
            },
            //伤残等级
            isDisabilityLevel: function () {
                return true;
            },
            //伤残等级
            isDeadSurplusQuota: function () {
                return true;
            }
        };


        // 必填校验
        var errorMsg = "";
        function checkVerify() {
            errorMsg = "";
            $("#object").find("div[code]:visible").find(".form-control").each(function() {
                let val = $(this).val().trim();
                let existsBool = $(this).parent().prev().text().includes("*");
                if (val == "" && existsBool) {
                    errorMsg = $(this).parent().prev().text().replace("*","") + "必填";
                    $(this).focus();
                    return false;   // break
                }
            });
        }

        function installData() {
            // 开始 必填校验
            checkVerify();
            if (errorMsg) {
                layer.msg(errorMsg, {icon: 5, time: 1000, id: "LAYER_MSG"});
                return false;
            }

            // 开始 组装数据
            var objectData = {};
            switch ($("#typeCategory").val()) {
                case "1-1":
                    objectData = {
                        type: 1,                                                                            // 骑手
                        category: 1,                                                                        // 人伤
                        typeName: "骑手人伤",                                                               // 骑手人伤
                        name: $("div[code='骑手人伤']").find("#treatName").val(),                           // 赔付对象名称
                        treatName: $("div[code='骑手人伤']").find("#treatName").val(),                      // 伤者姓名
                        treatIdNum: $("div[code='骑手人伤']").find("#treatIdNum").val(),                    // 身份证号
                        mobile: $("div[code='骑手人伤']").find("#mobile").val(),                            // 手机号
                        gender: $("div[code='骑手人伤']").find("#gender").val(),                            // 性别
                        injuredArea: $("div[code='骑手人伤']").find("#injuredArea").val(),                  // 受伤部位
                        injuredDesc: $("div[code='骑手人伤']").find("#injuredDesc").val(),                  // 伤情
                        estimatedOverallLoss: $("div[code='骑手人伤']").find("#estimatedOverallLoss").val(),         // 预估金额
                        hospitalName: $("div[code='骑手人伤']").find("#hospitalName").val()                 // 医院名称
                    };
                    break;
                case "2-1":
                    objectData = {
                        type: 2,                                                                            // 三者
                        category: 1,                                                                        // 人伤
                        typeName: "三者人伤",                                                               // 三者人伤
                        name: $("div[code='三者人伤']").find("#treatName").val(),                           // 赔付对象名称
                        treatName: $("div[code='三者人伤']").find("#treatName").val(),                      // 伤者姓名
                        treatIdNum: $("div[code='三者人伤']").find("#treatIdNum").val(),                    // 身份证号
                        age: $("div[code='三者人伤']").find("#age").val(),                                  // 年龄
                        gender: $("div[code='三者人伤']").find("#gender").val(),                            // 性别
                        injuredArea: $("div[code='三者人伤']").find("#injuredArea").val(),                  // 受伤部位
                        injuredDesc: $("div[code='三者人伤']").find("#injuredDesc").val(),                  // 伤情
                        mobile: $("div[code='三者人伤']").find("#mobile").val(),                            // 手机号
                        workAddress: $("div[code='三者人伤']").find("#workAddress").val(),                  // 三者工作单位
                        homeAddress: $("div[code='三者人伤']").find("#homeAddress").val(),                  // 家庭住址
                        estimatedOverallLoss: $("div[code='三者人伤']").find("#estimatedOverallLoss").val(), // 预估金额
                        hospitalName: $("div[code='三者人伤']").find("#hospitalName").val()                  // 医院名称
                    };
                    break;
                case "2-2":     // 三者物损
                    objectData = {
                        type: 2,                                                                            // 三者
                        category: 2,                                                                        // 物损
                        typeName: "三者物损",                                                               // 三者物损
                        name: $("div[code='物损']").find("#name").val(),                                    // 品名(赔付对象名称)
                        mobile: $("div[code='物损']").find("#mobile").val(),                                // 手机号
                        estimatedOverallLoss: $("div[code='物损']").find("#estimatedOverallLoss").val()     // 预估金额
                    };
                    break;
                case "2-3":   // 三者车损
                    objectData = {
                        type: 2,                                                                            // 三者
                        category: 3,                                                                        // 机动车
                        typeName: "三者车损",                                                               // 三者车损
                        name: $("div[code='车损']").find("#carNumber").val(),                               // 三者车牌号(赔付对象名称)
                        carNumber: $("div[code='车损']").find("#carNumber").val(),                          // 三者车牌号
                        mobile: $("div[code='车损']").find("#mobile").val(),                                // 手机号
                        estimatedOverallLoss: $("div[code='车损']").find("#estimatedOverallLoss").val()     // 预估金额
                    };
                    break;
            }
            return submitObject(objectData);
        }

        function submitObject(objectData) {

            // 开始存储缓存
            var ajaxBool = true;
            if (objectId) {          // 编辑

                objectData.objectId = "${claimCaseObjectSup.objectId}";
                var objectList = [];
                if(localStorage.hasOwnProperty("${claimCaseObjectSup.pageId}")){
                    objectList = JSON.parse(localStorage.getItem("${claimCaseObjectSup.pageId}"));
                }
                // 校验同类型下重复name
                for (let i in objectList) {
                    let data = objectList[i];
                    if (objectData.name == data.name && objectData.typeName == data.typeName && objectData.objectId != data.objectId) {
                        layer.msg("当前赔付对象名称已存在", {icon: 5, time: 2000});
                        ajaxBool = false;
                    }
                    if (data.objectId == objectData.objectId) {
                        objectList[i] = objectData;
                    }
                }
                if (ajaxBool) {
                    localStorage.setItem("${claimCaseObjectSup.pageId}", JSON.stringify(objectList));
                }
            } else {                // 新增

                objectData.objectId = guid();

                var objectList = [];
                if(localStorage.hasOwnProperty("${claimCaseObjectSup.pageId}")){
                    objectList = JSON.parse(localStorage.getItem("${claimCaseObjectSup.pageId}"));
                }
                // 校验同类型下重复name
                for (let i in objectList) {
                    let data = objectList[i];
                    if (objectData.name == data.name && objectData.typeName == data.typeName && objectData.objectId != data.objectId) {
                        layer.msg("当前赔付对象名称已存在", {icon: 5, time: 2000});
                        ajaxBool = false;
                    }
                }
                if (ajaxBool) {
                    objectList.push(objectData);
                    localStorage.setItem("${claimCaseObjectSup.pageId}", JSON.stringify(objectList));
                }
            }
            return ajaxBool;
        }

        function save() {
            if (layerBool) {
                var bool = installData();
                if (bool) {
                    window.parent.freshDutyInfo();
                    parent.layer.closeAll();
                }
            }
        }

    </script>
</head>


<body id="qc-Body" style="overflow-x:hidden;background: #fff;">
<div class="container-fluid">
    <div class="row">
        <form id="inputForm" onsubmit="return false">
            <div class="row" style="display: flex;justify-content: space-evenly;">
                <!-- 左部分 -->
                <div class="col-sm-12" id="object" style="background: #F4F4F4;padding: 2%;margin: 1%;">
                    <div class="row" style="margin-bottom: 20px;">
                        <div class="col-sm-6 col-sm-offset-3">
                            <select class="form-control" id="typeCategory" name="typeCategory" >
                                <option value="1-1" >骑手人伤</option>
                                <option value="2-1" >三者人伤</option>
                                <option value="2-2" >三者物损</option>
                                <option value="2-3" >三者车损</option>
                            </select>
                        </div>
                    </div>
                    <div class="row" style="margin-bottom: 20px;" code="骑手人伤">
                        <div class="col-sm-9 col-sm-offset-1" style="margin-bottom: 20px;">
                           <div class="col-sm-3" style="text-align: right;line-height: 34px"><span style="color: red">*</span>伤者姓名</div>
                           <div class="col-sm-8">
                               <input type="text" class="form-control" id="treatName" name="treatName" disabled/>
                           </div>
                        </div>
                        <div class="col-sm-9 col-sm-offset-1" style="margin-bottom: 20px;">
                            <div class="col-sm-3" style="text-align: right;line-height: 34px"><span style="color: red">*</span>身份证号</div>
                            <div class="col-sm-8">
                                <input type="text" class="form-control" id="treatIdNum" name="treatIdNum" disabled/>
                            </div>
                        </div>
                        <div class="col-sm-9 col-sm-offset-1" style="margin-bottom: 20px;">
                            <div class="col-sm-3" style="text-align: right;line-height: 34px"><span style="color: red">*</span>手机号</div>
                            <div class="col-sm-8">
                                <input type="text" class="form-control" data-valid="isMobilePhone" id="mobile" name="mobile"/>
                            </div>
                        </div>
                        <div class="col-sm-9 col-sm-offset-1" style="margin-bottom: 20px;">
                            <div class="col-sm-3" style="text-align: right;line-height: 34px"><span style="color: red">*</span>性别</div>
                            <div class="col-sm-8">
                                <select id="gender" name="gender" class="form-control">
                                    <option value="">请选择性别</option>
                                    <option value="男">男</option>
                                    <option value="女">女</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-sm-9 col-sm-offset-1" style="margin-bottom: 20px;">
                            <div class="col-sm-3" style="text-align: right;line-height: 34px"><span style="color: red">*</span>受伤部位</div>
                            <div class="col-sm-8">
                                <input type="text" class="form-control" id="injuredArea" name="injuredArea" />
                            </div>
                        </div>
                        <div class="col-sm-9 col-sm-offset-1" style="margin-bottom: 20px;">
                            <div class="col-sm-3" style="text-align: right;line-height: 34px"><span style="color: red">*</span>伤情</div>
                            <div class="col-sm-8">
                                <select type="text" class="form-control js-example-tags kp-select2" id="injuredDesc" name="injuredDesc" >
                                    <option value="">请选择</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-sm-9 col-sm-offset-1" style="margin-bottom: 20px;">
                            <div class="col-sm-3" style="text-align: right;line-height: 34px">医院名称</div>
                            <div class="col-sm-8">
                                <input type="text" class="form-control" id="hospitalName" name="hospitalName" />
                            </div>
                        </div>
                        <div class="col-sm-9 col-sm-offset-1" style="margin-bottom: 20px;">
                            <div class="col-sm-3" style="text-align: right;line-height: 34px"><span style="color: red">*</span>预估金额</div>
                            <div class="col-sm-8">
                                <input type="text" class="form-control" data-valid="isNumberAndDecimalPoint" id="estimatedOverallLoss" name="estimatedOverallLoss" />
                            </div>
                        </div>
                    </div>
                    <div class="row" style="margin-bottom: 20px;display: none;" code="三者人伤">
                        <div class="col-sm-9 col-sm-offset-1" style="margin-bottom: 20px;">
                           <div class="col-sm-3" style="text-align: right;line-height: 34px"><span style="color: red">*</span>伤者姓名</div>
                           <div class="col-sm-8">
                               <input type="text" class="form-control" id="treatName" name="treatName"/>
                           </div>
                        </div>
                        <div class="col-sm-9 col-sm-offset-1" style="margin-bottom: 20px;">
                            <div class="col-sm-3" style="text-align: right;line-height: 34px">身份证号</div>
                            <div class="col-sm-8">
                                <input type="text" class="form-control" id="treatIdNum" name="treatIdNum" />
                            </div>
                        </div>
                        <div class="col-sm-9 col-sm-offset-1" style="margin-bottom: 20px;">
                            <div class="col-sm-3" style="text-align: right;line-height: 34px">年龄</div>
                            <div class="col-sm-8">
                                <input type="text" class="form-control" data-valid="isDigits" id="age" name="age" />
                            </div>
                        </div>
                        <div class="col-sm-9 col-sm-offset-1" style="margin-bottom: 20px;">
                            <div class="col-sm-3" style="text-align: right;line-height: 34px">性别</div>
                            <div class="col-sm-8">
                                <select id="gender" name="gender" class="form-control">
                                    <option value="">请选择性别</option>
                                    <option value="男">男</option>
                                    <option value="女">女</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-sm-9 col-sm-offset-1" style="margin-bottom: 20px;">
                            <div class="col-sm-3" style="text-align: right;line-height: 34px">受伤部位</div>
                            <div class="col-sm-8">
                                <input type="text" class="form-control" id="injuredArea" name="injuredArea"/>
                            </div>
                        </div>
                        <div class="col-sm-9 col-sm-offset-1" style="margin-bottom: 20px;">
                            <div class="col-sm-3" style="text-align: right;line-height: 34px">伤情</div>
                            <div class="col-sm-8">
                                <select type="text" class="form-control js-example-tags kp-select2" id="injuredDesc" name="injuredDesc" >
                                    <option value="">请选择</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-sm-9 col-sm-offset-1" style="margin-bottom: 20px;">
                            <div class="col-sm-3" style="text-align: right;line-height: 34px">医院名称</div>
                            <div class="col-sm-8">
                                <input type="text" class="form-control" id="hospitalName" name="hospitalName" />
                            </div>
                        </div>
                        <div class="col-sm-9 col-sm-offset-1" style="margin-bottom: 20px;">
                            <div class="col-sm-3" style="text-align: right;line-height: 34px">手机号</div>
                            <div class="col-sm-8">
                                <input type="text" class="form-control" data-valid="isMobilePhone" id="mobile" name="mobile"/>
                            </div>
                        </div>
                        <div class="col-sm-9 col-sm-offset-1" style="margin-bottom: 20px;">
                            <div class="col-sm-3" style="text-align: right;line-height: 34px">三者工作单位</div>
                            <div class="col-sm-8">
                                <input type="text" class="form-control" id="workAddress" name="workAddress" />
                            </div>
                        </div>
                        <div class="col-sm-9 col-sm-offset-1" style="margin-bottom: 20px;">
                            <div class="col-sm-3" style="text-align: right;line-height: 34px">家庭住址</div>
                            <div class="col-sm-8">
                                <input type="text" class="form-control" id="homeAddress" name="homeAddress" />
                            </div>
                        </div>
                        <div class="col-sm-9 col-sm-offset-1" style="margin-bottom: 20px;">
                            <div class="col-sm-3" style="text-align: right;line-height: 34px"><span style="color: red">*</span>预估金额</div>
                            <div class="col-sm-8">
                                <input type="text" class="form-control" data-valid="isNumberAndDecimalPoint" id="estimatedOverallLoss" name="estimatedOverallLoss" />
                            </div>
                        </div>
                    </div>
                    <div class="row" style="margin-bottom: 20px;display: none;" code="车损">
                        <div class="col-sm-9 col-sm-offset-1" style="margin-bottom: 20px;">
                           <div class="col-sm-3" style="text-align: right;line-height: 34px"><span style="color: red">*</span>三者车牌号</div>
                           <div class="col-sm-8">
                               <input type="text" class="form-control" id="carNumber" name="carNumber"/>
                           </div>
                        </div>
                        <div class="col-sm-9 col-sm-offset-1" style="margin-bottom: 20px;">
                            <div class="col-sm-3" style="text-align: right;line-height: 34px">手机号</div>
                            <div class="col-sm-8">
                                <input type="text" class="form-control" data-valid="isMobilePhone" id="mobile" name="mobile"/>
                            </div>
                        </div>
                        <div class="col-sm-9 col-sm-offset-1" style="margin-bottom: 20px;">
                            <div class="col-sm-3" style="text-align: right;line-height: 34px"><span style="color: red">*</span>预估金额</div>
                            <div class="col-sm-8">
                                <input type="text" class="form-control" data-valid="isNumberAndDecimalPoint" id="estimatedOverallLoss" name="estimatedOverallLoss" />
                            </div>
                        </div>
                    </div>
                    <div class="row" style="margin-bottom: 20px;display: none;" code="物损">
                        <div class="col-sm-9 col-sm-offset-1" style="margin-bottom: 20px;">
                            <div class="col-sm-3" style="text-align: right;line-height: 34px"><span style="color: red">*</span>品名</div>
                            <div class="col-sm-8">
                                <input type="text" class="form-control" id="name" name="name" />
                            </div>
                        </div>
                        <div class="col-sm-9 col-sm-offset-1" style="margin-bottom: 20px;">
                            <div class="col-sm-3" style="text-align: right;line-height: 34px">手机号</div>
                            <div class="col-sm-8">
                                <input type="text" class="form-control" data-valid="isMobilePhone" id="mobile" name="mobile" />
                            </div>
                        </div>
                        <div class="col-sm-9 col-sm-offset-1" style="margin-bottom: 20px;">
                            <div class="col-sm-3" style="text-align: right;line-height: 34px"><span style="color: red">*</span>预估金额</div>
                            <div class="col-sm-8">
                                <input type="text" class="form-control" data-valid="isNumberAndDecimalPoint" id="estimatedOverallLoss" name="estimatedOverallLoss" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row" style="text-align: center">
                <div class="col-sm-6">
                    <button class="btn btn-primary" onclick="save()" style="padding:5px 20px;font-size: 20px;">确定</button>
                </div>
                <div class="col-sm-6">
                    <button class="btn btn-primary" onclick="parent.layer.closeAll();" style="padding:5px 20px;font-size: 20px;">取消</button>
                </div>
            </div>
        </form>
    </div>
</div>

</body>
</html>
