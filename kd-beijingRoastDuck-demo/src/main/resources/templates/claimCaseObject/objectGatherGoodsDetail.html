<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!-- 上述3个meta标签*必须*放在最前面，任何其他内容都*必须*跟随其后！ -->
    <meta name="description" content="">
    <meta name="author" content="">
    <title>物损详情</title>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <script src="${ctx}/carousel/dataCollection/viewer.js"></script>
    <script src="${ctx}/carousel/dataCollection/viewermain.js"></script>
    <link href="${ctx}/carousel/dataCollection/viewer.css" rel="stylesheet">
    <link href="${ctx}/plugins/select2/css/applyDuty/select2.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/plugins/select2/css/applyDuty/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/plugins/select2/css/applyDuty/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/jquery-multi-select/css/multi-select.css" rel="stylesheet"
          type="text/css"/>
    <script src="${ctx}/plugins/select2/js/select2.js" type="text/javascript"></script>
    <link href="${ctx}/metronic/global/plugins/jquery-multi-select/css/multi-select.css" rel="stylesheet"
          type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/jstree/dist/themes/default/style.min.css" rel="stylesheet"
          type="text/css"/>
    <script src="${ctx}/metronic/global/scripts/app.min.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/layouts/layout4/scripts/layout.min.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/layouts/layout4/scripts/demo.min.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/plugins/jquery-multi-select/js/jquery.multi-select.js"
            type="text/javascript"></script>
    <style>

        .clear-margin {
            margin: 0px;
        }

        .clear-padding {
            padding: 0px;
        }

        .clear-padding-left {
            padding-left: 0px;
        }

        .chart-search {
            margin-bottom: 10px;
            display: none;
        }


        .search-channel-content {
            height: 100px;
            overflow-y: auto;
            overflow-x: hidden;
        }

        .search-ul-list {
            list-style-type: none;
            height: 20px;
            padding-left: 0px;
            color: black;
        }

        .channel {
            float: left;
            display: block;
        }

        .typeClick {
            height: 112px;
            margin-bottom: 10px;
            border: 2px solid black;
            border-radius: 5px !important;
            margin-left: 20px;
            cursor: pointer
        }

        .typeClickClick {
            height: 112px;
            margin-bottom: 10px;
            border: 2px solid #1676ff;
            background-color: #1676ff;
            color: white;
            border-radius: 5px !important;
            margin-left: 20px;
            cursor: pointer
        }

        .has-error {
            border-color: #ce0d0d !important;
            color: red !important;
        }

        .bckColor {
            background-color: rgba(51, 153, 241, 0.3);
        }

        .imageListChoose {
            /*background-color: #bad6ff !important;*/
            border: 5px solid #055ad7;
        }

        .collectionCompleted {
            background-color: #E8F2FF;
        }

        .collectionButton {
            /*border-color: #1ABB00;*/
            border: 1px solid #1ABB00;
            background-color: transparent;
            border-radius: 6px !important;
            color: #1ABB00;
        }

        .collectionButtonClick {
            /*border-color: #1ABB00;*/
            border: 1px solid #1ABB00;
            background-color: #1ABB00 !important;
            border-radius: 6px !important;
            color: #FFFFFF;
        }

        .collectionData:hover {
            /*border-color: #1ABB00;*/
            border: 1px solid #1ABB00;
            background-color: #1ABB00;
            color: #FFFFFF;
        }

        .middleTable {
            border: 4px solid #1767ff !important;
        }

        .hangup {
            /*margin-left: 5px;*/
            color: #fff !important;
            background-color: #ce0d0d !important;
            border-radius: 5px !important;
            height: 40px;
            width: 100%;
        }

        .error {
            color: #ce0d0d !important;
        }

        .txt-over-hide {
            color: red;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            display: block;
            width: 650px;
        }

        .jump {
            /*float: right;*/
            border-radius: 5px !important;
            color: #3399ff !important;
            background-color: #fff !important;
            border-color: #3399ff;
            height: 40px;
            width: 100%;
        }

        .genTask {
            /*margin-left: 5px;*/
            color: #fff !important;
            background-color: #3399ff !important;
            border-radius: 5px !important;
            height: 40px;
            width: 100%;
        }

        .thClass {
            text-align: center;
            color: #fff;
        }

        .typeSelect {
            border-color: #3399ff;
            background-color: #3399ff;
        }

        .thumbnail-img {
            width: 50px;
            height: 45px;
            border: 4px solid #D6D6C1;
        }

        .selected-thumbnail-img {
            border: 4px solid #3399ff !important;
        }

        .collection-thumbnail-img {
            border: 4px solid #00B176;
        }

        /*里面的代码可以根据自己需求去进行更改*/
        /* 设置滚动条的样式 */
        ::-webkit-scrollbar {
            width: 5px;
        }

        /* 滚动槽 */
        ::-webkit-scrollbar-track {
            -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.3);
            border-radius: 10px;
        }

        /* 滚动条滑块 */
        ::-webkit-scrollbar-thumb {
            border-radius: 10px;
            background: rgba(0, 0, 0, 0.1);
            -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.5);
        }

        ::-webkit-scrollbar-thumb:window-inactive {
            background: rgba(255, 0, 0, 0.4);
        }

        .ticket-information {
            height: 1050px;
            overflow-y: scroll;
        }

        .dataDisplayArea {
            width: 100%;
            height: 19%;
            border-radius: 10px !important;
            /*border: 1px solid #aca8a8;*/
            border: 1px solid #797979;
            cursor: pointer;
            margin-top: 10px;
            overflow: hidden;
        }

        .selected-dataDisplayArea {
            background-color: #91cae9;
        }

        .submit-dataDisplayArea {
            border: 3px solid #44db69 !important;
        }

        .change-dataDisplayArea {
            border: 3px solid red !important;
        }

        .dataDisplayArea > div {
            height: 20%;
            padding: 10px 0px 0px 0px !important;
        }

        .dataDisplayArea-head {
            background-color: #3399ff;
            /*border-radius: 10px 10px 0px 0px !important;*/
        }

        .dataDisplayArea-head-left {
            color: #ffffff;
            font-size: 15px;
            padding: 0px 0px 0px 5px;
        }

        .dataDisplayArea-head-right {
            text-align: right;
            padding: 0px !important;
        }

        .dataDisplayArea-head-img {
            width: 30px;
            height: 18px;
        }

        .dataDisplayArea-body-left {
            opacity: 0.7;
        }

        .dataDisplayArea-body-right {
            text-align: right;
        }

        .row {
            margin: 0px 0px;
        }

        .subject-name {
            font-size: 20px;
        }

        .estimateInventoryDataBlue {
            background-color: #1676ff;
        }

        .line-center {
            display: inline-block;
            background-color: blue;
            border-radius: 50%;
            height: 23px;
            width: 23px;
            color: white;
            text-align: center;
            line-height: 23px;
            font-size: 15px;
        }

        .icon-plus {
            font-size: 23px;
            line-height: 23px;
        }

        .icon-plus:hover {
            cursor: pointer;
        }

        .dataDisplayArea-head-img {
            margin: 10px 0px;
        }

        .dataDisplayArea-head-img:hover {
            cursor: pointer;
        }

        .form-control {
            height: 28px !important;
        }

        .label-right {
            font-size: 13px;
            text-align: right;
        }

        .align-item-center {
            display: flex;
            align-items: center;

        }

        .col-sm-1 {
            text-align: center;
        }

        textarea[class='layui-layer-input'] {
            outline: 0 !important;
            width: 700px !important;
            height: 350px !important;
            padding-left: 10px !important;
            border: 1px solid #e6e6e6 !important;
            color: #333 !important;
            box-shadow: none !important;
        }

        button[name='actionBtn'] {
            color: white;
        }

        select {
            padding: 0px 12px !important;
        }
    </style>

    <script type="text/javascript">

        var viewer = null;

        $(function () {

            /*图片展示工具*/
            (function () {
                viewer = new Viewer({
                    activeId: null
                });
            })();


            /*获取焦点移除报错样式*/
            $("body").on("focus", "input,textarea,select,.ggLayer", function () {
                document.onkeydown = function (w) {
                }
            });

            /*点击缩略图*/
            $('body').on('click', '.thumbnail-img', function () {
                if ($(this).hasClass('selected-thumbnail-img')) {
                    return;
                }
                let attrId = $(this).data('fileid');
                $('.selected-thumbnail-img').removeClass('selected-thumbnail-img');
                $(this).addClass('selected-thumbnail-img');
                viewer.showImageByFileId(attrId);
                let imgNumber = $(this).data('img-number');
                let offset = imgNumber * 95 - (1050 - 95) / 2;
                $('#thumbnail').scrollTop(offset);
            });


            /*A/D切换影像*/
            $('body').on('click', '.viewer-list > li', function () {
                let attchId = $(this).find('img').attr('alt');
                $('#thumbnail-' + attchId).trigger('click');
            });

            $("div[name='dataCollectArea']").on('change', "input[validName='billCollection']", check);

            // 刷新报价维修金额，当input失去焦点的时候
            $("div[name='dataCollectArea']").on('blur', "input[name='lossAssessment'],input[name='approvedAmount']", freshLossAssessment);
            $("#residualValue").on('blur', freshLossAssessment);
            $("#residualNuclearLossValue").on('blur', freshLossAssessment);

            //初始话数据
            <#if assessmentReportList?exists &&(assessmentReportList?size>0)>
            <#list assessmentReportList as assessmentReportData>
                addDataRow('${assessmentReportData.code}', '${assessmentReportData}');
            </#list>
            </#if>

            let status = '${status}';
            if (status == '0' || status == '2') {
                $.each($("[validName='billCollection']"), function (index, obj) {
                    $(this).attr("disabled", "1");
                });
                $.each($(""));
                $.each($(".icon-plus"), function (index, obj) {
                    $(this).remove();
                });
                $.each($(".dataDisplayArea-head-img"), function (index, obj) {
                    $(this).remove();
                });
            }
            if(status=='1'){
                $('#residualValue').val("");
                $('#verifyAmout').val("");
                $('#verifyDetail').val("");
                $('#dutyRate').val("");
                $('#deductFee').val("");
            }

            var createInput = function (name, value, type = "hidden") {
                var inputElement = document.createElement("input");
                inputElement.type = type;
                inputElement.name = name;
                if (value != null) {
                    inputElement.value = value;
                }
                return inputElement;
            }


            var postForm = function (url) {
                // 创建表单
                var formElement = document.createElement("form");
                formElement.action = url;
                formElement.method = "post";
                // 打开新标签
                formElement.target = '_blank';
                formElement.style.display = "none";
                var code = $.trim($("#assessmentReportName").val());
                var type = 2;//机动车为2
                if (code != "") {
                    formElement.appendChild(createInput("code", code));
                }
                formElement.appendChild(createInput("type", type));
                var assessmentFather = {};
                let lossAssessmentSum = $("#lossAssessmentSum").attr("sumMoney");
                if (lossAssessmentSum != "") {
                    assessmentFather.lossAssessmentSum = lossAssessmentSum;
                }
                let carNumber = $.trim($("#carNumber").val());
                if (carNumber != "") {
                    assessmentFather.carNumber = carNumber;
                }
                let carModel = $.trim($("#carModel").val());
                if (carModel != "") {
                    assessmentFather.carModel = carModel;
                }
                let carEncoding = $.trim($("#carEncoding").val());
                if (carModel != "") {
                    assessmentFather.carEncoding = carEncoding;
                }
                let firstRegistrationTime = $.trim($("#firstRegistrationTime").val());
                if (firstRegistrationTime != undefined && firstRegistrationTime.trim() != "") {
                    let firstRegistrationTimeStr = firstRegistrationTime.replace(/^(\d{4})(\d{2})(\d{2})$/, "$1-$2-$3");
                    firstRegistrationTime = new Date(firstRegistrationTimeStr+" 00:00:00").getTime();
                    assessmentFather.firstRegistrationTime = firstRegistrationTime;
                }
                let carOwner = $.trim($("#carOwner").val());
                if (carOwner != "") {
                    assessmentFather.carOwner = carOwner;
                }
                let lossAssessmentTime = $.trim($("#lossAssessmentTime").val());
                if (lossAssessmentTime != undefined && lossAssessmentTime.trim() != "") {
                    let lossAssessmentTimeStr = lossAssessmentTime.replace(/^(\d{4})(\d{2})(\d{2})$/, "$1-$2-$3");
                    lossAssessmentTime = new Date(lossAssessmentTimeStr+" 00:00:00").getTime();
                    assessmentFather.lossAssessmentTime = lossAssessmentTime;
                }
                let repairFactory = $.trim($("#repairFactory").val());
                if (repairFactory != "") {
                    assessmentFather.repairFactory = repairFactory;
                }
                let verifyAmout = $.trim($("#verifyAmout").val());
                if (verifyAmout != "") {
                    assessmentFather.verifyAmout = verifyAmout;
                }
                let residualValue = $.trim($("#residualValue").val());
                if (residualValue != "") {
                    assessmentFather.residualValue = residualValue;
                }
                let verifyDetail = $.trim($("#verifyDetail").val());
                if (verifyDetail != "") {
                    assessmentFather.verifyDetail = verifyDetail;
                }
                let is4S = $.trim($("#is4S").val());
                if (is4S != "") {
                    assessmentFather.is4S = is4S;
                }
                let dutyRate = $.trim($("#dutyRate").val());
                if (dutyRate != "") {
                    assessmentFather.dutyRate = dutyRate;
                }
                let deductFee = $.trim($("#deductFee").val());
                if (deductFee != "") {
                    assessmentFather.deductFee = deductFee;
                }

                assessmentFather.assessmentReport = [];
                $.each($("div[name='dataCollectAreaSubjet']"), function (index, obj) {
                    let code = $(this).attr("code");
                    $.each($(this).find("div[name='dataCollectAreaRow']"), function (index, obj) {
                        let repairName = $.trim($(this).find("input[name='repairName']").val());
                        let repairPrice = $.trim($(this).find("input[name='repairPrice']").val());
                        let approvedAmount = $.trim($(this).find("input[name='approvedAmount']").val());
                        let lossAssessment = $.trim($(this).find("input[name='lossAssessment']").val());

                        assessmentFather.assessmentReport.push({
                            "code": code,
                            "repairName": repairName,
                            "repairPrice": repairPrice,
                            "lossAssessment": lossAssessment,
                            "approvedAmount": approvedAmount
                        })
                    });
                });
                formElement.appendChild(createInput("assessmentFatherStr", JSON.stringify(assessmentFather)));
                document.body.appendChild(formElement);
                formElement.submit();
                formElement.remove();
                return;
            }


            //导出按钮监控器
            $("#inputForm").on("click", ".exportBtn", function (e) {
                e.stopPropagation();
                e.preventDefault();
                var _this = this;
                $(_this).removeClass("exportBtn");
                //提交之后再把class加回来 防止重复提交
                postForm("${ctx}/downloadCenterController/downloadAssessment");
                $(_this).addClass("exportBtn");

            });


            var bankSubbranchInitName = "请输入支行名称查询...";
            <#if bankInfo??>
            bankSubbranchInitName = "${bankInfo.bankName}";
            </#if>

            $("#bankSubbranchSelect").select2({
                ajax: {
                    type: 'POST',
                    url: "${ctx}/insuranceCaseController/getBankInfo",
                    dataType: 'json',
                    delay: 500,
                    contentType: "application/x-www-form-urlencoded; charset=utf-8",
                    data: function (params) {
                        return {
                            totalBankId: $('#totalBankSelect').val(),
                            paramMsg: params.term
                        };
                    },
                    processResults: function (data) {
                        console.log(data);
                        return {
                            results: data.bankList,
                        };
                    },
                },
                escapeMarkup: function (markup) {
                    return markup;
                },
                width: '100%',
                placeholder: "请输入支行名称查询...",
                minimumInputLength: 2,
                minimumResultsForSearch: Infinity, // 隐藏搜索框
                templateResult: function (data) {
                    if (data.id != undefined) {
                        return "<div>" + data.bankName+"("+data.provinceName+"-"+data.cityName+")" + "</div>";
                    }
                    return "";
                },
                templateSelection: function (data) {
                    if (data.id != "") {
                        $("#bankInfoId").val(data.id);
                        let bankText = data.bankName+"("+data.provinceName+"-"+data.cityName+")";
                        return "<span title='"+bankText+"'>" + bankText  + "</span>";
                    }
                    return bankSubbranchInitName;
                }
            });


            //查询总行信息
            $("#totalBankSelect").select2({
                ajax: {
                    type: 'POST',
                    url: "${ctx}/insuranceCaseController/getTotalBankInfo",
                    dataType: 'json',
                    delay: 500,
                    contentType: "application/x-www-form-urlencoded; charset=utf-8",
                    data: function (params) {
                        return {
                            paramMsg: params.term
                        };
                    },
                    processResults: function (data) {
                        return {
                            results: data.bankList,
                        };
                    },
                },
                escapeMarkup: function (markup) {
                    return markup;
                },
                width: '100%',
                placeholder: "请输入总行名称查询...",
                minimumInputLength: 2,
                minimumResultsForSearch: Infinity, // 隐藏搜索框
                templateResult: function (data) {
                    if (data.id != undefined) {
                        return "<div>" + data.bankName + "</div>";
                    }
                    return "";
                },
                templateSelection: function (data) {
                    if (data.id != "") {
                        $("#bankName").val(data.bankName);
                        $('#bankSubbranchSelect').prop('disabled', false);
                        $("#bankInfoId").val("");
                        $("#bankSubbranchSelect").select2("val", "");
                        $("#select2-bankSubbranchSelect-container .select2-selection__placeholder").html("");
                        return "<span>" + data.bankName + "</span>";
                    }
                    $("#bankName").val("");
                    $("#bankInfoId").val("");
                    $("#bankSubbranchSelect").select2("val", "");
                    $('#bankSubbranchSelect').prop('disabled', true);
                    return "请输入总行名称查询...";
                }
            });

            // 初始化 银行信息
            $("#select2-totalBankSelect-container .select2-selection__placeholder").html("${claimCaseObject.bankName!''}");
            $("#select2-bankSubbranchSelect-container .select2-selection__placeholder").html("${bankInfo.bankName!''}");
            $("#bankName").val("${claimCaseObject.bankName!''}");
            $("#bankInfoId").val("${claimCaseObject.bankInfoId!''}");

            // 责任变更同步比例
            $("div[name='dataCollectArea']").on("change", "#accidentLiability", function() {
                let dutyRate = $(this).find("option:selected").attr("data-dutyRate");
                $("#dutyRate").val(dutyRate);
            });

            freshLossAssessment();

            // 判断是否查看
            var isShow = "${isShow}";
            if (isShow) {
                $("input").attr("disabled", "1");
                $("select").attr("disabled", "1");
                $("textarea").attr("disabled", "1");
            }

            var layerTop = top.layer;
            var errorMsg = "${errorMsg}";
            if (errorMsg) {
                layerTop.msg(errorMsg, {
                    icon: 2,
                    time: 3000
                });
            }
        });

        //刷新报价维修金额
        function freshLossAssessment() {
            var repairPriceSum = new BigDecimal("0.00");
            var sumApprovedAmount = new BigDecimal("0.00");
            $.each($("div[name='dataCollectAreaSubjet']"), function (index, obj) {
                let sumLineRepairPrice = new BigDecimal("0");

                $.each($(this).find("div[name='dataCollectAreaRow']"), function (index, obj) {
                    try {
                        let lossAssessment = $(this).find("input[name='lossAssessment']").val();
                        if (lossAssessment != undefined && lossAssessment.trim() != '') {
                            lossAssessment = new BigDecimal(lossAssessment).setScale(2, MathContext.ROUND_HALF_UP);
                            sumLineRepairPrice = sumLineRepairPrice.add(lossAssessment).setScale(2, MathContext.ROUND_HALF_UP);
                            repairPriceSum = repairPriceSum.add(lossAssessment).setScale(2, MathContext.ROUND_HALF_UP);
                        }

                        let approvedAmount = $(this).find("input[name='approvedAmount']").val();
                        if (approvedAmount != undefined && approvedAmount.trim() != '') {
                            approvedAmount = new BigDecimal(approvedAmount).setScale(2, MathContext.ROUND_HALF_UP);
                            sumApprovedAmount = sumApprovedAmount.add(approvedAmount).setScale(2, MathContext.ROUND_HALF_UP);
                        }
                    } catch (e) {

                    }

                });
                let code = $(this).attr("code");
                let remark = "合计";
                switch (code) {
                    case "2-1":
                        remark = "配件金额合计：";
                        break;
                    case "2-2":
                        remark = "维修人工合计：";
                        break;
                }
                $("#" + code + "LossAssessment").html(remark + sumLineRepairPrice + "元");
            });
            //维修合计金额=配件合计+人工合计-残值
            let residualValue = new BigDecimal("0");
            try {
                residualValue = new BigDecimal($("#residualValue").val());
            } catch (e) {
            }
            repairPriceSum = repairPriceSum.subtract(residualValue).setScale(2, MathContext.ROUND_HALF_UP);

            // 核损合计 = 核损合计 - 残值核损金额
            let residualNuclearLossValue = new BigDecimal("0");
            try {
                residualNuclearLossValue = new BigDecimal($("#residualNuclearLossValue").val());
            } catch (e) {
            }
            sumApprovedAmount = sumApprovedAmount.subtract(residualNuclearLossValue).setScale(2, MathContext.ROUND_HALF_UP);

            $("#lossAssessmentSum").html("维修估损总金额：" + repairPriceSum + "元");
            $("#lossAssessmentSum").attr("sumMoney", repairPriceSum);

            $("#nuclearLossSum").html("核损合计：" + sumApprovedAmount + "元");
            $("#nuclearLossSum").attr("sumMoney", sumApprovedAmount);
        }




        // 添加项目
        function addDataRowClick(code) {
            let data = {};
            data["id"] = "";
            data["name"] = "";
            data["lossAssessment"] = "";
            data["approvedAmount"] = "";
            addDataRow(code, JSON.stringify(data))
        }

        //增加一条数据
        function addDataRow(code, data) {
            data = JSON.parse(data);
            console.log(data.id);
            let name=data.name;
            if(name == undefined){
                name = "";
            }
            let lossAssessment=data.lossAssessment;
            if(lossAssessment == undefined){
                lossAssessment = "";
            }
            let approvedAmount=data.approvedAmount;
            if(approvedAmount == undefined){
                approvedAmount = "";
            }
            let after = $("#" + code + "collectArea").append(
                '<div class="col-sm-12 align-item-center" style="margin-top: 10px" name="dataCollectAreaRow">\n' + '<input type="hidden"  name="id" class="form-control" value="' + data.id + '" />' +
                '                                        <div class="col-sm-1 clear-padding" style="text-align: left;">\n' +
                '                                            <div class="clear-padding line-center"></div>\n' +
                '                                        </div>\n' +
                '                                        <div class="col-sm-4 clear-padding align-item-center">\n' +
                '                                            <div class="col-sm-3 clear-padding">名称</div>\n' +
                '                                            <div class="col-sm-9 clear-padding-left" >\n' +
                '                                                <input type="text" style="padding: 0px !important;" validName="billCollection" data-valid="isEmpty" name="name" class="form-control" value="' + name + '" title="'+name+'" />\n' +
                '                                            </div>\n' +
                '                                        </div>\n' +
                /*'                                        <div class="col-sm-3 clear-padding" >\n' +
                '                                            <div class="col-sm-4 clear-padding" style="font-size: 15px;display: inline-block;text-align: center;line-height: 15px">维修报价</div>\n' +
                '                                            <div class="col-sm-8 " >\n' +
                '                                                <input type="text" style="padding: 0px !important;" validName="billCollection" data-valid="isNumberAndDecimalPoint" name="repairPrice" class="form-control" value="' + repairPrice + '" title="'+repairPrice+'" />\n' +
                '                                            </div>\n' +
                '                                        </div>\n' +*/
                '                                        <div class="col-sm-3 clear-padding align-item-center" >\n' +
                '                                            <div class="col-sm-4 clear-padding" >估损</div>\n' +
                '                                            <div class="col-sm-8 clear-padding-left" >\n' +
                '                                                <input type="text" validName="billCollection" name="lossAssessment" data-valid="isNumberAndDecimalPoint isEmpty" class="form-control clear-padding" value="' + lossAssessment + '" title="'+lossAssessment+'" />\n' +
                '                                            </div>\n' +
                '                                        </div>\n' +
                '                                        <div class="col-sm-3 clear-padding align-item-center" >\n' +
                '                                            <div class="col-sm-4 clear-padding">核损</div>\n' +
                '                                            <div class="col-sm-8 clear-padding-left" >\n' +
                '                                                <input type="text" validName="billCollection" name="approvedAmount" data-valid="isNumberAndDecimalPoint" class="form-control clear-padding" value="' + approvedAmount + '" title="'+approvedAmount+'" readonly />\n' +
                '                                            </div>\n' +
                '                                        </div>\n' +
                '                                        <div class="col-sm-1 clear-padding" >\n' +
                '                      <img src="/images/shanchu.svg"  class="dataDisplayArea-head-img" onclick="delDataRow(this,' + code + ')">' +
                '                                        </div>\n' +
                '                                    </div>'
            );
            //刷新序号
            freshLineCenter();
            //刷新维修报价金额
            freshLossAssessment();
        }

        //删除一行数据
        function delDataRow(obj, code) {
            $(obj).parent().parent().remove();
            //刷新序号
            freshLineCenter();
            //刷新维修报价金额
            freshLossAssessment();
        }

        //刷新序号
        function freshLineCenter() {
            $.each($("div[name='dataCollectAreaRow']"), function (index, obj) {
                let prevName = $(this).prev().attr("name");
                let num = 1;
                if (prevName != undefined && prevName == "dataCollectAreaRow") {
                    let prevNum = $(this).prev().find(".line-center").html();
                    num = new Number(prevNum) + 1;
                }
                $(this).find(".line-center").html(num);
            });
        }


        function seePlanName(planId) {
            if (typeof planId == 'undefined' || planId == '') {
                layer.msg('暂未数据！！！', {
                    icon: 2,
                    time: 2000
                });
                return;
            }

            var formData = new FormData();
            formData.append("planId", planId);
            $.ajax({
                url: "${ctx}/insuranceCaseController/seePlanNameByPlanId",
                type: 'POST',
                data: formData,
                async: true,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    var result = eval("(" + data + ")");
                    if (result.ret == "0") {
                        window.open(result.msg, "_blank");
                    } else {
                        layer.msg(result.msg, {
                            icon: 2,
                            time: 2000
                        });
                    }
                },
                error: function (data) {
                    var result = eval("(" + data + ")");
                    alert(result.msg);
                }
            });

        }

        function seeCaseProcessReason(claimCaseId) {
            var openWindowWidth = $(document).width() * 0.8 + "px";
            var offsetH = ($(window).height() / 5 - 20 > 120 ? 120 : $(window).height() / 5 - 20) + "px";
            layer.open({
                type: 2,
                title: '查看案件流转',
                area: openWindowWidth,
                offset: offsetH,
                fix: false, //不固定
                maxmin: true,
                content: "${ctx}/insuranceCaseController/getBSCaseAssessmentProcessReason?claimCaseId=" + claimCaseId,
                success: function (layero, index) {
                    layer.iframeAuto(index);
                }
            });
        }

        function getHistroyCaseInfo(baseUserId) {
            if (baseUserId == '') {
                layer.msg("暂无信息！！！", {icon: 2, time: 3000});
                return;
            }
            var openWindowWidth = $(document).width() * 0.8 + "px";
            var offsetH = ($(window).height() / 5 - 20 > 120 ? 120 : $(window).height() / 5 - 20) + "px";
            layer.open({
                type: 2,
                title: '查看历史案件',
                area: openWindowWidth,
                offset: offsetH,
                fix: false, //不固定
                maxmin: true,
                content: "${ctx}/insuranceCaseController/getHistroyCaseInfo?baseUserId=" + baseUserId,
                success: function (layero, index) {
                    layer.iframeAuto(index);
                }
            });
        }


        //生成uuid
        function guid() {
            return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
                var r = Math.random() * 16 | 0,
                    v = c == 'x' ? r : (r & 0x3 | 0x8);
                return v.toString(16);
            });
        }


        let check = function () {
            var _this = this;
            var val = _this.value;
            var valid = _this.getAttribute('data-valid').trim();
            valid = valid.split(" ");
            for (let check of valid) {
                if (!checkService[check](val)) {
                    $(_this).val("");
                    // 带遮罩的弹出
                    // layer.msg("输入格式不对", {icon: 2,time: 2000,shade: [0, 'rgba(0,0,0,0)']});
                    if (check == "isEmpty") {
                        layer.tips("值不能为空", $(_this), {time: 1500, shade: [0.0001, '#000']});
                    } else {
                        layer.tips("输入格式不对", $(_this), {time: 1500, shade: [0.0001, '#000']});
                    }
                    return;
                }
            }

            $(this).attr("title", val);

        }
        //校验规则
        const checkService = {
            // 不校验
            none: function () {
                return true;
            },

            //非空校验
            isEmpty: function (str) {
                let result = true;

                if (str == null || str == "") {
                    result = false;
                }
                if (str == "0") {
                    result = true;
                }

                return result;
            },

            // 只能输入数字[0-9]
            isDigits: function (str) {
                if (str == null || str == "") return true;
                var reg = /^\d+$/;
                return reg.test(str);
            },
            //百分比0-100
            isRate: function(str){
                if (str == null || str == "") return true;
                var reg = /^\d+$/;
                let result = false;
                if(reg.test(str) && (str>=0 && str <=100)){
                    result = true;
                }
                return result;
            },

            // 匹配english
            isEnglish: function (str) {
                if (str == null || str == "") return true;
                var reg = /^[A-Za-z]+$/;
                return reg.test(str);
            },

            // 匹配integer(包含正负)
            isInteger: function (str) {
                if (str == null || str == "") return true;
                var reg = /^[-\+]?\d+$/;
                return reg.test(str);
            },

            // 匹配汉字
            isChinese: function (str) {
                if (str == null || str == "") return true;
                var reg = /^[\u4e00-\u9fa5]+$/;
                return reg.test(str);
            },

            // 匹配中文(双字节字符,包括汉字和符号)
            isChineseChar: function (str) {
                if (str == null || str == "") return true;
                var reg = /^[\u0391-\uFFE5]+$/;
                return reg.test(str);
            },

            //匹配中英文
            isChineseAndEnglish: function (str) {
                if (str == null || str == "") return true;
                var reg = /^[\u4e00-\u9fa5a-zA-Z]+$/;
                return reg.test(str);
            },

            // 匹配URL
            isUrl: function (str) {
                if (str == null || str == "") return true;
                var reg = /^http:\/\/[A-Za-z0-9]+\.[A-Za-z0-9]+[\/=\?%\-&_~`@[\]\’:+!]*([^<>\"])*$/;
                return reg.test(str);
            },

            // 字符验证，只能包含中文、英文、数字、下划线、空格。
            stringCheck: function (str) {
                if (str == null || str == "") return true;
                var reg = /^[a-zA-Z0-9\u4e00-\u9fa5_ ,.，。]+$/;
                return reg.test(str);
            },

            //字符长度校验（最长64位）
            stringLengthCheck: function (str, length) {
                if (str == null || str == "") return true;
                length = length || 64;
                if (str.length > length) return false;
                return true;
            },
            //IP格式验证
            isIP: function (str) {
                if (str == null || str == "") return true;
                var reg = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;
                return reg.test(str);
            },
            //YYYYMMDD格式验证
            isTime: function (str) {
                if (str == null || str == "") return true;
                var reg = /^((([0-9]{3}[1-9]|[0-9]{2}[1-9][0-9]{1}|[0-9]{1}[1-9][0-9]{2}|[1-9][0-9]{3})(((0[13578]|1[02])(0[1-9]|[12][0-9]|3[01]))|((0[469]|11)(0[1-9]|[12][0-9]|30))|(02(0[1-9]|[1][0-9]|2[0-8]))))|((([0-9]{2})(0[48]|[2468][048]|[13579][26])|((0[48]|[2468][048]|[3579][26])00))0229))$/;
                return reg.test(str);
            },
            //只能输入数字和小数点
            isNumberAndDecimalPoint: function (str) {
                if (str == null || str == "") return true;
                var reg = /^\d+(\.\d{1,2})?$/;
                return reg.test(str);
            },
            //只能输入数字和小数点
            isNumberAndDecimalPointNotDefault: function (str) {
                if (str == null || str == "") return true;
                var reg = /^\d+(\.\d{1,2})?$/;
                return reg.test(str);
            },
            //select2--->是否
            isTrueFalse: function (str) {

                return true;
            },
            //伤残等级
            isDisabilityLevel: function () {
                return true;
            },
            //伤残等级
            isDeadSurplusQuota: function () {
                return true;
            }
        };

        // 组装数据
        function assembleData(status) {

            freshLossAssessment();  // 刷新估损总金额

            $("button[name='actionBtn']").attr('disabled','1');

            let objectName = $("#objectName").val().trim();
            let lossAssessmentSum = $("#lossAssessmentSum").attr("sumMoney");
            let nuclearLossSum = $("#nuclearLossSum").attr("sumMoney");

            let residualValue = $("#residualValue").val();
            let residualNuclearLossValue = $("#residualNuclearLossValue").val();

            let category = "${claimCaseObject.category}";

            let isError = false;
            let errorMsg = "";

            /*// 必填校验
            $("input[data-valid*='isEmpty']").not("[readonly]").each(function() {
                let val = $(this).val();
                if (!checkService["isEmpty"](val)) {
                    $(this).val("");
                    isError = true;
                    layer.tips("值不能为空", $(this), {time: 1500, shade: [0.0001, '#000']});
                    return false;
                }
            });
            if (isError) {
                $("button[name='actionBtn']").removeAttr("disabled");
                return;
            }*/

            if (!objectName) {
                isError = true;
                errorMsg += "估损清单名称不能为空！</br>";
            }


            let objectAssessmentList = [];
            $.each($("div[name='dataCollectAreaSubjet']"), function (index, obj) {
                let code = $(this).attr("code");
                $.each($(this).find("div[name='dataCollectAreaRow']"), function (index, obj) {
                    let id = $(this).find("input[name='id']").val().trim();
                    let name = $(this).find("input[name='name']").val().trim();
                    let lossAssessment = $(this).find("input[name='lossAssessment']").val().trim();
                    let approvedAmount = $(this).find("input[name='approvedAmount']").val().trim();
                    objectAssessmentList.push({"code": code, "id": id,"name": name, "appeal": lossAssessment, "lossAssessment": lossAssessment, "approvedAmount": approvedAmount});
                });
            });

            for (let assessment of objectAssessmentList) {
                if (!checkService["isEmpty"](assessment.name)) {
                    errorMsg += "维修名称不能为空！</br>";
                    isError = true;
                    break;
                }
                if (!checkService["isEmpty"](assessment.lossAssessment)) {
                    errorMsg += "维修估损金额不能为空！</br>";
                    isError = true;
                    break;
                }
            }

            if (!residualValue && category == 3) {
                isError = true;
                errorMsg += "残值不能为空！</br>";
            }

            let object = {
                id: "${claimCaseObject.id}",
                claimCaseId: "${claimCaseObject.claimCaseId}",
                claimCaseNo: "${claimCaseObject.claimCaseNo}",
                name: objectName,
                type: "${claimCaseObject.type}",
                category: "${claimCaseObject.category}",
                claimCaseObjectAssessmentList: objectAssessmentList,
                lossAssessmentSum: lossAssessmentSum,
                nuclearLossSum: nuclearLossSum,
                residualValue: residualValue,
                residualNuclearLossValue: residualNuclearLossValue,
                status: status,
                remark: $("#objectRemark").val().trim()
            };

            <#if claimCaseObject.category == 3>
            let carNumber = $("#carNumber").val().trim();
            if (!carNumber) {
                isError = true;
                errorMsg += "车牌号不能为空！</br>";
            }
            let carModel = $("#carModel").val().trim();
            let carEncoding = $("#carEncoding").val().trim();

            let firstRegistrationTime = $("#firstRegistrationTime").val().trim();
            if (firstRegistrationTime != undefined && firstRegistrationTime.trim() != "") {
                let firstRegistrationTimeStr = firstRegistrationTime.replace(/^(\d{4})(\d{2})(\d{2})$/, "$1-$2-$3");
                firstRegistrationTime = new Date(firstRegistrationTimeStr+" 00:00:00").getTime();
                console.log(firstRegistrationTime);
            }

            let treatName = $("#treatName").val().trim();

            let lossAssessmentTime = $("#lossAssessmentTime").val().trim();
            if (lossAssessmentTime != undefined && lossAssessmentTime.trim() != "") {
                let lossAssessmentTimeStr = lossAssessmentTime.replace(/^(\d{4})(\d{2})(\d{2})$/, "$1-$2-$3");
                lossAssessmentTime = new Date(lossAssessmentTimeStr+" 00:00:00").getTime();
                console.log(lossAssessmentTime);
            }

            let repairFactory = $("#repairFactory").val().trim();
            let isServiceShop = $("#isServiceShop").val().trim();

            object.carNumber = carNumber;
            object.carModel = carModel;
            object.carEncoding = carEncoding;
            object.firstRegistrationTime = firstRegistrationTime;
            object.treatName = treatName;
            object.lossAssessmentTime = lossAssessmentTime;
            object.repairFactory = repairFactory;
            object.isServiceShop = isServiceShop;
            </#if>

            if (status.startsWith("BAX2") && status != "BAX21" && status != "BAX22") {
                for (let assessment of objectAssessmentList) {
                    if (!checkService["isEmpty"](assessment.approvedAmount)) {
                        errorMsg += "各项核损金额不能为空！</br>";
                        isError = true;
                        break;
                    }
                }
                if (!checkService["isEmpty"](residualNuclearLossValue) && category == 3) {
                    errorMsg += "残值核损金额不能为空！</br>";
                    isError = true;
                }
            }

            if (status.startsWith("BAX3")) {
                let accidentLiability = $("#accidentLiability").val();
                let dutyRate = $("#dutyRate").val();
                let deductFee = $("#deductFee").val();
                let verifyAmout = $("#verifyAmout").val();
                let bankAccount = $("#bankAccount").val();
                let bankCard = $("#bankCard").val();
                let bankName = $("#bankName").val();
                let bankInfoId = $("#bankInfoId").val();
                object["accidentLiability"] = accidentLiability;
                object["dutyRate"] = dutyRate;
                object["deductFee"] = deductFee;
                object["verifyAmout"] = verifyAmout;
                object["verifyDetail"] = $("#verifyDetail").val();
                object["bankAccount"] = bankAccount;
                object["bankCard"] = bankCard;
                object["bankName"] = bankName;
                object["bankInfoId"] = bankInfoId;
                if (!accidentLiability) {
                    errorMsg += "责任不能为空！</br>";
                    isError = true;
                }
                if (!dutyRate) {
                    errorMsg += "赔付比例不能为空！</br>";
                    isError = true;
                }
                if (!deductFee) {
                    errorMsg += "扣减费用不能为空！</br>";
                    isError = true;
                }
                if (!verifyAmout) {
                    errorMsg += "理算金额不能为空！</br>";
                    isError = true;
                }
                if (verifyAmout && eval(verifyAmout) > eval(nuclearLossSum)) {
                    errorMsg += "理算金额不能大于核损总金额！</br>";
                    isError = true;
                }
                if (!bankAccount) {
                    errorMsg += "开户名不能为空！</br>";
                    isError = true;
                }
                if (!bankCard) {
                    errorMsg += "银行账号不能为空！</br>";
                    isError = true;
                }
                if (!bankName) {
                    errorMsg += "银行名称不能为空！</br>";
                    isError = true;
                }
                if (!bankInfoId) {
                    errorMsg += "银行支行不能为空！</br>";
                    isError = true;
                }
            }

            if(isError){
                layer.msg(errorMsg, {icon: 2, time: 3000},function () {
                    $("button[name='actionBtn']").removeAttr("disabled");
                });
                return;
            }

            console.log(JSON.stringify(object));

            return object;
        }

        // 保存 / 提审
        function submitData(status) {
            let claimCaseObject = assembleData(status);
            $("button[name='actionBtn']").removeAttr("disabled");
            if (claimCaseObject) {
                $.ajax({
                    url: "${ctx}/claimCaseObjectV2Controller/claimCaseObjectSubmit",
                    type: 'POST',
                    data: JSON.stringify(claimCaseObject),
                    async: true,
                    cache: false,
                    contentType: false,
                    processData: false,
                    success: function (data) {
                        var result = eval("(" + data + ")");
                        if (result.ret == "0") {
                            layer.msg(result.msg, {
                                icon: 1,
                                time: 2000,
                                shade: [0.0001, '#000']
                            }, function () {
                                window.location.href = "${ctx}/claimCaseObjectController/claimCaseObjectList?comeFrom=${comeFrom}&status=0";
                            });
                        } else {
                            layer.msg(result.msg, {
                                icon: 2,
                                time: 2000,
                                shade: [0.0001, '#000']
                            });
                        }

                    },
                    error: function (data) {
                        var result = eval("(" + data + ")");
                        alert(result.msg);
                    }
                })
            }
        }

        // 审核通过
        function auditPass(status) {
            let claimCaseObject = assembleData(status);

            var lossAssessmentSum = $("#lossAssessmentSum").attr("sumMoney");

            var nuclearLossSum = $("#nuclearLossSum").attr("sumMoney");

            if (lossAssessmentSum != nuclearLossSum) {
                layer.msg("估损总金额不等于核损总金额！", {icon: 2, time: 3000},function () {
                    $("button[name='actionBtn']").removeAttr("disabled");
                });
                return;
            }

            if (claimCaseObject) {
                $.ajax({
                    url: "${ctx}/claimCaseObjectController/auditPass",
                    type: 'POST',
                    data: JSON.stringify(claimCaseObject),
                    async: true,
                    cache: false,
                    contentType: false,
                    processData: false,
                    success: function (data) {
                        var result = eval("(" + data + ")");
                        if (result.ret == "0") {
                            layer.msg(result.msg, {
                                icon: 1,
                                time: 2000,
                                shade: [0.0001, '#000']
                            }, function () {
                                var comeFrom = "${comeFrom}";
                                if (comeFrom == 2) {
                                    window.location.href = "${ctx}/claimCaseObjectController/claimCaseObjectList4BS?comeFrom=${comeFrom}&status=3";
                                }
                                if (comeFrom == 3) {
                                    window.location.href = "${ctx}/claimCaseObjectController/claimCaseObjectList4BS?comeFrom=${comeFrom}&status=5";
                                }
                            });
                        } else {
                            layer.msg(result.msg, {
                                icon: 2,
                                time: 2000,
                                shade: [0.0001, '#000']
                            },function(){
                                $("button[name='actionBtn']").removeAttr("disabled");
                            });
                        }

                    },
                    error: function (data) {
                        var result = eval("(" + data + ")");
                        alert(result.msg);
                    }
                });
            }
        }

        var layerTop = top.layer;

        // 审核驳回
        function auditReject(status) {
            let claimCaseObject = assembleData(status);
            if (claimCaseObject) {
                layer.prompt({
                    formType: 2,        //0（文本）默认1（密码）2（文本域）
                    title: '请输入驳回原因',
                    fixed: false,
                    area: ['800px', '480px'],
                    closeBtn: 1,
                    yes: function (index, layero) {
                        let value = layero.find(".layui-layer-input").val().trim();
                        if (!value) {
                            layerTop.msg("驳回原因不能为空", {
                                icon: 2,
                                time: 2000
                            });
                            return false;
                        }
                        claimCaseObject["reason"] = value;
                        console.log(claimCaseObject, "对象数据");
                        $.ajax({
                            url: "${ctx}/claimCaseObjectController/auditReject",
                            type: 'POST',
                            data: JSON.stringify(claimCaseObject),
                            async: true,
                            cache: false,
                            contentType: false,
                            processData: false,
                            success: function (data) {
                                var result = eval("(" + data + ")");
                                if (result.ret == "0") {
                                    layer.msg(result.msg, {
                                        icon: 1,
                                        time: 2000,
                                        shade: [0.0001, '#000']
                                    }, function () {
                                        var comeFrom = "${comeFrom}";
                                        if (comeFrom == 2) {
                                            window.location.href = "${ctx}/claimCaseObjectController/claimCaseObjectList4BS?comeFrom=${comeFrom}&status=3";
                                        }
                                        if (comeFrom == 3) {
                                            window.location.href = "${ctx}/claimCaseObjectController/claimCaseObjectList4BS?comeFrom=${comeFrom}&status=5";
                                        }
                                    });
                                } else {
                                    layer.msg(result.msg, {
                                        icon: 2,
                                        time: 2000,
                                        shade: [0.0001, '#000']
                                    },function(){
                                        $("button[name='actionBtn']").removeAttr("disabled");
                                    });
                                }
                            },
                            error: function (data) {
                                var result = eval("(" + data + ")");
                                alert(result.msg);
                            }
                        });
                    },
                    end: function () {
                        $("button[name='actionBtn']").removeAttr("disabled");
                    }
                });
            }
        }
    </script>
</head>


<body id="qc-Body" style="overflow-x:hidden;background: #fff;">
<div class="container-fluid">
    <div class="row">
        <form id="inputForm" onsubmit="return false">
            <div class="row">
                <div class="col-sm-8">
                    <div class="col-sm-12" style="margin-bottom: 5px;padding-left: 0px;padding-right: 0px;">
                        <div class="col-sm-3" style="padding-left: 0px;">
                            案件号：${claimCase.claimCaseNo}
                        </div>
                        <div class="col-sm-3" style="padding-left: 0px;">
                            姓名：${claimCase.treatName}
                        </div>
                        <div class="col-sm-3" style="padding-left: 0px;">
                            是否延迟报立案：<#if claimCase.delayReport?? && claimCase.delayReport == 1>是<#else >否</#if>
                        </div>
                        <div class="col-sm-6 label-hide-overflow" style="padding-left: 0px;" title="${claimCase.productName}">
                            产品名称：${claimCase.productName}
                        </div>
                        <div class="col-sm-5" style="padding-left: 0px;">
                            起保时间：${(policyPerson.startDate?string["yyyy-MM-dd HH:mm:ss"])!''}
                        </div>
                        <div class="col-sm-12" style="padding-left: 0px;">
                            承保公司：
                            <#if claimCase.province == '辽宁省' && claimCase.treatDate?datetime gte "2023-09-29 00:00:00"?datetime>
                                <span style="display:inline-block;background-color: #067ee5;color: #fff;padding: 2px 6px;border-radius: 2px;">国元</span>
                            <#else >
                                <span style="display:inline-block;background-color: #26c281;color: #fff;padding: 2px 6px;border-radius: 2px;">大地</span>
                            </#if>
                        </div>
                    </div>
                    <#-- 按钮 -->
                    <div class="col-sm-12" style="margin-bottom: 5px;padding-left: 0px;padding-right: 0px;">

                        <#--<div class="col-sm-2" style="padding-left: 0px;">
                            <div class="col-sm-12" style="padding-left: 0px;">
                                <button type="button" class="btn genTask btn-look" onclick="seePlanName('${product.id!''}')">
                                    查看产品方案
                                </button>
                            </div>
                        </div>-->

                        <div class="col-sm-2" style="padding-left: 0px;">
                            <button type="button" onclick="getHistroyCaseInfo('${claimCase.baseUserId}')" class="btn genTask">
                                历史案件
                            </button>
                        </div>

                        <div class="col-sm-2">
                            <button type="button" class="btn genTask circulation_reasons" onclick="seeCaseProcessReason('${claimCase.id}')"> 查看案件流转原因</button>
                        </div>

                    </div>
                    <#if claimCaseObject.reason != "">
                    <div class="col-sm-12 clear-padding" style="color: red">
                        驳回原因：${claimCaseObject.reason} （最新一次驳回原因）
                    </div>
                    </#if>
                </div>
                <div class="col-sm-4" style="margin-bottom: 5px;padding-left: 0px;padding-right: 0px;">
                    <#if !isShow>
                    <div class="col-sm-12 pull-left" style="margin: 20px auto;display: flex;">
                        <#if comeFrom == 1>
                            <!--采集按钮-->
                            <#if !readonly>
                                <div class="col-sm-3 col-sm-offset-3">
                                    <button name="actionBtn" style="background-color: #0597FF;" class="btn btn-lg" onclick="submitData('BAX21')">保存</button>
                                </div>
                                <div class="col-sm-3">
                                    <button name="actionBtn" style="background-color: #F49929;" class="btn btn-lg" onclick="submitData('BAX22')">提审</button>
                                </div>
                            </#if>
                            <!--理算按钮-->
                            <#if !verifyReadonly>
                                <div class="col-sm-3 col-sm-offset-3">
                                    <button name="actionBtn" style="background-color: #0597FF;" class="btn btn-lg" onclick="submitData('BAX31')">保存</button>
                                </div>
                                <div class="col-sm-3">
                                    <button name="actionBtn" style="background-color: #F49929;" class="btn btn-lg" onclick="submitData('BAX32')">提审</button>
                                </div>
                            </#if>
                        </#if>
                        <#if comeFrom == 2>
                            <#if claimCaseObject.status == "BAX22">
                                <div class="col-sm-3 col-sm-offset-3">
                                    <button name="actionBtn" style="background-color: #0597FF;" class="btn btn-lg" onclick="auditPass('BAX23')">通过</button>
                                </div>
                                <div class="col-sm-3">
                                    <button name="actionBtn" style="background-color: #F49929;" class="btn btn-lg" onclick="auditReject('BAX24')">驳回</button>
                                </div>
                            </#if>
                            <#if claimCaseObject.status == "BAX32">
                                <div class="col-sm-3 col-sm-offset-3">
                                    <button name="actionBtn" style="background-color: #0597FF;" class="btn btn-lg" onclick="auditPass('BAX33')">通过</button>
                                </div>
                                <div class="col-sm-3">
                                    <button name="actionBtn" style="background-color: #F49929;" class="btn btn-lg" onclick="auditReject('BAX34')">驳回</button>
                                </div>
                            </#if>
                        </#if>
                        <#if comeFrom == 3>
                            <#if claimCaseObject.status == "BAX25">
                                <div class="col-sm-3 col-sm-offset-3">
                                    <button name="actionBtn" style="background-color: #0597FF;" class="btn btn-lg" onclick="auditPass('BAX26')">通过</button>
                                </div>
                                <div class="col-sm-3">
                                    <button name="actionBtn" style="background-color: #F49929;" class="btn btn-lg" onclick="auditReject('BAX27')">驳回</button>
                                </div>
                            </#if>
                            <#if claimCaseObject.status == "BAX35">
                                <div class="col-sm-3 col-sm-offset-3">
                                    <button name="actionBtn" style="background-color: #0597FF;" class="btn btn-lg" onclick="auditPass('BAX36')">通过</button>
                                </div>
                                <div class="col-sm-3">
                                    <button name="actionBtn" style="background-color: #F49929;" class="btn btn-lg" onclick="auditReject('BAX37')">驳回</button>
                                </div>
                            </#if>
                        </#if>
                        <#--   <div class="col-sm-4">
                               <button style="background-color: #009F9F;color: white" class="exportBtn btn btn-lg">导出</button>
                           </div>-->

                    </div>
                    </#if>
                </div>
            </div>
            <!-- 左部分 -->
            <div class="col-sm-8" style="background: none!important;padding-right: 0px !important;">
                <#--审核头-->
                <div class="row">
                    <#-- 展示图片区域以及采集数据tbale -->
                    <div class="col-sm-12" style="padding: 5px 0px 0px 0px; background: none;">
                        <div class="row" style="height: 1050px; overflow: hidden;">
                            <div class="col-sm-1" style="height: 1050px;background-color: rgba(226, 226, 226, 0.5);padding-left: 0px!important;padding-right: 10px!important; border-right: groove;">
                                <#if claimAttachList?exists && (claimAttachList?size>0)>
                                    <div id="thumbnail" style="height: 1050px;<#if (claimAttachList?size>11)>overflow-y: scroll;</#if>overflow-x:hidden">
                                        <ol style="padding-left:25px !important;/*padding-right: 15px !important;*/">
                                            <#list claimAttachList as attach>
                                                <li style="width:auto; height:auto; cursor:pointer;text-align: center;padding-top:5px">
                                                    <img id="thumbnail-${attach.id}" class="thumbnail-img <#if attach_index == 0>selected-thumbnail-img</#if>"
                                                         data-fileid="${attach.id}" data-img-number=''
                                                         title="${attach.fileName}"
                                                         src="${attach.fileObjectId}" onerror="javascript:this.src='/a/job_done.png'"/>
                                                </li>
                                            </#list>
                                        </ol>
                                    </div>
                                </#if>
                            </div>
                            <div class="col-sm-11" style="height: 1300px;">
                                <ul id="images">
                                    <#list claimAttachList as attach>
                                        <li hidden="hidden">
                                            <img data-fileid="${attach.id}"
                                                 title="${attach.fileName}"
                                                 src="${attach.fileObjectId}"
                                                 alt="${attach.id}"/>
                                        </li>
                                    </#list>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 右部分 -->
            <div class="col-sm-4" style="padding-left: 15px;">
                <div class="row" style="font-size: 15px;margin: 9px 0px;display: flex;align-items: center;">
                    <div class="col-sm-3 clear-padding" style="text-align: right">估损清单名称</div>
                    <div class="col-sm-9 ">
                        <input id="objectName" type="text" class="form-control" title="${claimCaseObject.name!''}" value="${claimCaseObject.name!''}" <#if readonly?? >readonly</#if>>
                    </div>
                </div>
                <#if claimCaseObject.category == 3>
                <div class="row" style="margin-bottom: 20px">
                    <div style="margin-left: 5px;">
                        <img class="marginLeft15" src="${ctx}/images/u48.svg" align="left" style="margin-top: 3px;"/>
                        <span style="font-size: 16px;font-weight: bold;margin-left: 7px">机动车基本信息 </span>
                    </div>
                    <div name="dataCollectArea" class="row">
                        <div class="col-sm-12 clear-padding">
                            <div class="col-sm-6 clear-padding align-item-center" style="margin: 7px 0px;">
                                <div class="col-sm-4 clear-padding label-right">车牌号码</div>
                                <div class="col-sm-8 ">
                                    <input type="text" validName="billCollection" id="carNumber" data-valid="none" name="carNumber" title="${claimCaseObject.carNumber!''}" value="${claimCaseObject.carNumber!''}" class="form-control" <#if readonly?? >readonly</#if> />
                                </div>
                            </div>
                            <div class="col-sm-6 clear-padding align-item-center" style="margin: 7px 0px;">
                                <div class="col-sm-4 clear-padding label-right" >厂牌型号</div>
                                <div class="col-sm-8 ">
                                    <input type="text" validName="billCollection" id="carModel" data-valid="none" name="carModel" title="${claimCaseObject.carModel!''}" value="${claimCaseObject.carModel!''}" class="form-control" <#if readonly?? >readonly</#if> />
                                </div>
                            </div>
                            <div class="col-sm-6 clear-padding align-item-center" style="margin: 7px 0px;">
                                <div class="col-sm-4 clear-padding label-right" >车辆识别代码</div>
                                <div class="col-sm-8 ">
                                    <input type="text" validName="billCollection" id="carEncoding" data-valid="none" name="carEncoding" title="${claimCaseObject.carEncoding!''}" value="${claimCaseObject.carEncoding!''}" class="form-control" <#if readonly?? >readonly</#if> />
                                </div>
                            </div>
                            <div class="col-sm-6 clear-padding align-item-center" style="margin: 7px 0px;">
                                <div class="col-sm-4 clear-padding label-right">初次登记日期</div>
                                <div class="col-sm-8 ">
                                    <input type="text" validName="billCollection" id="firstRegistrationTime" data-valid="isTime" name="firstRegistrationTime" title="${(claimCaseObject.firstRegistrationTime?string('yyyyMMdd'))!''}" value="${(claimCaseObject.firstRegistrationTime?string('yyyyMMdd'))!''}" class="form-control" <#if readonly?? >readonly</#if> />
                                </div>
                            </div>
                            <div class="col-sm-6 clear-padding align-item-center" style="margin: 7px 0px;">
                                <div class="col-sm-4 clear-padding label-right">行驶证车主</div>
                                <div class="col-sm-8 ">
                                    <input type="text" validName="billCollection" id="treatName" name="treatName" data-valid="none" title="${claimCaseObject.treatName!''}" value="${claimCaseObject.treatName!''}" class="form-control" <#if readonly?? >readonly</#if> />
                                </div>
                            </div>
                            <div class="col-sm-6 clear-padding align-item-center" style="margin: 7px 0px;">
                                <div class="col-sm-4 clear-padding label-right" >定损时间</div>
                                <div class="col-sm-8 ">
                                    <input type="text" validName="billCollection" data-valid="isTime" id="lossAssessmentTime" name="lossAssessmentTime" title="${(claimCaseObject.lossAssessmentTime?string('yyyyMMdd'))!''}" value="${(claimCaseObject.lossAssessmentTime?string('yyyyMMdd'))!''}" class="form-control" <#if readonly?? >readonly</#if> />
                                </div>
                            </div>
                            <div class="col-sm-6 clear-padding align-item-center" style="margin: 7px 0px;">
                                <div class="col-sm-4 clear-padding label-right" >修理厂</div>
                                <div class="col-sm-8 ">
                                    <input type="text" validName="billCollection" id="repairFactory" data-valid="none" name="repairFactory" title="${claimCaseObject.repairFactory!''}" value="${claimCaseObject.repairFactory!''}" class="form-control" <#if readonly?? >readonly</#if> />
                                </div>
                            </div>
                            <div class="col-sm-6 clear-padding align-item-center" style="margin: 7px 0px;">
                                <div class="col-sm-4 clear-padding label-right">是否4S</div>
                                <div class="col-sm-8 ">
                                    <select validName="billCollection" class="kp-select2 inputStyle form-control" data-valid="none" id="isServiceShop" name="isServiceShop" <#if readonly?? >disabled</#if>>
                                        <option value="是" <#if claimCaseObject.isServiceShop='是' >selected</#if>>是</option>
                                        <option value="否" <#if claimCaseObject.isServiceShop='否' >selected</#if>>否</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                </#if>

                <div class="row" style="">
                    <div style="margin-left: 5px;">
                        <img class="marginLeft15" src="${ctx}/images/u48.svg" align="left" style="margin-top: 3px;"/>
                        <span style="font-size: 16px;font-weight: bold;margin-left: 7px">维修定损 </span>
                    </div>
                    <div name="dataCollectArea" class="row" style="height:700px;overflow-y: scroll;overflow-x: hidden;">
                        <#assign pjIndex = 0>
                        <#assign rgIndex = 0>
                        <#if enumMapByParentCode?exists>
                            <#list enumMapByParentCode.keySet() as code>
                                <div class="row" name="dataCollectAreaSubjet" code="${code}" name="${enumMapByParentCode.get(code)}">
                                    <div class="col-sm-8" style="margin: 15px;">
                                        <div class="subject-name">${enumMapByParentCode.get(code)}</div>
                                    </div>
                                    <div class="col-sm-12 ">
                                        <div class="col-sm-1 clear-padding" style="font-size: 15px;margin: 9px 0px;">

                                        </div>
                                        <div class="col-sm-4 clear-padding align-item-center">
                                            <div class="col-sm-3 clear-padding" >名称</div>
                                            <div class="col-sm-9 clear-padding-left">
                                                <input type="text" style="padding: 0px !important;" class="form-control" disabled/>
                                            </div>
                                        </div>
                                        <#--<div class="col-sm-3 clear-padding">
                                            <div class="col-sm-4 clear-padding" style="font-size: 15px;display: inline-block;text-align: center;line-height: 15px">维修报价</div>
                                            <div class="col-sm-8 ">
                                                <input type="text" style="padding: 0px !important;" class="form-control" disabled/>
                                            </div>
                                        </div>-->
                                        <div class="col-sm-3 clear-padding align-item-center">
                                            <div class="col-sm-4 clear-padding">估损</div>
                                            <div class="col-sm-8 clear-padding-left">
                                                <input type="text" style="padding: 0px !important;" class="form-control" disabled/>
                                            </div>
                                        </div>

                                        <div class="col-sm-3 clear-padding align-item-center">
                                            <div class="col-sm-4 clear-padding" >核损</div>
                                            <div class="col-sm-8 clear-padding-left">
                                                <input type="text" style="padding: 0px !important;" class="form-control" disabled/>
                                            </div>
                                        </div>
                                        <#if !readonly>
                                        <div class="col-sm-1 clear-padding">
                                            <div class="icon-plus" id="${code}AddDataRow" onclick="addDataRowClick('${code}')"></div>
                                        </div>
                                        </#if>
                                    </div>
                                    <div class="row" id="${code}collectArea">
                                        <#list objectAssessmentList as assessment>
                                            <#if assessment.code == code>
                                            <div class="col-sm-12 align-item-center" style="margin-top: 10px" name="dataCollectAreaRow">
                                            <input type="hidden" name="id" class="form-control" value="${assessment.id}">
                                            <div class="col-sm-1 clear-padding" style="text-align: left;">
                                                 <div class="clear-padding line-center">
                                                     <#switch code>
                                                         <#case "2-1">
                                                             <#assign pjIndex = pjIndex + 1>
                                                             ${pjIndex}
                                                             <#break >
                                                         <#case "2-2">
                                                             <#assign rgIndex = rgIndex + 1>
                                                             ${rgIndex}
                                                             <#break >
                                                     </#switch>
                                                 </div>
                                            </div>
                                            <div class="col-sm-4 clear-padding align-item-center">
                                                <div class="col-sm-3 clear-padding">名称</div>
                                                <div class="col-sm-9 clear-padding-left">
                                                    <input type="text" validname="billCollection" data-valid="isEmpty" name="name" class="form-control clear-padding" value="${assessment.name!''}" title="${assessment.name!''}" <#if readonly?? >readonly</#if>>
                                                </div>
                                            </div>
                                            <div class="col-sm-3 clear-padding align-item-center">
                                                <div class="col-sm-4 clear-padding">估损</div>
                                                <div class="col-sm-8 clear-padding-left">
                                                    <input type="text"  validname="billCollection" name="lossAssessment" data-valid="isNumberAndDecimalPoint isEmpty" class="form-control clear-padding" value="${assessment.lossAssessment!''}" title="${assessment.lossAssessment!''}" <#if readonly?? >readonly</#if>>
                                                </div>
                                            </div>
                                            <div class="col-sm-3 clear-padding align-item-center">
                                                <div class="col-sm-4 clear-padding">核损</div>
                                                <div class="col-sm-8 clear-padding-left">
                                                    <input type="text" validname="billCollection" name="approvedAmount" data-valid="isNumberAndDecimalPoint isEmpty" class="form-control clear-padding" value="${assessment.approvedAmount!''}" title="${assessment.approvedAmount!''}" <#if  claimCaseObject.status != "BAX22" && claimCaseObject.status != "BAX25">readonly</#if>>
                                                </div>
                                            </div>
                                            <#if !readonly>
                                            <div class="col-sm-1 clear-padding">
                                                        <img src="/images/shanchu.svg" class="dataDisplayArea-head-img" onclick="delDataRow(this,'${assessment.code}')">
                                            </div>
                                            </#if>
                                        </div>
                                            </#if>
                                        </#list>
                                    </div>
                                    <div class="row">
                                        <div class="col-sm-12" style="margin: 15px;">
                                            <span style="font-size: 15px;float:right;color: red;font-weight: bold" id="${code}LossAssessment"></span>
                                        </div>
                                    </div>
                                </div>
                            </#list>
                        </#if>
                        <div class="row">
                            <div style="margin: 15px;display: flex;align-items: center;">
                                <div class="col-sm-3 clear-padding" style="font-size: 17px;font-weight: bold;">估损描述</div>
                                <div class="col-sm-9 clear-padding-left">
                                    <input validName="billCollection" type="text" id="objectRemark" name="objectRemark" class="form-control" title="${claimCaseObject.remark!''}" value="${claimCaseObject.remark!''}"  data-valid="none" <#if readonly?? >readonly</#if>>
                                </div>
                            </div>
                            <#if claimCaseObject.category == 3 >
                            <div style="margin: 15px;display: flex;align-items: center;">
                                <div class="col-sm-3 clear-padding" style="font-size: 17px;font-weight: bold;">残值</div>
                                <div class="col-sm-9 clear-padding-left">
                                    <input validName="billCollection" id="residualValue" name="residualValue" type="text" class="form-control" value="${claimCaseObject.residualValue!''}" title="${claimCaseObject.residualValue!''}" data-valid="isNumberAndDecimalPoint isEmpty" <#if readonly??>readonly</#if>/>
                                </div>
                            </div>
                            <div style="margin: 15px;display: flex;align-items: center;">
                                <div class="col-sm-3 clear-padding" style="font-size: 17px;font-weight: bold;">残值核损金额</div>
                                <div class="col-sm-9 clear-padding-left">
                                    <input validName="billCollection" id="residualNuclearLossValue" name="residualNuclearLossValue" type="text" class="form-control" value="${claimCaseObject.residualNuclearLossValue!''}" title="${claimCaseObject.residualNuclearLossValue!''}" data-valid="isNumberAndDecimalPoint isEmpty" <#if claimCaseObject.status != "BAX22" && claimCaseObject.status != "BAX25">readonly</#if> />
                                </div>
                            </div>
                            </#if>
                            <div style="margin: 15px;display: flex;align-items: center;">
                                <span style="font-size: 17px;color: red;font-weight: bold" id="lossAssessmentSum" sumMoney="${(claimCaseObject.lossAssessmentSum?string('0.00'))!''}">维修估损总金额：${(claimCaseObject.lossAssessmentSum?string('0.00'))!''} 元</span>
                            </div>
                            <div style="margin: 15px;display: flex;align-items: center;">
                                <span style="font-size: 17px;color: red;font-weight: bold" id="nuclearLossSum" sumMoney="${(claimCaseObject.nuclearLossSum?string('0.00'))!''}">核损合计：${(claimCaseObject.nuclearLossSum?string('0.00'))!''} 元</span>
                            </div>
                        </div>

                        <!--理算部分-->
                        <#if claimCaseObject.status?starts_with("BAX3") || claimCaseObject.status == "BAX99">
                            <div>
                                <div class="row">
                                    <div style="margin: 15px;display: flex;align-items: center;">
                                        <div class="col-sm-3 clear-padding" style="font-size: 17px;font-weight: bold;">责任比例</div>
                                        <div class="col-sm-4 clear-padding-left">
                                            <select type="text" class="form-control" id="accidentLiability" name="accidentLiability" disabled >
                                                <option value="全责" <#if claimCaseObject.accidentLiability == "全责">selected</#if> data-dutyRate="100" >全责</option>
                                                <option value="主责" <#if claimCaseObject.accidentLiability == "主责">selected</#if> data-dutyRate="70" >主责</option>
                                                <option value="同责" <#if claimCaseObject.accidentLiability == "同责">selected</#if> data-dutyRate="50" >同责</option>
                                                <option value="次责" <#if claimCaseObject.accidentLiability == "次责">selected</#if> data-dutyRate="30" >次责</option>
                                                <option value="无责" <#if claimCaseObject.accidentLiability == "无责">selected</#if> data-dutyRate="0" >无责</option>
                                                <option value="责任待确认" <#if claimCaseObject.accidentLiability == "责任待确认">selected</#if> data-dutyRate="" >责任待确认</option>
                                            </select>
                                        </div>
                                        <div class="col-sm-4 clear-padding-left">
                                            <input validName="billCollection" placeholder="赔付比例" title="赔付比例" id="dutyRate" type="text" class="form-control" value="${claimCaseObject.dutyRate!claimCaseObject.accidentProportion}" data-valid="isRate isEmpty" <#if verifyReadonly??>readonly</#if>/>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div style="margin: 15px;display: flex;align-items: center;">
                                        <div class="col-sm-3 clear-padding" style="font-size: 17px;font-weight: bold;">扣减费用</div>
                                        <div class="col-sm-9 clear-padding-left">
                                            <input validName="billCollection" id="deductFee" type="text" class="form-control" value="${claimCaseObject.deductFee!''}" title="${claimCaseObject.deductFee!''}" data-valid="isNumberAndDecimalPoint isEmpty" <#if verifyReadonly??>readonly</#if>/>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div style="margin: 15px;display: flex;align-items: center;">
                                        <div class="col-sm-3 clear-padding" style="font-size: 17px;font-weight: bold;">理算金额</div>
                                        <div class="col-sm-9 clear-padding-left">
                                            <input validName="billCollection" id="verifyAmout" type="text" class="form-control" value="${claimCaseObject.verifyAmout!''}" title="${claimCaseObject.verifyAmout!''}" data-valid="isNumberAndDecimalPoint isEmpty" <#if verifyReadonly??>readonly</#if>/>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div style="margin: 15px;display: flex;align-items: center;">
                                        <div class="col-sm-3 clear-padding" style="font-size: 17px;font-weight: bold;">理算描述</div>
                                        <div class="col-sm-9 clear-padding-left">
                                            <input validName="billCollection" id="verifyDetail" type="text" class="form-control" value="${claimCaseObject.verifyDetail!''}" title="${claimCaseObject.verifyDetail!''}" data-valid="none" <#if verifyReadonly??>readonly</#if>/>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div style="margin: 15px;display: flex;align-items: center;">
                                        <div class="col-sm-3 clear-padding" style="font-size: 17px;font-weight: bold;">开户名</div>
                                        <div class="col-sm-9 clear-padding-left">
                                            <input validName="billCollection" id="bankAccount" type="text" class="form-control" value="${claimCaseObject.bankAccount!''}" title="${claimCaseObject.bankAccount!''}" data-valid="isEmpty" <#if verifyReadonly??>readonly</#if>/>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div style="margin: 15px;display: flex;align-items: center;">
                                        <div class="col-sm-3 clear-padding" style="font-size: 17px;font-weight: bold;">银行账号</div>
                                        <div class="col-sm-9 clear-padding-left">
                                            <input validName="billCollection" id="bankCard" type="text" class="form-control" value="${claimCaseObject.bankCard!''}" title="${claimCaseObject.bankCard!''}" data-valid="isEmpty" <#if verifyReadonly??>readonly</#if>/>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div style="margin: 15px;display: flex;align-items: center;">
                                        <div class="col-sm-3 clear-padding" style="font-size: 17px;font-weight: bold;">银行名称</div>
                                        <div class="col-sm-9 clear-padding-left">
                                            <select id="totalBankSelect" name="totalBankSelect" <#if verifyReadonly??>disabled</#if>
                                                    class="js-data-example-ajax">
                                            </select>
                                            <input id="bankName" name="bankName" type="hidden" value="${claimCaseObject.bankName}">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div style="margin: 15px;display: flex;align-items: center;">
                                        <div class="col-sm-3 clear-padding" style="font-size: 17px;font-weight: bold;">银行支行</div>
                                        <div class="col-sm-9 clear-padding-left">
                                            <select id="bankSubbranchSelect" name="bankSubbranchSelect" class="js-data-example-ajax" disabled>
                                            </select>
                                            <input id="bankInfoId" name="bankInfoId" type="hidden" value="${claimCaseObject.bankInfoId}">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </#if>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

</body>
</html>
