<!--[if IE 8]> <html lang="en" class="ie8 no-js"> <![endif]-->
<!--[if IE 9]> <html lang="en" class="ie9 no-js"> <![endif]-->
<!--[if !IE]><!-->
<html lang="en">
<!--<![endif]-->
<head>
<meta charset="utf-8" />
<title>编辑资源</title>
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta content="width=device-width, initial-scale=1" name="viewport" />
<meta content="" name="description" />
<meta content="" name="author" />
<#include "/common/cssResource.html"> <#include
"/common/jsResource.html">

<script type="text/javascript">
	$(document).ready(function() {

		$.validator.addMethod("linkRequired",function(value){
			var str = value.replace(/(^\s*)|(\s*$)/g, '');//去除空格;
			if ($("#type").val() != 0 && (str == '' || str == undefined || str == null)) {
				return false;
			}
			return true;
		},"这是必填字段");

		var form = $('#inputForm');
		form.validate({
			errorElement : 'span', //default input error message container
			errorClass : 'help-block help-block-error', // default input error message class
			focusInvalid : false, // do not focus the last invalid input
			rules : {
				name : {
					maxlength : 50,
					required : true,
					remote : {
				        url: "${ctx}/jqueryValidateController/checkResourceName?id=${resource.id}&platform=${platform}",
				        type: "post"
// 						data: {
// 							username: function() {
// 								return $("#username").val();
// 							}
// 						}
					}
				},
				type : {
					required : true
				},
				link : {
					linkRequired : true,
					maxlength : 100
				},
				idx : {
					maxlength : 10,
					digits : true,
					required : true
				},
				shiroPermission : {
					maxlength : 50
				}
			},
			messages: {
				name: {remote : "资源名称已存在"}
			},
	
			invalidHandler : function(event, validator) { //display error alert on form submit              
				App.scrollTo(form, 0);
			},
	
			highlight : function(element) { // hightlight error inputs
				$(element).closest('.form-group').removeClass(
						"has-success").addClass('has-error'); // set error class to the control group   
			},
	
			unhighlight : function(element) { // revert the change done by hightlight
	
			},
	
			success : function(label, element) {
				var icon = $(element).parent('.input-icon').children(
						'i');
				$(element).closest('.form-group').removeClass(
						'has-error').addClass('has-success'); // set success class to the control group
				icon.removeClass("fa-warning").addClass("fa-checkDifference18");
			},
	
			submitHandler : function(form) {
				var formData = new FormData($('#inputForm')[0]); 
				$.ajax({
					url: "${ctx}/resourceController/editResource",
					type: 'POST',  
					data: formData,  
					async: true,  
					cache: false,  
					contentType: false,  
					processData: false,  
					success: function (data) { 
						var result = eval("("+data+")");
						parent.window.location.reload();
					   	var index = parent.layer.getFrameIndex(window.name); //获取窗口索引
					   	parent.layer.close(index);
					},  
					error: function (data) {  
						var result = eval("("+data+")");
				    	alert(result.msg);
					}  
	            });
			}
		});
		
		if(${resource.type} == 0) {
			$("#menuParent").hide();
			$("#dirParent").hide();
			$("#linkDiv label span").hide();
		}
		if(${resource.type} == 1) {
			if('${platform}' == "1") {
				$("#menuParent").hide();
				$("#dirParent").hide();
			}else {
				$("#menuParent").hide();
			}
		}
		if(${resource.type} == 2) {
			if('${platform}' == "1") {
				$("#menuParent").hide();
				$("#dirParent").show();
				$("#linkDiv").hide();
			}else {
				$("#dirParent").hide();
				$("#linkDiv").hide();
			}
		}
	});
	
	function typeSelector(selector){
		var typeSelect = $(selector).find("option:selected").val();
		if (typeSelect == "") {
			$("#dirParent").hide();
			$("#menuParent").hide();
			$("#parentId").val("");
			$("#linkDiv").hide();
			$("#link").val("");
		} else if (typeSelect == "0") {
			$("#dirParent").hide();
			$("#menuParent").hide();
			$("#parentId").val("");
			$("#linkDiv").show();
			$("#link").val("");
			$("#linkDiv label span").hide();
		} else if (typeSelect == "1") {
			<#--if('${platform}' == "1") {-->
			<#--	$("#dirParent").hide();-->
			<#--	$("#menuParent").hide();-->
			<#--	$("#parentId").val("");-->
			<#--	$("#linkDiv").show();-->
			<#--	$("#linkDiv label span").show();-->
			<#--}else {-->
				$("#dirParent").show();
				$("#menuParent").hide();
				setParentId($("#dirParentSelector"));
				$("#linkDiv").show();
				$("#linkDiv label span").show();
			// }
		} else if (typeSelect == "2") {
			//根据是企业端还是管理端来判断，选择权限时，父级资源应该显示什么
			<#--if('${platform}' == "1") {-->
			<#--	$("#dirParent").show();-->
			<#--	$("#menuParent").hide();-->
			<#--	setParentId($("#dirParentSelector"));-->
			<#--	$("#linkDiv").hide();-->
			<#--	$("#link").val("");-->
			<#--}else {-->
				$("#dirParent").hide();
				$("#menuParent").show();
				setParentId($("#menuParentSelector"));
				$("#linkDiv").hide();
				$("#link").val("");
			// }
		}
	}

	function setParentId(selector) {
		$("#parentId").val($(selector).find("option:selected").val());
	}
	
</script>

</head>
<body>
	<div class="portlet light bordered">
		<div class="portlet-body form">
			<!-- BEGIN FORM-->
			<form id="inputForm" class="form-horizontal form-row-seperated">
				<input type="hidden" name="id" value="${resource.id}" />
				<input type="hidden" id="parentId" name="parentId" value="${resource.parentId}">
				<input type="hidden" name="platform" value="${platform}">
				<div class="form-body">
					<div class="form-group">
						<label class="control-label col-sm-3">资源名称 <span
							class="required"> * </span>
						</label>
						<div class="col-sm-9">
							<div class="input-icon right">
								<i class="fa"></i> <input type="text" class="form-control"
									name="name" value="${resource.name}" />
							</div>
						</div>
					</div>
					<div class="form-group">
						<label class="control-label col-sm-3">类型 <span
							class="required"> * </span>
						</label>
						<div class="col-sm-9">
							<div class="input-icon right">
								<select class="bs-select form-control" id="type" name="type" onchange="typeSelector(this);">
									<option value="">-请选择-</option>
									<option value="0" <#if resource.type == 0> selected </#if>>目录</option>
									<option value="1" <#if resource.type == 1> selected </#if>>菜单</option>
									<option value="2" <#if resource.type == 2> selected </#if>>权限</option>
								</select>
							</div>
						</div>
					</div>
					<div class="form-group" id="dirParent">
						<label class="control-label col-sm-3">父级资源 <span
							class="required"> * </span>
						</label>
						<div class="col-sm-9">
							<select class="bs-select form-control" id="dirParentSelector" onchange="setParentId(this);">
								<#list parentResourceList as pr>
									<#if pr.parentId == null>
										<option value="${pr.id}" <#if resource.parentId == pr.id> selected </#if>>${pr.name}</option>
									</#if>
								</#list>
							</select>
							</div>
						</div>
					</div>
					<div class="form-group" id="menuParent">
						<label class="control-label col-sm-3">父级资源 <span
							class="required"> * </span>
						</label>
						<div class="col-sm-9">
							<select class="bs-select form-control" id="menuParentSelector" onchange="setParentId(this);">
								<#list parentResourceList as pr>
									<#if pr.parentId == null>
										<#if pr_index != 0>
											</optgroup>
										</#if>
										<optgroup label="${pr.name}">
									<#else>
										<option value="${pr.id}" <#if resource.parentId == pr.id> selected </#if>>${pr.name}</option>
									</#if>
								</#list>
								</optgroup>
							</select>
						</div>
					</div>
					<div class="form-group" id="linkDiv">
						<label class="control-label col-sm-3">链接<span
							class="required"> * </span>
						</label>
						<div class="col-sm-9">
							<div class="input-icon right">
								<i class="fa"></i> <input type="text" class="form-control"
									id="link" name="link" value="${resource.link}" />
							</div>
						</div>
					</div>
					<div class="form-group">
						<label class="control-label col-sm-3">排序索引<span
							class="required"> * </span>
						</label>
						<div class="col-sm-9">
							<div class="input-icon right">
								<i class="fa"></i> <input type="text" class="form-control"
									name="idx" value="${resource.idx}" />
							</div>
						</div>
					</div>
					<div class="form-group">
						<label class="control-label col-sm-3">是否显示<span
							class="required"> * </span>
						</label>
						<div class="col-sm-9">
							<div class="input-icon right">
								<select class="bs-select form-control" name="isShow">
									<option value="1" <#if resource.isShow == 1> selected </#if>>显示</option>
									<option value="0" <#if resource.isShow == 0> selected </#if>>隐藏</option>
								</select>
							</div>
						</div>
					</div>
					<div class="form-group">
						<label class="control-label col-sm-3">shiro权限代码 </label>
						<div class="col-sm-9">
							<div class="input-icon right">
								<i class="fa"></i> <input type="text" class="form-control"
									name="shiroPermission" value="${resource.shiroPermission}" />
							</div>
						</div>
					</div>
				</div>
				<div class="form-actions">
					<div class="row">
						<div class="col-sm-3">
						</div>
						<div class="col-sm-9">
							<button type="submit" class="btn green">提交</button>
							<button type="button" class="btn default" onclick="parent.layer.closeAll();">取消</button>
						</div>
					</div>
				</div>
			</form>
		</div>
		<!-- END FORM-->
	</div>
</body>
</html>