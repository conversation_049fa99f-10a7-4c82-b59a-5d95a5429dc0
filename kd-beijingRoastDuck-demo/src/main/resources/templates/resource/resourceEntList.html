<!--[if IE 8]> <html lang="en" class="ie8 no-js"> <![endif]-->
<!--[if IE 9]> <html lang="en" class="ie9 no-js"> <![endif]-->
<!--[if !IE]><!-->
<html lang="en">
    <!--<![endif]-->
<head>
<meta charset="utf-8" />
<title>企业端资源管理</title>
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta content="width=device-width, initial-scale=1" name="viewport" />
<meta content="" name="description" />
<meta content="" name="author" />
<#include "/common/cssResource.html">
<link href="${ctx}/metronic/global/plugins/jquery-treetable/css/jquery.treetable.css" rel="stylesheet" type="text/css" />
<link href="${ctx}/metronic/global/plugins/jquery-treetable/css/jquery.treetable.theme.default.css" rel="stylesheet" type="text/css" />
<#include "/common/jsResource.html">
<script src="${ctx}/metronic/global/plugins/jquery-treetable/jquery.treetable.js" type="text/javascript"></script>
<script type="text/javascript">

$(document).ready(function() {
	$("#resourceTable").treetable({clickableNodeNames : true, expandable : true}).treetable("expandAll");
	$("#tab${platform}").attr("class", "active");
});

function openNew(obj){
	var openWindowWidth = $(document).width() * 0.8 + "px";
	var offsetH = ($(window).height()/5 - 20 > 120 ? 120 : $(window).height()/5 - 20) + "px";
	layer.open({
		type: 2,
		area: openWindowWidth,
		offset : offsetH,
		fix: false, //不固定
		maxmin: true,
		content: '${ctx}/resourceController/form?operationType=new&platform='+obj,
		success: function(layero, index){
		    layer.iframeAuto(index);
		}
	});
}

function openEdit(resourceId){
	var openWindowWidth = $(document).width() * 0.8 + "px";
	var offsetH = ($(window).height()/5 - 20 > 120 ? 120 : $(window).height()/5 - 20) + "px";
	layer.open({
		type: 2,
		area: openWindowWidth,
		offset : offsetH,
		fix: false, //不固定
		maxmin: true,
		content: '${ctx}/resourceController/form?id='+resourceId+'&operationType=edit&platform=${platform}',
		success: function(layero, index){
		    layer.iframeAuto(index);
		}
	});
}

function submitForm(tabIdx) {
	if(tabIdx == 0){
		$("#tab").val("0");
	}
	if(tabIdx == 1){
		$("#tab").val("1");		
	}
	if(tabIdx == 2){
		$("#tab").val("2");
	}
	if(tabIdx == 3){
		$("#tab").val("3");
	}
	if(tabIdx == 4){
		$("#tab").val("4");
	}
	if(tabIdx == 5){
		$("#tab").val("5");
	}
	$('#searchForm').submit();
}

</script>

</head>
<body>
	<!-- BEGIN PAGE BASE CONTENT -->
	<div class="row">
		<div class="col-sm-12">
			<!-- BEGIN EXAMPLE TABLE PORTLET-->
			<div class="portlet light portlet-fit bordered">
				<div class="portlet-title">
					<ul class="page-breadcrumb breadcrumb">
						<li><a>企业服务台</a> <i class="fa fa-circle"></i></li>
						<li><a>系统管理</a> <i class="fa fa-circle"></i></li>
						<li><span class="active">资源管理</span></li>
					</ul>
				</div>
				
				<div class="portlet-body">
					<form id="searchForm" method="post" action="${ctx}/resourceController/resourceList" class="form-horizontal">
						
						<input id="tab" name="platform" type="hidden" value="${platform}" />
						<!-- <div class="form-body">
							<div class="row">
								<div class="col-sm-4">
									<div class="form-group">
										<label class="control-label col-sm-4" style="padding-right: 0">用户名：</label>
										<div class="col-sm-8" style="padding-left: 0;">
											<input type="text" class="form-control" name="loginName" value="${enterpriseReq.loginName}" />
										</div>
									</div>
								</div>
								<div class="col-sm-4">
									<div class="form-group">
										<label class="control-label col-sm-4" style="padding-right: 0">企业名称：</label>
										<div class="col-sm-8" style="padding-left: 0">
											<input type="text" class="form-control" name="enterpriseName" value="${enterpriseReq.enterpriseName}" />
										</div>
									</div>
								</div>
							</div>
						</div> -->
						<!-- <div class="form-actions">
							<div class="row">
								<div class="col-sm-12">
									<div class="btn-group pull-right">
										<button type="button" class="btn green" onclick="submitForm(-1);">查询</button>
									</div>
								</div>
							</div>
						</div> -->
					</form>
					<div class="table-toolbar">
						<div class="row">
							<div class="col-sm-12">
								<div class="btn-group pull-right">
									<@shiro.hasPermission name="RESOURCE_ADD">
									<button id="sample_editable_1_new" class="btn green" onclick="openNew('${platform}')">新增</button>
									</@shiro.hasPermission>
								</div>
							</div>
						</div>
					</div>
					<ul class="nav nav-tabs">
						<li id="tab0"><a data-toggle="tab" onclick="submitForm(0);"> 系统端 </a></li>
						<li id="tab1"><a data-toggle="tab" onclick="submitForm(1);"> 客户公司端 </a></li>
						<li id="tab2"><a data-toggle="tab" onclick="submitForm(2);"> 经纪公司端 </a></li>
						<li id="tab3"><a data-toggle="tab" onclick="submitForm(3);"> 保险公司端 </a></li>
						<li id="tab4"><a data-toggle="tab" onclick="submitForm(4);"> 供应商端 </a></li>
						<li id="tab5"><a data-toggle="tab" onclick="submitForm(5);"> 人力资源端 </a></li>
					</ul>
					<table class="table table-striped table-bordered table-hover table-header-fixed"
						id="resourceTable">
						<thead>
							<tr>
								<th>资源名称</th>
								<th>类型</th>
								<th>链接</th>
								<th>排序</th>
								<th>显示</th>
								<th>shiro权限代码</th>
								<th>操作</th>
							</tr>
						</thead>
						<tbody>
							<#list resourceList as resource>
								<tr data-tt-id="${resource.id}" <#if resource.parentId != null>data-tt-parent-id="${resource.parentId}"</#if>>
									<td class="col-sm-2" style="word-wrap:break-word;word-break:break-all;">${resource.name}</td>
									<td class="col-sm-1" style="word-wrap:break-word;word-break:break-all;">
										<#if resource.type == 0>
											目录
										<#elseif resource.type == 1>
											菜单
										<#elseif resource.type == 2>
											权限
										</#if>
									</td>
									<td class="col-sm-3" style="word-wrap:break-word;word-break:break-all;">${resource.link}</td>
									<td class="col-sm-1" style="word-wrap:break-word;word-break:break-all;">${resource.idx}</td>
									<td class="col-sm-1" style="word-wrap:break-word;word-break:break-all;">
										<#if resource.isShow == 1>
											显示
										<#else>
											隐藏
										</#if>
									</td>
									<td class="col-sm-2" style="word-wrap:break-word;word-break:break-all;">${resource.shiroPermission}</td>
									<td class="col-sm-2" style="word-wrap:break-word;word-break:break-all;">
										<@shiro.hasPermission name="RESOURCE_EDIT">
										<a href="#" onclick="openEdit('${resource.id}')">编辑</a>
										</@shiro.hasPermission>
										<@shiro.hasPermission name="RESOURCE_DELETE">
										<a href="${ctx}/resourceController/deleteResource?id=${resource.id}&platform=${platform}" onclick="return confirm('包含删除连带子菜单，确认要执行 删除 操作吗？', this.href)">删除</a>
										</@shiro.hasPermission>
									</td>
								</tr>
							</#list>
						</tbody>
					</table>
				</div>
			</div>
			<!-- END EXAMPLE TABLE PORTLET-->
		</div>
	</div>
	<!-- END PAGE BASE CONTENT -->
</body>
</html>