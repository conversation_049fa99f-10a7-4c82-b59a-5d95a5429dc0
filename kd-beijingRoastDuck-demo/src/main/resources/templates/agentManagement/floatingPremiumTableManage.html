<!--[if IE 8]>
<html lang="en" class="ie8 no-js"> <![endif]-->
<!--[if IE 9]>
<html lang="en" class="ie9 no-js"> <![endif]-->
<!--[if !IE]><!-->
<html lang="en">
<!--<![endif]-->
<head>
    <meta charset="UTF-8">
    <title>浮动保费表导入</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta content="width=device-width, initial-scale=1" name="viewport"/>
    <meta content="" name="description"/>
    <meta content="" name="author"/>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <style type="text/css">
        .vaerror {
            background: #f8e6e6;
            border: 1px solid #ec9b9b;
        }
    </style>
    <script type="text/javascript">


        //取消按钮失效，样式恢复
        function cancelDisabled() {
            if ($.isFunction(parent.iFrameHeight)) {
                parent.iFrameHeight();
            }
            $("#subBtn").attr("disabled", false);
            $("#subBtn").attr("class", "btn btn-default");
            $("#subBtn").removeAttr("style");
            $("#myModal").css('display', 'none');
            $("#myModal").modal("hide");
            $('#myModal').on('hidden.bs.modal', function () {
                $("#myModal").off().on('shown', 'shown.bs.modal');
            });
        }

        // 提交
        function saveAgentFloatFeeForm() {
            // 点击提交后，遮罩且按钮失效
            $("#subBtn").attr("disabled", true);
            $("#subBtn").attr("class", "btn btn-primary");
            $("#subBtn").css("background", "#6ba7e8");

            $(".errorDiv1").detach();

            var height = Math.round((document.body.scrollHeight - window.screen.availHeight) + (window.screen.availHeight) / 2);
            $("#myModal").css("margin-top", height + "px");// 设置提示高度
            $('#myModal').modal({backdrop: 'static'});// 设置提示居中

            var fileType = $("#uploadFile").val().split(".");
            if (fileType[fileType.length - 1] != "xlsx") {
                $("#uploadFile").val("");
                $('#errorModal').modal('show');
                cancelDisabled();
                return;
            }

            $('#myModal').on('shown.bs.modal', function () {
                var formData = new FormData($('#inputFileForm')[0]);
                $.ajax({
                    url: "${ctx}/agentManagementController/floatingPremiumCheck",
                    type: 'POST',
                    data: formData,
                    async: true,
                    cache: false,
                    contentType: false,
                    processData: false,
                    success: function (data) {
                        cancelDisabled();
                        $("#uploadBtn").val("");
                        $("#uploadFile").val("");

                        var result = eval("("+data+")");
                        console.log(data);
                        if (result.ret == 0) {
                            alert("添加成功，导入浮动保费表："+result.msg)
                            parent.window.location.reload();
                            var index = parent.layer.getFrameIndex(window.name); //获取窗口索引
                            parent.layer.close(index);
                        } else {
                            $("#errorInfo").append("<div style='padding-top: 10px; color: #e34444;' class='errorDiv1'>错误原因如下：</div>");
                            $("#errorInfo").append("<div style='padding-top: 10px; padding-left: 20px; color: #e34444;' class='errorDiv1'>" + result.msg + "</div>");
                            $("#errorInfo").append("<div style='padding-top: 20px; padding-left: 20px;'class='errorDiv1'>"
                                + 	"<font>备注：如有疑问，请联系客服！</font>"
                                +  "</div>");
                            $("#errorDiv").show();
                        }
                    },
                    error: function (data) {
                        var result = eval("(" + data + ")");
                        alert(result.msg);
                    }
                });
            });
        }

    </script>

</head>
<body>
<!-- BEGIN PAGE BASE CONTENT -->
<div class="row">
    <div class="col-sm-12">
        <!-- BEGIN EXAMPLE TABLE PORTLET-->
        <div class="portlet light portlet-fit bordered">
            <div class="portlet-title">
                <ul class="page-breadcrumb breadcrumb">
                    <li><a href="#">管理功能</a> <i class="fa fa-circle"></i></li>
                    <li><span class="active">代理商管理</span></li>
                </ul>
            </div>
            <div class="portlet-body">
                <div class="modal fade" id="myModal" tabindex="-1" role="basic" aria-hidden="true">
                    <div class="modal-dialog">
                        <font size="6" style="font-weight: bold;"> 数据正在提交，请稍后...</font>
                    </div>
                </div>
                <div class="" id="tj_batch" style="padding-left: 20px;margin-top: 10px;">
                    <form id="inputFileForm" class="form-horizontal" method="post" enctype="multipart/form-data">
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="control-label col-sm-3" style="padding-right: 0">保险公司代码：</label>
                                <div class="col-sm-8" style="padding-left: 0;">
                                    <input type="text" class="form-control" name="insCode" id="insCode"
                                           placeholder="请输入"/>
                                </div>
                            </div>
                        </div>
                        <div class="uploadFilebox">
                            <div class="fileUpload btn btn-primary">
                                <input id="uploadBtn" type="file" name="file" class="upload" accept=".xlsx"/>
                            </div>
                            <input id="uploadFile" placeholder="请选择文件" disabled="disabled" type="hidden"/>
                            <script>
                                document.getElementById("uploadBtn").onchange = function () {
                                    $("#uploadFile").val(this.value);
                                };
                            </script>&nbsp;&nbsp;
                            <button type="button" id="subBtn" class="btn btn-default"
                                    onClick="saveAgentFloatFeeForm();">导入
                            </button>
                        </div>
                        <br>
                    </form>
                    <div class="col-sm-12">
                        <div class="mt-element-list" style="display: none;" id="errorDiv">
                            <div class="mt-list-container list-news" style="box-shadow: 2px 2px 7px rgba(0, 0, 0, .4);"
                                 id="containerUl">
                                <div class="list-item-content formstyle1 enterset_con_l"
                                     style="padding-top: 10px; font-size: 16px; padding-bottom: 20px;" id="errorInfo">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- END EXAMPLE TABLE PORTLET-->
        </div>
    </div>
</div>
</body>
</html>