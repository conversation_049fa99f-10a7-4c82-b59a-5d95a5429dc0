<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!-- 上述3个meta标签*必须*放在最前面，任何其他内容都*必须*跟随其后！ -->
    <meta name="description" content="">
    <meta name="author" content="">
    <title>保单配置列表</title>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">

    <link href="${ctx}/metronic/global/plugins/select2/css/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/select2/css/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>
    <script src="${ctx}/metronic/global/plugins/select2/js/select2.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/plugins/select2/js/select2.full.min.js" type="text/javascript"></script>

    <script type="text/javascript">

        function page(n, s) {
            $("#pageNum").val(n);
            $("#pageSize").val(s);
            $("#searchForm").submit();
            return false;
        }

        /*新增*/
        function openNew() {
            window.location.href = `${ctx}/policyController/add`;
        }

        /*变更*/
        function change(policyNo) {

            var openWindowWidth = $(document).width() * 0.3 + "px";
            var openWindowHeight = $(document).height() * 0.4 + "px";
            var offsetH = ($(window).height() / 5 - 20 > 480 ? 300 : $(window).height() / 5 - 20) + "px";
            layer.open({
                type: 2,
                title: '保单业务变更',
                area: [openWindowWidth, openWindowHeight],
                offset: offsetH,
                shadeClose: false,
                fix: false, //不固定
                btn: ['确定', '取消'], //按钮
                maxmin: true,
                btn1: function (index, layero) {
                    //当点击‘确定'按钮的时候，获取弹出层返回的值
                    var res = window["layui-layer-iframe" + index].callbackdata();
                    if (res.textareaData.trim()) {
                        let submitTaskObj = {
                            business: res.textareaData,
                            policyNo: res.policyNo
                        };
                        $.post("${ctx}/policyController/changeSubmit?business="+submitTaskObj.business+"&policy="+submitTaskObj.policyNo , function (data) {
                            let res = JSON.parse(data);
                            if (res.ret == 0) {
                                layer.msg('提交成功', {icon: 6, time: 1000},
                                    function () {
                                        window.location.reload();
                                    });
                            } else {
                                alert(res.msg);
                            }
                        });
                    } else {
                        alert("保单业务不能为空");
                    }
                },
                content: "${ctx}/policyController/change?policyNo="+policyNo
            });
        }
    </script>
    <style>
        table {
            table-layout: fixed;
        }

        table td {
            word-break: break-all;
            white-space: normal;
        }

        table td > a {
            margin-right: 4px;
            text-decoration: none;
        }

        .text-white {
            color: white;
        }

        .audit{
            color: #1676FF;
            background-color: #ffffff;
            border-color: #1676FF;
            border-radius: 5px !important;
        }
        .audit:hover{
            background-color: #1676FF;
        }

    </style>
</head>
<body id="qc-Body">
<div class="row">
    <div class="col-sm-12">
        <div class="portlet light portlet-fit bordered">
            <div class="portlet-title ">
                <ul class="page-breadcrumb breadcrumb">
                    <li><a href="#">企业服务台</a> <i class="fa fa-circle"></i></li>
                    <li><a href="#">管理功能</a> <i class="fa fa-circle"></i></li>
                    <li><span class="active">保单管理</span></li>
                </ul>
            </div>
            <div class="portlet-body">
                <!-- BEGIN FORM-->
                <form id="searchForm" class="form-horizontal" action="${ctx}/policyController/policyList"
                      method="post">
                    <input id="pageNum" name="pageNum" type="hidden" value="${page.pageNum}"/>
                    <input id="pageSize" name="pageSize" type="hidden" value="${page.pageSize}"/>
                    <div class="form-body">
                        <div class="row">
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">业务名称：</label>
                                    <div class="col-sm-8 select2-bootstrap-append" style="padding-left: 0;">
                                        <select class="form-control select2-allow-clear" name="business" id="business">
                                            <option value="">-请选择-</option>
                                            <#list stringSet as ent>
                                                <option value="${ent}" <#if dataCollectionReq.business == ent>selected</#if>>${ent}</option>
                                            </#list>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">保单号：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <input type="text" class="form-control" name="policyNo" id="policyNo"
                                               value="${dataCollectionReq.policyNo}"
                                               placeholder="保单号"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">是否配置：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <select class="form-control" name="isConfiguration" id="isConfiguration">
                                            <option value="">-请选择-</option>
                                            <option value="0" <#if dataCollectionReq.isConfiguration == 0>selected</#if>>未配置</option>
                                            <option value="1" <#if dataCollectionReq.isConfiguration == 1>selected</#if>>已配置</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12 text-right" style="margin-bottom: 5px ">
                                <button type="button" id="sample_editable_1_new" class="btn green" onclick="openNew()">新增</button>
                                <input type="submit" class="btn blue" style="margin-left: 20px;border-radius: 5px!important;" value="查询" />
                            </div>
                        </div>
                    </div>
                </form>
                <table class="table table-striped table-bordered table-hover table-header-fixed">
                    <thead>
                    <tr>
                        <th>保单号</th>
                        <th>业务名称</th>
                        <th>任务数量</th>
                        <th>是否配置</th>
                        <th>功能</th>
                    </tr>
                    </thead>
                    <tbody>
                    <#list page.list as c>
                        <tr>
                            <td>
                                ${c.policyNo!'--'}
                            </td>
                            <td>
                                ${taskMap.get(c.policyNo)!'--'}
                            </td>
                            <td>
                                ${c.status!'--'}
                            </td>
                            <td>
                                <#if taskMap[c.policyNo]??>
                                    已配置
                                <#else>
                                   未配置
                                </#if>
                            </td>
                            <td>
                                <a class="btn audit" onclick="change('${c.policyNo}')">变更</a>
                            </td>
                        </tr>
                    </#list>
                    </tbody>
                </table>
                <div class="modal fade" id="saveBtnModal" tabindex="-1" role="basic" aria-hidden="true">
                    <div class="modal-dialog">
                        <img src="${ctx}/images/load.gif">
                    </div>
                </div>
            </div>
            <!-- END FORM-->
        </div>
    </div>
</div>
<@sc.pagination page=page />
</body>
</html>
