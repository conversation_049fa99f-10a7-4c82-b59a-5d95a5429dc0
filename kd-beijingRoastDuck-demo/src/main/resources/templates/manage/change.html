<!--[if IE 8]>
<html lang="en" class="ie8 no-js"> <![endif]-->
<!--[if IE 9]>
<html lang="en" class="ie9 no-js"> <![endif]-->
<!--[if !IE]><!-->
<html lang="en" xmlns="http://www.w3.org/1999/html">
<!--<![endif]-->
<head>
    <meta charset="utf-8"/>
    <title>核对数据</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta content="width=device-width, initial-scale=1" name="viewport"/>
    <meta content="" name="description"/>
    <meta content="" name="author"/>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">

    <link href="${ctx}/metronic/global/plugins/select2/css/select2.min.css" rel="stylesheet" type="text/css" />
    <link href="${ctx}/metronic/global/plugins/select2/css/select2-bootstrap.min.css" rel="stylesheet" type="text/css" />
    <script src="${ctx}/metronic/pages/scripts/components-select2.min.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/plugins/select2/js/select2.full.min.js" type="text/javascript"></script>

    <style type="text/css">
        .content-box {
            display: -webkit-flex;
            display: flex;
            flex-direction: row;
            flex-wrap: nowrap;
            justify-content: flex-start;
            align-items: baseline;
        }
    </style>
    <script type="text/javascript">

        $(document).ready(function () {
            $("#business").select2({
                placeholder: "无",
                templateSelection: function (repo) {
                    if (repo.id != "") {
                        console.log(repo);
                        return repo.text;
                    }
                    return "";
                }
            });
        });

        var callbackdata = function () {
            var data = {
                textareaData: $("#business").val(),
                policyNo: "${policyNo}"
            };
            return data;
        }
    </script>

</head>
<body>
<!-- BEGIN PAGE BASE CONTENT -->
<div class="row">
    <div class="col-sm-12" style="margin-bottom: 200px;">

        <div class="col-sm-12 content-box" >
            <span>保单：${policyNo} </br>现所属业务：${business}</span>
        </div>
        <div class="col-sm-12 content-box">
            <span>变更后所属业务：</span>
            <select class="form-control select2-allow-clear" style="width: 50%;margin-right: 0px" name="business" id="business">
                <option value="无">无</option>
                <#list stringSet as ret>
                    <option value="${ret}" <#if ret==business>selected</#if>>${ret}</option>
                </#list>
            </select>
        </div>
    </div>
</div>
</body>
</html>