<!--[if IE 8]>
<html lang="en" class="ie8 no-js"> <![endif]-->
<!--[if IE 9]>
<html lang="en" class="ie9 no-js"> <![endif]-->
<!--[if !IE]><!-->
<html lang="en">
<!--<![endif]-->
<head>
    <meta charset="utf-8"/>
    <title>分配审核组别和审核人员</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta content="width=device-width, initial-scale=1" name="viewport"/>
    <meta content="" name="description"/>
    <meta content="" name="author"/>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <link href="${ctx}/plugins/select2/css/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/plugins/select2/css/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>
    <script src="${ctx}/plugins/select2/js/select2.js" type="text/javascript"></script>
    <script src="${ctx}/plugins/select2/js/select2.full.min.js" type="text/javascript"></script>

    <link href="${ctx}/metronic/global/plugins/bootstrap-select/css/bootstrap-select.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/jquery-multi-select/css/multi-select.css" rel="stylesheet" type="text/css"/>
    <script src="${ctx}/metronic/global/plugins/bootstrap-select/js/bootstrap-select.min.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/plugins/jquery-multi-select/js/jquery.multi-select.js" type="text/javascript"></script>

    <script type="text/javascript">

        $(document).ready(function () {

            $("#submit").on("click", function (e) {

                var roleIds = "";
                $("#manager_select option:selected").each(function() {
                    roleIds += $(this).val() + ",";
                });

                if (roleIds == ""){
                    alert("请先分配保单！");
                    return false;
                }

                if (!$("#managerName").val()){
                    alert("请填写业务名称！");
                    return false;
                }

                var formData = new FormData();
                formData.append("business", $("#managerName").val());
                formData.append("policyNo",roleIds );

                $.ajax({
                    url: "${ctx}/policyController/submit",
                    type: 'POST',
                    data: formData,
                    async: true,
                    cache: false,
                    contentType: false,
                    processData: false,
                    success: function (data) {
                        var result = eval("(" + data + ")");
                        if (result.ret != 0) {
                            alert( result.msg);
                            return false;
                        }
                        alert("成功");
                        window.location.href = "${ctx}/policyController/policyList"
                    },
                    error: function (data) {
                        alert(data);
                    }
                });

            });

            $("#manager_select").multiSelect({
                selectableFooter: "<div class='custom-header'>未分配保单</div>",
                selectionFooter: "<div class='custom-header'>已选择保单</div>"
            });

        });

    </script>

</head>
<body>

<div class="portlet light bordered">
    <div class="portlet-body">


        <div class="row">
            <div class="col-sm-12 form-group">
                <h4>
                    <div>
                        <i class="fa fa-bookmark"></i>
                        <span class="caption-subject uppercase">新增业务</span>
                    </div>
                </h4>
            </div>
            <div class="col-sm-12 form-group">
                <div class="portlet light bordered">
                    <div class="row">
                        <div class="col-sm-12 ">
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-4" style="padding-right: 0">新增业务名称：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <input type="text" class="form-control" name="managerName" id="managerName" placeholder="输入新增业务名称"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-sm-12 form-group">
                <div class="portlet light bordered">
                    <div class="row">
                        <div class="col-sm-12 ">
                            <label class="control-label col-md-3">待选保单范围</label>
                            <div class="col-md-9">
                                <select multiple="multiple" class="multi-select" id="manager_select">
                                    <#list list as roleGrant>
                                        <option value="${roleGrant}">${roleGrant}</option>
                                    </#list>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-sm-12 form-group">
                <div class="col-sm-4 col-sm-offset-8">
                    <button id="submit" type="button" class="btn green pull-right" style="margin: 10px;">提交</button>
                </div>
            </div>
        </div>

    </div>
</div>
</body>
</html>