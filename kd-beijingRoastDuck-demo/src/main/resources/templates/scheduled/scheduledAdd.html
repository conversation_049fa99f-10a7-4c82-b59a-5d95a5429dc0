<!--[if IE 8]>
<html lang="en" class="ie8 no-js"> <![endif]-->
<!--[if IE 9]>
<html lang="en" class="ie9 no-js"> <![endif]-->
<!--[if !IE]><!-->
<html lang="en" xmlns="http://www.w3.org/1999/html">
<!--<![endif]-->
<head>
    <meta charset="utf-8"/>
    <title>定时任务</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta content="width=device-width, initial-scale=1" name="viewport"/>
    <meta content="" name="description"/>
    <meta content="" name="author"/>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">

    <link type="text/css" href="${ctx}/a/cronUtil/cronGen.css" rel="Stylesheet"/>
    <script src="${ctx}/a/cronUtil/cronGen.js" type="text/javascript"></script>
    <script src="${ctx}/a/cronUtil/later.min.js" type="text/javascript"></script>
    <script type="text/javascript">
        $(function () {
            //编辑页
            var scheduledStr = '${scheduledStr}';
            var scheduleE = {};
            if (scheduledStr) {
                scheduleE = eval("(" + scheduledStr + ")");
            }
            //初始化cron
            $("#cron").cronGen({
                direction: 'left'
            });
            //解析cron表达式式
            $('#startBtn').click(function () {
                var link = $("#cron").val();
                if (link == '') {
                    layer.msg(
                        "表达式不能为空！",
                        {
                            time: 1000
                        }
                    );
                    return false;
                }
                var linkArray = link.split(' ');
                var sched = later.parse.cron(link, linkArray.length);
                later.date.localTime();
                var jxmsg = "";
                var results = later.schedule(sched).next(10);
                jxmsg += "最近10次运行结果：<hr/>";
                for (var i = 0; i < results.length; i++) {
                    if ((i != 0) && (i % 2 != 0)) {
                        jxmsg += ("<i style='color:blue;'>" + i + "</i>：" + results[i].toLocaleString() + "<hr/>");
                    } else {
                        jxmsg += ("<i style='color:blue;'>" + i + "</i>：" + results[i].toLocaleString() + "&emsp;");
                    }
                }
                layer.msg(jxmsg, {
                    time: 20000,
                    area: ['50%', '70%'],
                    btn: ['确定']
                });
            });
            //队列名 请求url
            $('#reqType').on('click', function () {
                var reqType = $(this).val();
                if (reqType == '3') {
                    if ($('.urlAddress').length > 0) {
                        $('.urlAddress').remove();
                    }
                    if ($('.queueName').length === 0) {
                        $('.reqType').after('<div class="form-group queueName"> <div class="row"> <label class="col-sm-3 control-label" for="queueName"> <span style="color:red">*</span>队列名称：</label> <div class="col-sm-6"> <input type="text" class="form-control" id="queueName" name="queueName" maxlength="50"/> </div> </div> </div>');
                        if (scheduledStr) {
                            $('#queueName').val(scheduleE.queueName);
                        }
                    }
                } else if (reqType == '') {
                    if ($('.queueName').length > 0) {
                        $('.queueName').remove();
                    } else {
                        $('.urlAddress').remove();
                    }
                } else {
                    if ($('.queueName').length > 0) {
                        $('.queueName').remove();
                    }
                    if ($('.urlAddress').length === 0) {
                        $('.reqType').after('<div class="form-group urlAddress"> <div class="row"> <label class="col-sm-3 control-label" for="url"> <span style="color:red">*</span>url地址：</label> <div class="col-sm-6"> <input type="text" class="form-control" id="url" name="url" maxlength="100"/> </div> </div> </div>');
                        if (scheduledStr) {
                            $('#url').val(scheduleE.url);
                        }
                    }
                }
            });
            //编辑页
            if (scheduledStr) {
                var reqType = scheduleE.reqType;
                $('#taskName').val(scheduleE.taskName);
                $('#type').val(scheduleE.type);
                $('#cron').val(scheduleE.cron);
                $('#reqType').val(reqType).trigger('click');
                $('#status').val(scheduleE.status);
                if (reqType == '3') {
                    $('#queueName').val(scheduleE.queueName);
                } else {
                    $('#url').val(scheduleE.url);
                }
            }
        })

        //确认
        function doAdd() {
            //校验
            var isError = false;
            $('input').each(function () {
                if ($(this).val() === '') {
                    isError = true;
                }
            })
            $('select').each(function () {
                if ($(this).val() === '') {
                    isError = true;
                }
            })
            if (isError) {
                alert('请输入或选择，不可为空！');
                return false;
            }
            var formData = new FormData($('#inputForm')[0]);
            $.ajax({
                url: "${ctx}/scheduledController/saveOrEdit",
                type: 'POST',
                data: formData,
                async: true,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    var result = eval("(" + data + ")");
                    if (result.ret === "-1") {
                        layer.msg(result.msg,
                            {
                                time: 2000
                            });
                        return false;
                    }
                    parent.window.location.reload();
                    var index = parent.layer.getFrameIndex(window.name); //获取窗口索引
                    parent.layer.close(index);
                },
                error: function (data) {
                    var result = eval("(" + data + ")");
                    alert(result.msg);
                }
            });
        }

        //取消
        function doCancel() {
            var index = parent.layer.getFrameIndex(window.name); //获取窗口索引
            parent.layer.close(index);
        }
    </script>
    <style>

    </style>
</head>
<body>
<div class="portlet light bordered">
    <div class="portlet-body form">
        <form id="inputForm" class="form-horizontal form-row-seperated">
            <#if scheduled ??>
                <input type="hidden" name="id" value="${scheduled.id}" id="id"/>
            </#if>
            <div class="form-body">
                <div class="form-group">
                    <div class="row">
                        <label class="col-sm-3 control-label" for="taskName"><span
                                    style="color:red">*</span>任务名称：</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="taskName" name="taskName"
                                   maxlength="50"/>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <div class="row">
                        <label class="col-sm-3 control-label" for="type"><span
                                    style="color:red">*</span>任务类型：</label>
                        <div class="col-sm-6">
                            <select id="type" name="type" class="form-control">
                                <option value="">-请选择-</option>
                                <option value="1">循环任务</option>
                                <option value="2">单次任务</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="form-group">

                    <div class="row">
                        <label class="col-sm-3 control-label" for="cron"><span
                                    style="color:red">*</span>cron表达式：</label>
                        <div class="col-sm-4">
                            <div class="input-group">
                                <input type="text" class="form-control" id="cron" name="cron"
                                       maxlength="50"/>
                            </div>
                        </div>
                        <div class="col-sm-2">
                            <button type=button id="startBtn" class="btn btn-info">表达式解析</button>
                        </div>
                    </div>
                </div>
                <div class="form-group reqType">
                    <div class="row">
                        <label class="col-sm-3 control-label" for="reqType"><span
                                    style="color:red">*</span>请求类型：</label>
                        <div class="col-sm-6">
                            <select id="reqType" name="reqType" class="form-control">
                                <option value="">-请选择-</option>
                                <option value="1">外网url请求</option>
                                <option value="2">内网url请求</option>
                                <option value="3">mq队列请求</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <div class="row">
                        <label class="col-sm-3 control-label" for="status"><span
                                    style="color:red">*</span>状态：</label>
                        <div class="col-sm-6">
                            <select id="status" name="status" class="form-control">
                                <option value="1" selected>有效</option>
                                <option value="-1">无效</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="form-actions">
                <div class="col-sm-12">
                    <div class="btn-group pull-right">
                        <button class="btn btn-success" id="confirmBtn" type="button"
                                style="margin-right: 20px"
                                onclick="doAdd()">确认
                        </button>
                        <button class="btn" id="cancelBtn" type="button"
                                style="margin-right:80px" onclick="doCancel()">取消
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <!-- END FORM-->
</div>
</body>
</html>