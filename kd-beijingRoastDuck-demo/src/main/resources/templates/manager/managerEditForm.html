<!--[if IE 8]> <html lang="en" class="ie8 no-js"> <![endif]-->
<!--[if IE 9]> <html lang="en" class="ie9 no-js"> <![endif]-->
<!--[if !IE]><!-->
<html lang="en">
<!--<![endif]-->
<head>
<meta charset="utf-8" />
<title>编辑用户</title>
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta content="width=device-width, initial-scale=1" name="viewport" />
<meta content="" name="description" />
<meta content="" name="author" />
<#include "/common/cssResource.html"> <#include
"/common/jsResource.html">

<script type="text/javascript">

	$(document).ready(function() {
		var form = $('#inputForm');
		form.validate({
			errorElement : 'span', //default input error message container
			errorClass : 'help-block help-block-error', // default input error message class
			focusInvalid : false, // do not focus the last invalid input
			rules : {
				userName : {
					minlength : 4,
					maxlength : 50,
					required : true,
					remote : "${ctx}/jqueryValidateController/checkManagerUserName?id=${manager.id}"
				},
				realName : {
					maxlength : 50,
					required : true
				},
				mobile : {
					maxlength : 50,
					required : true,
					isMobilePhone : true
				},
				telephone : {
					maxlength : 50,
					isTel : true
				},
				email : {
					maxlength : 100,
					required : true,
					isEmail : true
				}
			},
			messages: {
				userName: {remote : "用户名已存在"}
			},

			invalidHandler : function(event, validator) { //display error alert on form submit
				App.scrollTo(form, 0);
			},

			highlight : function(element) { // hightlight error inputs
				$(element).closest('.form-group').removeClass(
						"has-success").addClass('has-error'); // set error class to the control group
			},

			unhighlight : function(element) { // revert the change done by hightlight

			},

			success : function(label, element) {
				var icon = $(element).parent('.input-icon').children(
						'i');
				$(element).closest('.form-group').removeClass(
						'has-error').addClass('has-success'); // set success class to the control group
				icon.removeClass("fa-warning").addClass("fa-checkDifference18");
			},

			submitHandler : function(form) {
				var formData = new FormData($('#inputForm')[0]);
				$.ajax({
					url : "${ctx}/managerController/editManager",
					type : 'POST',
					data : formData,
					async : true,
					cache : false,
					contentType : false,
					processData : false,
					success : function(data) {
						var result = eval("(" + data + ")");
						parent.window.location.reload();
						var index = parent.layer.getFrameIndex(window.name); //获取窗口索引
						parent.layer.close(index);
					},
					error : function(data) {
						var result = eval("(" + data + ")");
						alert(result.msg);
					}
				});
			}
		});
	});

</script>

</head>
<body>
	<div class="portlet light bordered">
		<div class="portlet-body form">
			<!-- BEGIN FORM-->
			<form id="inputForm" class="form-horizontal form-row-seperated">
				<input type="hidden" name="id" value="${manager.id}" />
				<div class="form-body">
					<div class="form-group">
						<label class="control-label col-sm-3">用户名 <span
							class="required"> * </span>
						</label>
						<div class="col-sm-9">
							<div class="input-icon right">
								<i class="fa"></i> <input type="text" class="form-control"
									name="userName" value="${manager.userName}" />
							</div>
						</div>
					</div>
					<div class="form-group">
						<label class="control-label col-sm-3">真实姓名 <span
							class="required"> * </span>
						</label>
						<div class="col-sm-9">
							<div class="input-icon right">
								<i class="fa"></i> <input type="text" class="form-control"
									name="realName" value="${manager.realName}" />
							</div>
						</div>
					</div>
					<div class="form-group">
						<label class="control-label col-sm-3">手机 <span
							class="required"> * </span></label>
						<div class="col-sm-9">
							<div class="input-icon right">
								<i class="fa"></i> <input type="text" class="form-control"
									name="mobile" value="${manager.mobile}" />
							</div>
						</div>
					</div>
					<div class="form-group">
						<label class="control-label col-sm-3">座机 </label>
						<div class="col-sm-9">
							<div class="input-icon right">
								<i class="fa"></i> <input type="text" class="form-control"
									name="telephone" value="${manager.telephone}" />
							</div>
						</div>
					</div>
					<div class="form-group">
						<label class="control-label col-sm-3">邮箱 <span
							class="required"> * </span></label>
						<div class="col-sm-9">
							<div class="input-icon right">
								<i class="fa"></i> <input type="text" class="form-control"
									name="email" value="${manager.email}" />
							</div>
						</div>
					</div>
					<div class="form-group">
						<label class="control-label col-sm-3">状态 <span
							class="required"> * </span></label>
						<div class="col-sm-9">
							<select class="bs-select form-control" name="status">
								<option value="1" <#if manager.status == 1> selected </#if>>有效</option>
								<option value="0" <#if manager.status == 0> selected </#if>>无效</option>
							</select>
						</div>
					</div>
				</div>
				<div class="form-actions">
					<div class="row">
						<div class="col-sm-3">
						</div>
						<div class="col-sm-9">
							<button type="submit" class="btn green">提交</button>
							<button type="button" class="btn default" onclick="parent.layer.closeAll();">取消</button>
						</div>
					</div>
				</div>
			</form>
		</div>
		<!-- END FORM-->
	</div>
</body>
</html>