/**
 * Lithuanian translation for bootstrap-datetimepicker
 * <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
 */

;(function($){
    $.fn.datetimepicker.dates['lt'] = {
        days: ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Pirmadienis", "<PERSON><PERSON><PERSON><PERSON>", "Trečiadienis", "Ketvirtadien<PERSON>", "Penktadienis", "Š<PERSON>štadien<PERSON>", "Sekmadien<PERSON>"],
        daysShort: ["S", "Pr", "A", "T", "K", "Pn", "Š", "S"],
        daysMin: ["Sk", "Pr", "An", "Tr", "Ke", "Pn", "Št", "Sk"],
        months: ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "G<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>ug<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>p<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"],
        monthsShort: ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Rugp", "Rugs", "<PERSON>", "Lap", "<PERSON>ru"],
        today: "Šiandien",
		suffix: [],
		meridiem: [],
        weekStart: 1
    };
}(jQuery));
