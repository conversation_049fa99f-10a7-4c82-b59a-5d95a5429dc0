/**
 * Dutch translation for bootstrap-datetimepicker
 * <PERSON><PERSON><PERSON> <m<PERSON><PERSON><PERSON>@gmail.com>
 */
;(function($){
	$.fn.datetimepicker.dates['nl'] = {
		days: ["Zondag", "Maandag", "Dinsdag", "Woensdag", "Donderdag", "Vrijdag", "Zaterdag", "Zondag"],
		daysShort: ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>o", "<PERSON>", "Vr", "<PERSON><PERSON>", "<PERSON><PERSON>"],
		daysMin: ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Wo", "<PERSON>", "<PERSON>r", "<PERSON>a", "<PERSON>o"],
		months: ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "April", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "September", "<PERSON><PERSON><PERSON>", "November", "December"],
		monthsShort: ["<PERSON>", "Feb", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "Nov", "Dec"],
		today: "Vandaag",
		suffix: [],
		meridiem: []
	};
}(jQ<PERSON>y));
