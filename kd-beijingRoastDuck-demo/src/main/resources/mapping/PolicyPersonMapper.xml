<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="kd.beijingRoastDuck.dao.PolicyPersonDao">

	<resultMap id="BaseResultVoMap" type="kd.beijingRoastDuck.support.BusinessDataExport">
		<result column="dateStr" property="dateStr" jdbcType="VARCHAR"/>
		<result column="planCode" property="planCode" jdbcType="VARCHAR"/>
		<result column="sumCount" property="sumCount" jdbcType="INTEGER"/>
	</resultMap>

	<insert id="insertSelective" parameterType="kd.entity.PolicyPerson">
		<include refid="mapping.PolicyPersonMapper.insertSelective" />
	</insert>
	
	<update id="updateByPrimaryKeySelective" parameterType="kd.entity.PolicyPerson">
		<include refid="mapping.PolicyPersonMapper.updateByPrimaryKeySelective" />
	</update>

	<select id="selectInsuredBusinessDataExport" resultMap="BaseResultVoMap">
		SELECT
		DATE_FORMAT(create_time,"%Y-%m-%d") as dateStr,
		plan_id as planCode,
		COUNT(1) as sumCount
		FROM t_policy_person
		WHERE ex_info_json ="false" AND policy_no IS NOT NULL
		GROUP BY plan_id,DATE_FORMAT(create_time,"%Y-%m-%d")
		order by create_time asc
	</select>

	<select id="selectUnInsuredBusinessDataExport" resultMap="BaseResultVoMap">
		SELECT
		DATE_FORMAT(create_time,"%Y-%m-%d") as dateStr,
		plan_id as planCode,
		COUNT(1) as sumCount
		FROM t_policy_person
		WHERE ex_info_json ="false" AND policy_no IS NULL
		GROUP BY plan_id,DATE_FORMAT(create_time,"%Y-%m-%d")
		order by create_time asc
	</select>

</mapper>