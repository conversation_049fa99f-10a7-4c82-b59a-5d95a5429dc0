<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="kd.beijingRoastDuck.dao.ManagerDao">
	
	<delete id="deleteByPrimaryKey" parameterType="java.lang.String">
		<include refid="mapping.ManagerMapper.deleteByPrimaryKey" />
	</delete>
	
	<insert id="insertSelective" parameterType="kd.entity.Manager">
		<include refid="mapping.ManagerMapper.insertSelective" />
	</insert>
	
	<update id="updateByPrimaryKeySelective" parameterType="kd.entity.Manager">
		<include refid="mapping.ManagerMapper.updateByPrimaryKeySelective" />
	</update>
	

</mapper>