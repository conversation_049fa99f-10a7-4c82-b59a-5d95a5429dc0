<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="kd.beijingRoastDuck.dao.ProductDao">
	
	<insert id="insertSelective" parameterType="kd.entity.Product">
		<include refid="mapping.ProductMapper.insertSelective" />
	</insert>
	
	<update id="updateByPrimaryKeySelective" parameterType="kd.entity.Product">
		<include refid="mapping.ProductMapper.updateByPrimaryKeySelective" />
	</update>
	
	
</mapper>