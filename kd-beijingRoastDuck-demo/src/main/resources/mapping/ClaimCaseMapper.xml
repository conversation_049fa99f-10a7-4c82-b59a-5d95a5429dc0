<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="kd.beijingRoastDuck.dao.ClaimCaseDao">

	<resultMap id="BaseResultVoMap" type="kd.beijingRoastDuck.support.BusinessDataExport">
		<result column="dateStr" property="dateStr" jdbcType="VARCHAR"/>
		<result column="claimCaseCount" property="claimCaseCount" jdbcType="INTEGER"/>
	</resultMap>
	
	<insert id="insertSelective" parameterType="kd.entity.ClaimCase">
		<include refid="mapping.ClaimCaseMapper.insertSelective" />
	</insert>
	
	<update id="updateByPrimaryKeySelective" parameterType="kd.entity.ClaimCase">
		<include refid="mapping.ClaimCaseMapper.updateByPrimaryKeySelective" />
	</update>

	<select id="selectByPrimaryKey" resultMap="mapping.ClaimCaseMapper.BaseResultMap" parameterType="java.lang.String">
		<include refid="mapping.ClaimCaseMapper.selectByPrimaryKey" />
	</select>

	<update id="setAappraisalAmountIsNull" parameterType="java.lang.String">
		update t_claim_case
		set appraisal_amount = null
		where id = #{claimCaseId,jdbcType=VARCHAR}
	</update>

	<select id="selectGroupByDate" resultMap="BaseResultVoMap">
		select
		DATE_FORMAT(create_time,"%Y-%m-%d") as dateStr,
		COUNT(1) as claimCaseCount
		from t_claim_case
		GROUP BY DATE_FORMAT(create_time,"%Y-%m-%d")
	</select>

</mapper>