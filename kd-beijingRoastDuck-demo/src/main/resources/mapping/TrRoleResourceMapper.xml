<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="kd.beijingRoastDuck.dao.TrRoleResourceDao">
	
	<delete id="deleteByPrimaryKey" parameterType="map">
		delete from tr_role_resource
		where 1 = 1
		<if test="resourceId != null">
			and resource_id = #{resourceId,jdbcType=VARCHAR}
		</if>
		<if test="roleId != null">
			and role_id = #{roleId,jdbcType=VARCHAR}
		</if>
	</delete>
	
	<insert id="insertSelective" parameterType="kd.entity.TrRoleResourceKey">
		<include refid="mapping.TrRoleResourceMapper.insertSelective" />
	</insert>
	
	<delete id="deleteByResourceIdStr" parameterType="java.lang.String">
		delete from tr_role_resource
		where find_in_set(resource_id, #{resourceIdStr})
	</delete>
	
</mapper>