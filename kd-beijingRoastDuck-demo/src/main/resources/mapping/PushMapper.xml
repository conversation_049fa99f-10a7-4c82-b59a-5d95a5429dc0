<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="kd.beijingRoastDuck.dao.PushDao">
	
	<insert id="insertSelective" parameterType="kd.entity.Push">
		<include refid="mapping.PushMapper.insertSelective" />
	</insert>
	
	<update id="updateByPrimaryKeySelective" parameterType="kd.entity.Push">
		<include refid="mapping.PushMapper.updateByPrimaryKeySelective" />
	</update>

	<select id="selectByPrimaryKey" resultMap="mapping.PushMapper.BaseResultMap" parameterType="java.lang.String">
		<include refid="mapping.PushMapper.selectByPrimaryKey"/>
	</select>
	
</mapper>