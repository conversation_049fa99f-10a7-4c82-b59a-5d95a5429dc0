<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="kd.beijingRoastDuck.dao.DictDao">

    <select id="selectByPrimaryKey" resultMap="mapping.DictMapper.BaseResultMap"
            parameterType="java.lang.String">
        <include refid="mapping.DictMapper.selectByPrimaryKey"/>
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        <include refid="mapping.DictMapper.deleteByPrimaryKey"/>
    </delete>

    <insert id="insertSelective" parameterType="kd.entity.Dict">
        <include refid="mapping.DictMapper.insertSelective"/>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="kd.entity.Dict">
        <include refid="mapping.DictMapper.updateByPrimaryKeySelective"/>
    </update>

    <select id="findAllList" resultMap="mapping.DictMapper.BaseResultMap"
            parameterType="java.util.Map">
        select
        <include refid="mapping.DictMapper.Base_Column_List"/>
        from t_dict a
        where 1=1
        <if test="dvalue != null and dvalue != ''">
            and a.dvalue like CONCAT('%', #{dvalue,jdbcType=VARCHAR} ,'%')
        </if>
        <if test="dlabel != null and dlabel != ''">
            and a.dlabel like CONCAT('%', #{dlabel,jdbcType=VARCHAR} ,'%')
        </if>
        <if test="type != null and type != ''">
            and a.type = #{type,jdbcType=VARCHAR}
        </if>
        order by a.type, a.dvalue
    </select>

    <insert id="addDictBatch" parameterType="java.util.List">
        INSERT INTO t_dict (
        id,
        type,
        dvalue,
        dlabel
        )
        VALUES
        <foreach collection="dicts" item="dict" separator=",">(
            #{dict.id }, #{dict.type },#{dict.dvalue },#{dict.dlabel  })
        </foreach>
    </insert>

    <select id="selectByDlabel" resultMap="mapping.DictMapper.BaseResultMap"
            parameterType="java.lang.String">
        select
        <include refid="mapping.DictMapper.Base_Column_List"/>
        from t_dict a
        where 1=1 and a.dlabel=  #{dlabel,jdbcType=VARCHAR}
    </select>

    <select id="selectByTypeAndDvalue" resultMap="mapping.DictMapper.BaseResultMap"
            parameterType="java.lang.String">
        select
        <include refid="mapping.DictMapper.Base_Column_List"/>
        from t_dict a
        where 1=1 and a.type=  #{type,jdbcType=VARCHAR} and a.dvalue =  #{policyNo,jdbcType=VARCHAR}
    </select>
</mapper>