<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="kd.beijingRoastDuck.dao.ClaimCaseSubjectDao">
	
	<insert id="insertSelective" parameterType="kd.entity.ClaimCaseSubject">
		<include refid="mapping.ClaimCaseSubjectMapper.insertSelective" />
	</insert>
	
	<update id="updateByPrimaryKeySelective" parameterType="kd.entity.ClaimCaseSubject">
		<include refid="mapping.ClaimCaseSubjectMapper.updateByPrimaryKeySelective" />
	</update>
    <update id="updateDealPersonByCaseId" parameterType="java.util.Map">
		UPDATE t_claim_case_subject SET operator=if(operator is null,#{dealName},concat(operator,"--->",#{dealName}))
		WHERE claim_case_id=#{caseId,jdbcType=VARCHAR}
	</update>

    <select id="selectByPrimaryKey" resultMap="mapping.ClaimCaseSubjectMapper.BaseResultMap" parameterType="java.lang.String">
		<include refid="mapping.ClaimCaseSubjectMapper.selectByPrimaryKey" />
	</select>

	<delete id="deleteByClaimCaseId" parameterType="java.lang.String">
		delete from t_claim_case_subject where claim_case_id = #{claimCaseId,jdbcType=VARCHAR}
	</delete>
    <delete id="deleteByPrimaryKey">
		<include refid="mapping.ClaimCaseSubjectMapper.deleteByPrimaryKey"/>
	</delete>

</mapper>