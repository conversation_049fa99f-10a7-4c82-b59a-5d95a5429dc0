<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="kd.beijingRoastDuck.dao.AssessmentReportTaskDao">
	
	<insert id="insertSelective" parameterType="kd.entity.AssessmentReportTask">
		<include refid="mapping.AssessmentReportTaskMapper.insertSelective"/>
	</insert>
	
	<update id="updateByPrimaryKeySelective" parameterType="kd.entity.AssessmentReportTask">
		<include refid="mapping.AssessmentReportTaskMapper.updateByPrimaryKeySelective" />
	</update>

	<select id="selectByPrimaryKey" resultMap="mapping.AssessmentReportTaskMapper.BaseResultMap" parameterType="java.lang.String">
		<include refid="mapping.AssessmentReportTaskMapper.selectByPrimaryKey"/>
	</select>
</mapper>