package kd.injury.app.util;

import kd.common.tool.AliOssToolV3;
import kd.common.tool.JsonBizTool;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

public class AliossTool {

    private static final Logger logger = LoggerFactory.getLogger(AliossTool.class.getName());

    /**
     * 文件上传结果类
     */
    public static class UploadResult {
        private boolean success;
        private String objectId;
        private String fileName;
        private long fileSize;
        private String contentType;
        private String errorMessage;

        public UploadResult() {}

        public UploadResult(boolean success, String objectId, String fileName, long fileSize, String contentType) {
            this.success = success;
            this.objectId = objectId;
            this.fileName = fileName;
            this.fileSize = fileSize;
            this.contentType = contentType;
        }

        public static UploadResult success(String objectId, String fileName, long fileSize, String contentType) {
            return new UploadResult(true, objectId, fileName, fileSize, contentType);
        }

        public static UploadResult failure(String errorMessage) {
            UploadResult result = new UploadResult();
            result.success = false;
            result.errorMessage = errorMessage;
            return result;
        }

        // Getters and Setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }

        public String getObjectId() { return objectId; }
        public void setObjectId(String objectId) { this.objectId = objectId; }

        public String getFileName() { return fileName; }
        public void setFileName(String fileName) { this.fileName = fileName; }

        public long getFileSize() { return fileSize; }
        public void setFileSize(long fileSize) { this.fileSize = fileSize; }

        public String getContentType() { return contentType; }
        public void setContentType(String contentType) { this.contentType = contentType; }

        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }

        @Override
        public String toString() {
            return "UploadResult{" +
                    "success=" + success +
                    ", objectId='" + objectId + '\'' +
                    ", fileName='" + fileName + '\'' +
                    ", fileSize=" + fileSize +
                    ", contentType='" + contentType + '\'' +
                    ", errorMessage='" + errorMessage + '\'' +
                    '}';
        }
    }
    /**
     * URL上传文件 返回objectId (保持向后兼容)
     * @param path 链接地址（url）
     * @param objectId oss文件地址
     * @return objectId
     * @deprecated 建议使用 uploadOssEnhanced 方法获取更详细的信息
     */
    @Deprecated
    public static String uploadOss(String path, String objectId) {
        UploadResult result = uploadOssEnhanced(path, objectId);
        if (result.isSuccess()) {
            return result.getObjectId();
        } else {
            return JsonBizTool.genJson("-1", result.getErrorMessage());
        }
    }

    /**
     * 增强版URL上传文件方法 - 支持大文件，返回详细信息
     * @param path 链接地址（url）
     * @param objectId oss文件地址
     * @return UploadResult 包含文件名、大小、类型等详细信息
     */
    public static UploadResult uploadOssEnhanced(String path, String objectId) {
        InputStream inputStream = null;
        HttpURLConnection connection = null;

        try {
            logger.info("开始从URL下载文件: {}", path);

            // 处理URL编码问题（特别是阿里云OSS签名URL）
            String processedUrl = processUrl(path);

            // 创建连接
            URL url = new URL(processedUrl);
            connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(30000); // 30秒连接超时
            connection.setReadTimeout(60000);    // 60秒读取超时
            connection.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");

            // 处理重定向
            int responseCode = connection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_MOVED_TEMP ||
                responseCode == HttpURLConnection.HTTP_MOVED_PERM ||
                responseCode == HttpURLConnection.HTTP_SEE_OTHER) {

                String location = connection.getHeaderField("Location");
                if (location != null) {
                    logger.info("检测到重定向，新URL: {}", location);
                    connection.disconnect();

                    url = new URL(location);
                    connection = (HttpURLConnection) url.openConnection();
                    connection.setRequestMethod("GET");
                    connection.setConnectTimeout(30000);
                    connection.setReadTimeout(60000);
                    connection.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
                    responseCode = connection.getResponseCode();
                }
            }

            // 检查响应状态
            if (responseCode != HttpURLConnection.HTTP_OK) {
                String errorMsg = "HTTP请求失败，响应码: " + responseCode + ", URL: " + path;
                logger.error(errorMsg);
                return UploadResult.failure(errorMsg);
            }

            // 获取文件信息
            String contentType = connection.getContentType();
            long contentLength = connection.getContentLengthLong();
            String fileName = extractFileNameFromUrl(path, contentType);

            logger.info("文件信息 - 名称: {}, 大小: {}, 类型: {}", fileName, contentLength, contentType);

            // 获取输入流
            inputStream = connection.getInputStream();

            // 直接使用流上传，避免大文件内存问题
            AliOssToolV3.uploadByStreamOuter(AliOssToolV3.PRIVATE_BUCKET_ID, objectId, inputStream, contentType);

            logger.info("文件上传成功，ObjectId: {}", objectId);

            return UploadResult.success(objectId, fileName, contentLength, contentType);

        } catch (Exception e) {
            String errorMsg = "文件下载或上传失败: " + e.getMessage() + ", URL: " + path;
            logger.error(errorMsg, e);
            return UploadResult.failure(errorMsg);

        } finally {
            // 关闭资源
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    logger.warn("关闭输入流失败", e);
                }
            }
            if (connection != null) {
                try {
                    connection.disconnect();
                } catch (Exception e) {
                    logger.warn("断开连接失败", e);
                }
            }
        }
    }

    /**
     * 上传本地文件到oss
     * @param path 文件路径
     * @param objectId oss地址
     * @return objectId
     */
    public static String uploadLocal(String path, String objectId) {
        File file = new File(path);
        byte[] bytes = getFileByteArray(file);
        AliOssToolV3.uploadByByteArrayOuter(AliOssToolV3.PRIVATE_BUCKET_ID, objectId, bytes, "application/pdf");
        return objectId;
    }

    /**
     * 获取文件的 bytes
     * @param file
     * @return
     */
    public static byte[] getFileByteArray(File file) {
        if (file.length() > Integer.MAX_VALUE) {
            logger.info("===>> file too big... ");
            return null;
        }
        byte[] buf = null;
        try (FileInputStream in = new FileInputStream(file);){
            buf = new byte[(int) file.length()];
            int offset = 0;
            int numRead = 0;
            while (offset < buf.length && (numRead = in.read(buf,offset, buf.length - offset))>=0 ) {
                offset += numRead;
            }

            if (offset != buf.length) {
                throw new IOException("文件未全部读取" + file.getName());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return buf;
    }
}
