package kd.injury.app.util;

import kd.common.tool.AliOssToolV3;
import kd.common.tool.JsonBizTool;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

public class AliossTool {

    private static final Logger logger = LoggerFactory.getLogger(AliossTool.class.getName());
    /**
     * URL上传文件 返回包含文件信息的JSON
     * @param path 链接地址（url）
     * @param objectId oss文件地址
     * @return JSON字符串，包含objectId、fileName、fileSize、fileType等信息
     */
    public static String uploadOss(String path, String objectId) {
        InputStream is = null;
        ByteArrayOutputStream os = null;
        HttpURLConnection con = null;
        byte[] buff = null;
        Map<String, Object> resultMap = new HashMap<>();

        try {
            URL imgUrl = new URL(path);
            con = (HttpURLConnection) imgUrl.openConnection();
            if (302 == con.getResponseCode()) {
                String location = con.getHeaderField("location");
                URL url = new URL(location);
                con = (HttpURLConnection) url.openConnection();
            }
            is = con.getInputStream();
            os = new ByteArrayOutputStream();
            buff = new byte[4096];
            int len;
            while ((len = is.read(buff)) != -1) {
                os.write(buff, 0, len);
            }

            byte[] fileBytes = os.toByteArray();
            String contentType = con.getContentType();

            // 从URL中提取文件名
            String fileName = extractFileNameFromUrl(path);

            // 获取文件大小
            long fileSize = fileBytes.length;

            // 上传oss
            AliOssToolV3.uploadByByteArrayOuter(AliOssToolV3.PRIVATE_BUCKET_ID, objectId, fileBytes, contentType);

            // 构建返回结果
            resultMap.put("objectId", objectId);
            resultMap.put("fileName", fileName);
            resultMap.put("fileSize", fileSize);
            resultMap.put("fileType", contentType);
            resultMap.put("success", true);

            return JsonBizTool.genJson(resultMap);

        } catch (IOException e) {
            e.printStackTrace();
            logger.error("影像下载出错,url:" + path, e);
            return JsonBizTool.genJson("-1", "影像下载出错,url:" + path);
        } finally {
            if (is != null) {
                try {
                    // 关闭inputStream流
                    is.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (os != null) {
                try {
                    //关闭outputStream流
                    os.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (con != null) {
                try {
                    con.disconnect();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 上传本地文件到oss
     * @param path 文件路径
     * @param objectId oss地址
     * @return objectId
     */
    public static String uploadLocal(String path, String objectId) {
        File file = new File(path);
        byte[] bytes = getFileByteArray(file);
        AliOssToolV3.uploadByByteArrayOuter(AliOssToolV3.PRIVATE_BUCKET_ID, objectId, bytes, "application/pdf");
        return objectId;
    }

    /**
     * 获取文件的 bytes
     * @param file
     * @return
     */
    public static byte[] getFileByteArray(File file) {
        if (file.length() > Integer.MAX_VALUE) {
            logger.info("===>> file too big... ");
            return null;
        }
        byte[] buf = null;
        try (FileInputStream in = new FileInputStream(file);){
            buf = new byte[(int) file.length()];
            int offset = 0;
            int numRead = 0;
            while (offset < buf.length && (numRead = in.read(buf,offset, buf.length - offset))>=0 ) {
                offset += numRead;
            }

            if (offset != buf.length) {
                throw new IOException("文件未全部读取" + file.getName());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return buf;
    }

    /**
     * 从URL中提取文件名
     * @param url URL地址
     * @return 文件名
     */
    private static String extractFileNameFromUrl(String url) {
        try {
            // 解码URL
            String decodedUrl = URLDecoder.decode(url, StandardCharsets.UTF_8.toString());

            // 移除查询参数
            if (decodedUrl.contains("?")) {
                decodedUrl = decodedUrl.substring(0, decodedUrl.indexOf("?"));
            }

            // 提取文件名
            String fileName = decodedUrl.substring(decodedUrl.lastIndexOf("/") + 1);

            // 如果没有扩展名，尝试从原始URL中获取
            if (!fileName.contains(".")) {
                // 从原始URL路径中查找可能的文件扩展名
                if (url.contains(".ext")) {
                    fileName = fileName + ".ext";
                } else if (url.toLowerCase().contains("jpg") || url.toLowerCase().contains("jpeg")) {
                    fileName = fileName + ".jpg";
                } else if (url.toLowerCase().contains("png")) {
                    fileName = fileName + ".png";
                } else if (url.toLowerCase().contains("pdf")) {
                    fileName = fileName + ".pdf";
                } else {
                    fileName = fileName + ".file";
                }
            }

            return fileName.isEmpty() ? "unknown_file" : fileName;
        } catch (Exception e) {
            logger.error("提取文件名失败: " + url, e);
            return "unknown_file";
        }
    }
}
